# Focused Offline Display Implementation Plan

## Overview

Simple implementation plan for adding offline display functionality to StoreTrack. Focus only on file types downloaded by `_downloadFilesForOfflineUse()` in `sync_utils.dart`. Use KISS principle - create 2 reusable widgets and update specific usage points.

## Current State

###  What Works
- **Download System**: `_downloadFilesForOfflineUse()` downloads 6 file types:
  - Client logos via `_downloadClientLogos()`
  - Photo URLs via `_downloadPhotoUrls()`
  - Signature URLs via `_downloadSignatureUrls()` 
  - Question photos via `_downloadQuestionPhotos()`
  - User photos via `_downloadUserPhotos()`
  - Document files via `_downloadDocumentFiles()`
- **Storage**: `DownloadedFileModel` tracks local paths and metadata
- **Utilities**: `ImageStorageUtils.isLocalFile()` detects local vs network paths

### L What's Missing
- **No offline integration**: All widgets use network URLs despite downloaded files
- **MeasurementThumbnailWidget**: Needs to be replaced with reusable solution
- **Document viewing**: Always uses `WebBrowserRoute`, no offline support

## Simple Solution

### 2 Reusable Widgets

#### 1. OfflineImageWidget
```dart
// lib/shared/widgets/offline_image_widget.dart
class OfflineImageWidget extends StatelessWidget {
  final String url;
  final double? width, height;
  final BoxFit fit;
  
  // 1. Check DownloadedFileModel for local path
  // 2. If local file exists -> Image.file()
  // 3. Else -> CachedNetworkImage()
}
```

#### 2. OfflineDocumentWidget  
```dart
// lib/shared/widgets/offline_document_widget.dart
class OfflineDocumentWidget extends StatelessWidget {
  final String url;
  final String title;
  
  // 1. Check DownloadedFileModel for local path
  // 2. Try open_file.open() for default app
  // 3. Fallback to WebBrowserRoute if fails
}
```

## Implementation Plan

### Day 1: Create OfflineImageWidget
**File**: `lib/shared/widgets/offline_image_widget.dart`

**Features**:
- Simple helper method to check `DownloadedFileModel`
- `Image.file()` for local files, `CachedNetworkImage` for network
- Configurable size, fit, placeholder, error handling
- Copy pattern from `MeasurementThumbnailWidget._buildImageWidget()`

### Day 2: Create OfflineDocumentWidget
**File**: `lib/shared/widgets/offline_document_widget.dart`

**Features**:
- Check for local document first
- Use `open_file` package to open in default app
- Fallback to `WebBrowserRoute` if opening fails
- Show appropriate error messages

**Dependencies**: Add `open_file: ^3.3.2` to `pubspec.yaml`

### Day 3: Replace Image Widgets
**Target only downloaded file types**:

1. **Replace MeasurementThumbnailWidget**:
   - Delete `lib/features/home/<USER>/widgets/measurement_widgets/measurement_thumbnail_widget.dart`
   - Find all usages and replace with `OfflineImageWidget`

2. **Update Client Logos** (`clientLogoUrl`):
   - Search for `clientLogoUrl` usage
   - Replace `Image.network()` with `OfflineImageWidget()`

3. **Update User Photos** (`profileImageUrl`):
   - Search for `profileImageUrl` usage  
   - Replace `Image.network()` with `OfflineImageWidget()`

4. **Update Photo URLs** (`photoUrl`):
   - Search for photo URL displays
   - Replace with `OfflineImageWidget()`

5. **Update Signature URLs** (`signatureUrl`):
   - Search for signature displays
   - Replace with `OfflineImageWidget()`

6. **Update Question Photos**:
   - Find question photo displays in forms
   - Replace with `OfflineImageWidget()`

### Day 4: Update Document Display & Testing
1. **Update DocumentsView**:
   - File: `lib/features/home/<USER>/widgets/task_details/documents_view.dart`
   - Replace `WebBrowserRoute` calls with `OfflineDocumentWidget`

2. **Update DocumentsContentWidget**:
   - File: `lib/features/home/<USER>/widgets/task_details/documents_content_widget.dart`
   - Replace `WebBrowserRoute` calls with `OfflineDocumentWidget`

3. **Testing**:
   - Test offline image display for all 5 image types
   - Test document opening with default apps
   - Test fallback behavior when files not cached

## Technical Details

### Simple Path Resolution
```dart
// Helper method in OfflineImageWidget
Future<String> _getDisplayPath(String url) async {
  try {
    final realm = sl<RealmDatabase>().realm;
    final cached = realm.find<DownloadedFileModel>(url);
    
    if (cached?.isDownloaded == true) {
      final file = File(cached!.localPath);
      if (await file.exists()) {
        // Update last access
        realm.write(() => cached.lastAccessDate = DateTime.now());
        return cached.localPath;
      }
    }
    
    return url; // Fallback to network
  } catch (e) {
    return url; // Fallback on error
  }
}
```

### Document Opening
```dart
// In OfflineDocumentWidget
Future<void> _openDocument(String path) async {
  try {
    final result = await OpenFile.open(path);
    if (result.type != ResultType.done) {
      // Fallback to WebBrowserRoute
      context.router.push(WebBrowserRoute(url: widget.url, title: widget.title));
    }
  } catch (e) {
    // Fallback to WebBrowserRoute
    context.router.push(WebBrowserRoute(url: widget.url, title: widget.title));
  }
}
```

## File Structure
```
lib/
   shared/
      widgets/
          offline_image_widget.dart       # New
          offline_document_widget.dart    # New
   features/home/<USER>/widgets/
      task_details/
         documents_view.dart             # Update
         documents_content_widget.dart   # Update
      measurement_widgets/
          measurement_thumbnail_widget.dart # DELETE
   di/
       service_locator.dart                # No changes needed
```

## Dependencies

### Add to pubspec.yaml
```yaml
dependencies:
  open_file: ^3.3.2  # For opening files in default apps
  
# Already available:
# cached_network_image: # Network image caching
# realm: # Database access
# path_provider: # File system access
```

## Success Criteria

1. **All downloaded image types display offline**: Client logos, user photos, photo URLs, signature URLs, question photos
2. **Documents open in default apps**: PDFs in PDF viewers, images in gallery apps, etc.
3. **Seamless fallback**: Network loading when offline files unavailable
4. **Performance**: No regression in loading times
5. **Clean code**: Reusable widgets reduce duplication

## Testing Checklist

- [ ] Client logos display offline
- [ ] User profile photos display offline  
- [ ] Photo URLs display offline
- [ ] Signature URLs display offline
- [ ] Question photos display offline
- [ ] Documents open in default apps
- [ ] Fallback to WebBrowser when default app fails
- [ ] Network fallback when files not cached
- [ ] No crashes when files missing
- [ ] Performance similar to current implementation

## Timeline: 4 Days Total

- **Day 1**: `OfflineImageWidget` creation
- **Day 2**: `OfflineDocumentWidget` creation + `open_file` integration  
- **Day 3**: Replace all image widgets (6 types including MeasurementThumbnailWidget)
- **Day 4**: Update document widgets + comprehensive testing

This focused approach delivers complete offline functionality for all downloaded file types while maintaining simplicity and performance.