package com.au.storetrack.Fragments.Task;

import android.app.AlertDialog;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;

import com.au.storetrack.Activities.ActivityBase;
import com.au.storetrack.Fragments.Base.FragmentBase;
import com.au.storetrack.Model.DataModel.FormModel;
import com.au.storetrack.Model.DataModel.QuestionAnswerModel;
import com.au.storetrack.Model.DataModel.QuestionMeasurementModel;
import com.au.storetrack.Model.DataModel.QuestionModel;
import com.au.storetrack.Model.DataModel.QuestionPartModel;
import com.au.storetrack.Model.DataModel.SignatureFolderModel;
import com.au.storetrack.Model.DataModel.SignatureModel;
import com.au.storetrack.Model.DataModel.TaskDetailModel;
import com.au.storetrack.Model.EventModel.APIEvent;
import com.au.storetrack.Model.EventModel.PermissionEvent;
import com.au.storetrack.R;
import com.au.storetrack.Utilities.CommonFunction;
import com.au.storetrack.Utilities.Constant;
import com.au.storetrack.Utilities.ControlParser;
import com.au.storetrack.Utilities.Database.DatabaseManager;
import com.au.storetrack.Utilities.Database.DatabaseRunnable;
import com.au.storetrack.Utilities.Network.API;
import com.au.storetrack.Utilities.Storage.FileProvider;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.io.File;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

import io.realm.Realm;
import io.realm.RealmList;
import se.warting.signatureview.views.SignaturePad;
import se.warting.signatureview.views.SignedListener;

/**
 * Created by Augustin on 16/5/2022.
 */

public class FormQuestionSignatureFragment extends FragmentBase {


    public static final String FORM_QUESTION_PART_DETAIL_TITLE_KEY = "FORM_QUESTION_PART_DETAIL_TITLE_KEY";

    private String taskID, formID, signatureFolderID;
    private QuestionModel questionModel;
    private FormModel formModel;
    private RealmList<QuestionAnswerModel> questionAnswerModel;
    private boolean isQuestionAnswered = false;
    private LinearLayout questionSignatureContainer_linearLayout;
    private SignaturePad signaturePad;
    private Button clearButton, noSignature;
    private ImageView rightControl;
    private ImageView signatureImageView;
    private SignatureModel signatureModel;
    private SignatureFolderModel signatureFolderModel;
    private EditText signature_signedBy_editText;
    private Bitmap chosenBitmap = null;
    private ProgressBar signatureDetailLoadIndicator_progressBar;

    //for debug
    private final String TAG = "FormQuestionSignatureFragment";

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        EventBus.getDefault().register(this);

        Bundle receiveBundle = getArguments();
        checkWritePermission();

        View v = inflater.inflate(R.layout.form_question_signature_fragment, container, false);
        questionSignatureContainer_linearLayout = ControlParser.parseControl(v, R.id.questionSignatureContainer_linearLayout);

        //get starting taskid anf formid
        //get form model using starting IDs
        if (Objects.requireNonNull(receiveBundle).containsKey(TaskDetailModel.TASK_ID_KEY) &&
                (receiveBundle.containsKey(FormModel.FORM_ID_KEY)) &&
                (receiveBundle.containsKey(QuestionModel.QUESTION_ID_KEY))) {

            taskID = receiveBundle.getString(TaskDetailModel.TASK_ID_KEY);
            formID = receiveBundle.getString(FormModel.FORM_ID_KEY);
            signatureFolderID = receiveBundle.getString(FormModel.FORM_ID_KEY);
            String questionID = receiveBundle.getString(QuestionModel.QUESTION_ID_KEY);

            formModel = DatabaseManager.getInstance(getContext()).getFormModelFromTaskIDWithFormID(ActivityBase.mRealm, taskID, formID, true);
            questionModel = DatabaseManager.getInstance(getContext()).getFormQuestionDetailModelWithQuestionID(ActivityBase.mRealm, formModel, questionID, true);
            questionAnswerModel = DatabaseManager.getInstance(getContext()).getQuestionAnswerModelForQuestionFromTaskIDWithFormID(ActivityBase.mRealm, taskID, formID, questionID, true);
            isQuestionAnswered = DatabaseManager.getInstance(getContext()).isQuestionAnsweredSubmitForm(taskID, formModel, questionModel);

        }

        //error if task detail request == null
        if ((taskID == null) || (formID == null) || (formModel == null)) {
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("Fail to initialise");
            builder.setMessage("Please try again later.");
            builder.setCancelable(false);
            builder.setPositiveButton(R.string.OK, (dialog, id) -> popFragment());

            builder.setOnDismissListener(dialog -> CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog));
            AlertDialog alertDialog = builder.create();
            alertDialog.show();

            CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);
        } else {

            if ((questionAnswerModel == null) || (questionAnswerModel.size() < 1)) {
                AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
                builder.setTitle("Question Parts Not Answered");
                builder.setMessage("Please complete all questions parts for this question and try again. ");
                builder.setCancelable(false);
                builder.setPositiveButton(R.string.OK, (dialog, id) -> popFragment());
                builder.setOnDismissListener(dialog -> CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog));
                AlertDialog alertDialog = builder.create();
                alertDialog.show();

                CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);

            } else {

                signatureFolderModel = DatabaseManager.getInstance(getContext()).getSignatureFolderModelFromTaskIDWithSignatureFolderID(ActivityBase.mRealm, taskID, signatureFolderID, true);

                if (signatureFolderModel != null) {
                    signatureModel = DatabaseManager.getInstance(getContext()).getSignatureModel_FromSignatureFolderModel(ActivityBase.mRealm, taskID, signatureFolderID, questionModel.getQuestionID(), true);
                }

                checkWritePermission();
                prepareInterfaces(v);
                refreshDisplayedInformation();
            }
        }

        return v;
    }

    private void refreshDisplayedInformation() {

        signatureDetailLoadIndicator_progressBar.setVisibility(View.GONE);
        signatureImageView.setVisibility(View.GONE);

        if (signatureModel != null) {
            //Signature Exists
            signatureDetailLoadIndicator_progressBar.setVisibility(View.GONE);
            signatureImageView.setVisibility(View.GONE);

            if ((!CommonFunction.isEmptyStringField(signatureModel.getSignatureLocalPath())) &&
                    (CommonFunction.isFileExist(signatureModel.getSignatureLocalPath()))
                    && (!signatureModel.isCannotUploadMandatory())) {
                // Local image file
                Bitmap localBitmap = CommonFunction.decodeSampledBitmapFromUri(getContext(), Uri.parse(FileProvider.contentUri + signatureModel.getSignatureLocalPath()));

                signatureImageView.setImageBitmap(localBitmap);
                signatureImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
                signatureDetailLoadIndicator_progressBar.setVisibility(View.GONE);
                signatureImageView.setVisibility(View.VISIBLE);
                signature_signedBy_editText.setText(signatureModel.getSignedBy());
               // CommonFunction.print("Signature Folders : Savings : UPLOADS : SIGNATURE : SIG B " + signatureModel.getSignatureId() + " : " + signatureModel.getSignatureLocalPath() + " : " + localBitmap.getByteCount() + " : " + signatureModel.isCannotUploadMandatory());

            } else if (
                    ((CommonFunction.isEmptyStringField(signatureModel.getSignatureLocalPath())) &&
                            !signatureModel.isSignatureIsDeleted() ||
                            ((!CommonFunction.isEmptyStringField(signatureModel.getSignatureLocalPath()))
                                    && !signatureModel.isSignatureIsDeleted()
                                    && !signatureModel.isCannotUploadMandatory()
                                    && (!signatureModel.getSignatureLocalPath().equalsIgnoreCase(Constant.INVALID_RESOURCE_URL_MARKER_STRING))
                                    && (!CommonFunction.isFileExist(signatureModel.getSignatureLocalPath()))))
                            && (!CommonFunction.isEmptyStringField(signatureModel.getSignatureURL()))
            ) {
                // Download missing file (photo url exists but no local file)

                String fileName = CommonFunction.getFileNameForLocalPathFromURL(signatureModel.getSignatureURL());

               // CommonFunction.print("Signature Folders : Saving : Upload : SIGNATURE : SIG C " + signatureModel.getSignatureId() + " : " + signatureModel.getSignatureLocalPath() + " : " + fileName + " : " + signatureModel.getSignatureURL());

                if (!CommonFunction.isEmptyStringField(fileName) && (!signatureModel.isCannotUploadMandatory())) {
                    String localFilePath = CommonFunction.generateExternalFilePathAccordingToType(fileName);
                   // CommonFunction.print("Signature Folders : Saving : Upload : SIGNATURE : SIG C 2 " + signatureModel.getSignatureId() + " : " + signatureModel.getSignatureLocalPath() + " : " + localFilePath);

                    if (!CommonFunction.isEmptyStringField(localFilePath)) {
                        if (API.getInstance(getContext()).hasNetworkConnection()) {
                            HashMap<String, String> fileMap = new HashMap<>();
                            fileMap.put(API.API_DOWNLOAD_FILE_CLASS_KEY, SignatureModel.class.toString());
                            fileMap.put(API.API_DOWNLOAD_FILE_URL_KEY, signatureModel.getSignatureURL());
                            fileMap.put(API.API_DOWNLOAD_FILE_LOCAL_PATH_KEY, localFilePath);

                            API.getInstance(getContext()).downloadFile(fileMap, false);
                            //mo, when download file is successful, you come to this function again to show photo, and you will come to first if clause.
                        } else {
                            showTaskLastUpdateInformation(false);
                            API.getInstance(getContext()).showNoConnectionErrorMessage();
                        }
                    }
                } else if ((!CommonFunction.isEmptyStringField(signatureModel.getSignatureLocalPath())) && (!CommonFunction.isFileExist(signatureModel.getSignatureLocalPath())) || signatureModel.isCannotUploadMandatory()) {
                    //photo file supposed exist but not >> show 'no image found' iamge.
                    CommonFunction.print("Signature Folders : Saving : Upload : SIGNATURE : SIG C 2 " + signatureModel.getSignatureId() + " : " + signatureModel.getSignatureLocalPath() + " : " + signatureModel.isCannotUploadMandatory());

                    signatureImageView.setImageResource(R.drawable.no_signature);
                    signatureImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
                    signatureDetailLoadIndicator_progressBar.setVisibility(View.GONE);
                    signatureImageView.setVisibility(View.VISIBLE);
                    noSignature.setSelected(true);
                    noSignature.setPressed(true);

                } else {
                    //no photo >> hide photo panel
                    signatureDetailLoadIndicator_progressBar.setVisibility(View.GONE);
                    signatureImageView.setVisibility(View.GONE);
                    signatureModel.setSignatureIsDeleted(true);
                }
            }

            if (signatureModel.isCannotUploadMandatory()) {
//                CommonFunction.print("Signature Folders : Saving : Upload : SIGNATURE : SIG C 33 " + signatureModel.getSignatureId() + " : " + signatureModel.getSignatureLocalPath() + " : " + signatureModel.isCannotUploadMandatory());

                signatureImageView.setImageResource(R.drawable.no_signature);
                signatureImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
                signatureImageView.setVisibility(View.VISIBLE);
                noSignature.setSelected(true);
            }
        }

    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onDestroyView() {
        try {

            CommonFunction.hideSoftKeyboard(requireActivity());
            EventBus.getDefault().unregister(this);
        } catch (Exception e) {
            CommonFunction.print("error: " + e.getMessage());
        }

        super.onDestroyView();
    }

    @Subscribe
    public void onAPIEventReceived(APIEvent event) {
        switch (event.getApiRequestTag()) {
            case API.API_DOWNLOAD_FILE_REQUEST_TAG:
                if (event.getApiResponseType().equalsIgnoreCase(API.API_SUCCESS_RESPONSE_TYPE)) {
                    refreshDisplayedInformation();
                }

                break;

            default:
                break;
        }
    }

    @Subscribe
    public void onPermissionEventReceived(PermissionEvent event) {

        switch (event.getSenderActivityTag()) {

            case Constant.CHECK_WRITE_PERMISSION_WHEN_ADD_UPDATE_SIGNATURE_ACTIVITY_TAG:
                if (event.getPermissionResult() == PackageManager.PERMISSION_GRANTED) {
                    CommonFunction.getInstance(getContext()).hideProgressIndicator();
                }

                break;

            default:
                break;
        }
    }

    private void prepareInterfaces(View v) {
        loadQuestionMeasurement(questionModel);
        signature_signedBy_editText = v.findViewById(R.id.signed_by);
        signatureImageView = ControlParser.parseControl(v, R.id.signature_ImageView);
        signatureImageView.setVisibility(View.GONE); //signature imageview is not visible
        rightControl.setVisibility(View.GONE);
        signaturePad = ControlParser.parseControl(v, R.id.signaturePad);
        clearButton = ControlParser.parseControl(v, R.id.clearButton);
        noSignature = ControlParser.parseControl(v, R.id.NoSignatureButton);
        signatureDetailLoadIndicator_progressBar = ControlParser.parseControl(v, R.id.signatureDetailLoadIndicator_progressBar);

        signaturePad.setOnSignedListener(new SignedListener() {
            @Override
            public void onStartSigning() {
                CommonFunction.hideSoftKeyboard(requireActivity());
            }

            @Override
            public void onSigned() {
                CommonFunction.hideSoftKeyboard(requireActivity());
                rightControl.setVisibility(View.VISIBLE);
                rightControl.setEnabled(true);
                clearButton.setEnabled(true);
                noSignature.setEnabled(true);
            }

            @Override
            public void onClear() {
            }
        });

        signature_signedBy_editText.addTextChangedListener(new TextWatcher() {

            public void afterTextChanged(Editable s) {

                if (!signaturePad.isEmpty()) {
                    rightControl.setVisibility(View.VISIBLE);
                    rightControl.setEnabled(true);
                    clearButton.setEnabled(true);
                    noSignature.setEnabled(true);
                }
            }

            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }
        });


        clearButton.setOnClickListener(v1 -> {

            clearSignatureDetailImageView(false);

        });

        rightControl.setOnClickListener(v1 -> {
            saveButtonClicked();
        });


        noSignature.setOnClickListener(v1 -> {

            noSignature.setSelected(true);

            if (noSignature.isSelected()) {
//                CommonFunction.print("NO SIGNATURE CLICKED : SELECTED");
                clearSignatureDetailImageView(true);
            }
        });

    }

    private void checkWritePermission() {

        if (ActivityCompat.checkSelfPermission(requireContext(), android.Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                requestPermissions(new String[]{android.Manifest.permission.WRITE_EXTERNAL_STORAGE}, Constant.CHECK_WRITE_PERMISSION_WHEN_APP_LOAD_ACTIVITY_TAG);
            } else {
                showNoWriteExternalStoragePermissionErrorMessage();
            }
        }
    }

    public void showNoWriteExternalStoragePermissionErrorMessage() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("Permission Denied");
        builder.setCancelable(false);
        builder.setMessage(getString(R.string.NoWriteExternalStoragePermissionErrorMessage));
        builder.setPositiveButton(R.string.OK, (dialog, id) -> checkWritePermission());

        builder.show();
    }

    public void saveButtonClicked() {

        try {

            if (!noSignature.isSelected() && !clearButton.isSelected()) {

                chosenBitmap = signaturePad.getSignatureBitmap();
                signatureImageView.setVisibility(View.VISIBLE);
                signatureImageView.setImageBitmap(chosenBitmap);
                signatureImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);

                startInsertOrUpdateSignatureDetailDirect(chosenBitmap);

            } else {
                popFragment();
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }


    //    @SuppressLint("ResourceType")
    @Override
    public void setNavigationBarContent() {

        //set form title
        String fragmentTitle = requireArguments().getString(FORM_QUESTION_PART_DETAIL_TITLE_KEY);
        navigationBar.setNavigationBarTitleTextMenuPage((fragmentTitle != null ? fragmentTitle : questionModel.getQuestionDescription()));

        ImageView leftControl = new ImageView(rootActivity);
        leftControl.setImageResource(R.drawable.btn_back);
        leftControl.setOnClickListener(v -> backButtonPressed());
        rightControl = new ImageView(rootActivity);
        rightControl.setImageResource(R.drawable.icon_save);
        rightControl.setOnClickListener(v -> saveButtonClicked());
        navigationBar.addLeftNavigationBarControl(leftControl);
        navigationBar.addRightNavigationBarControl(rightControl);

    }

    public void backButtonPressed() {
        Bundle fragmentBundle = new Bundle();
        String fragmentNavigator = requireArguments().getString(FORM_QUESTION_PART_DETAIL_TITLE_KEY);
        fragmentBundle.putString(FormQuestionPartDetailFragment.TASK_FORM_NAVIGATION_KEY, fragmentNavigator);
        fragmentBundle.putString(TaskDetailModel.TASK_ID_KEY, taskID);
        fragmentBundle.putString(FormModel.FORM_ID_KEY, formModel.getFormID());
        fragmentBundle.putString(QuestionModel.QUESTION_ID_KEY, questionModel.getQuestionID());

        if(questionModel.isQuestionMulti()){
            replaceFragment(new FormQuestionPartDetailFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_LEFT_ANIMATION_KEY);

        }else{
            replaceFragment(new SubheaderFragment(), fragmentBundle, FragmentBase.SLIDE_IN_FROM_LEFT_ANIMATION_KEY);

        }

    }


    private void loadQuestionMeasurement(QuestionModel questionModel) {

        questionSignatureContainer_linearLayout.removeAllViews();

        for (QuestionPartModel questionPartModel : questionModel.getQuestionParts()) {
            if (!questionModel.isQuestionMulti()) {
                populateQuestionParts(questionSignatureContainer_linearLayout, questionPartModel);
            } else {

                if (questionPartModel.getQuestionPartMultiID().contains("-")) {
                    populateQuestionParts(questionSignatureContainer_linearLayout, questionPartModel);
                   System.out.println("Question Part:" + questionPartModel.getQuestionPartDescription());

                }
            }
        }
    }

    private ViewGroup populateQuestionParts(ViewGroup parentView, QuestionPartModel questionPartModel) {


        View questionPartHeaderContainer_linearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.question_part_header, parentView, false);
        LinearLayout questionSignatureResultsContainer_linearLayout = ControlParser.parseControl(questionPartHeaderContainer_linearLayout, R.id.questionResultsContainer_linearLayout);
        TextView questionPartTextView = ControlParser.parseControl(questionPartHeaderContainer_linearLayout, R.id.question_part_text_textview);
        questionPartTextView.setText(questionPartModel.getQuestionPartDescription());

        questionSignatureResultsContainer_linearLayout.removeAllViews();

        for (QuestionAnswerModel qa : questionAnswerModel) {

            if (qa.getQuestionAnswerQuestionPartMultiID().equalsIgnoreCase(questionPartModel.getQuestionPartMultiID())) {
                for (QuestionMeasurementModel qm : questionModel.getQuestionMeasurements()) {
                    if (qm.getQuestionMeasurementID().equalsIgnoreCase(qa.getQuestionAnswerMeasurementID())) {
                        populateQuestionMeasurements(questionSignatureResultsContainer_linearLayout, qm, qa);
                    }
                }
            }
        }

        parentView.addView(questionPartHeaderContainer_linearLayout);
        return (ViewGroup) questionPartHeaderContainer_linearLayout;

    }

    private ViewGroup populateQuestionMeasurements(ViewGroup parentView, QuestionMeasurementModel qm, QuestionAnswerModel questionPartAnswers) {
        View questionResultsContainer_linearLayout = LayoutInflater.from(rootActivity).inflate(R.layout.signature_results, parentView, false);

        TextView measurementTextView = ControlParser.parseControl(questionResultsContainer_linearLayout, R.id.measurement_textview);
        TextView responseTextView = ControlParser.parseControl(questionResultsContainer_linearLayout, R.id.response_textview);
        measurementTextView.setText(qm.getQuestionMeasurementDescription());
        responseTextView.setText(questionPartAnswers.getQuestionAnswerMeasurementTextResult());
        parentView.addView(questionResultsContainer_linearLayout);

        return (ViewGroup) questionResultsContainer_linearLayout;
    }

    private void clearSignatureDetailImageView(boolean noSignatureVal) {

        signaturePad.clear();
        signatureImageView.setVisibility(View.GONE);
        rightControl.setVisibility(View.VISIBLE);
        signature_signedBy_editText.setText("");

        if (chosenBitmap != null) {
            chosenBitmap.recycle();
            CommonFunction.recycleImageViewBitmap(signatureImageView);
            chosenBitmap = null;
        }

        if (noSignatureVal) {
            clearButton.setSelected(false);
            noSignature.setSelected(true);
            confirmNoSignature();
        } else {
            noSignature.setSelected(false);
            clearButton.setSelected(true);
            confirmDeleted();
        }

    }

    private void confirmDeleted() {

        if (signatureModel != null) {
            //--------------------------------
            //Delete clicked >> DELETE SIGNATURE
            //--------------------------------

            DatabaseManager.getInstance(getContext()).processRealmDataUpdate(ActivityBase.mRealm, new DatabaseRunnable(null) {
                @Override
                public void run() {
                    int signatureID = Integer.MAX_VALUE;

                    try {
                        signatureID = Integer.parseInt(signatureModel.getSignatureId());
                    } catch (Exception e) {
                        CommonFunction.print("Exception");
                    }

                    if ((String.valueOf(signatureID).equalsIgnoreCase(signatureModel.getSignatureId())) && (signatureID < 0)) {
                        //unsynced photo >> delete from realm now.
                        signatureModel.deleteFromRealm();
                    } else {
                        //synced photo >> just mark as deleted >> will be actually deleted on sync_pic_info
                        CommonFunction.print("Deleted signatures count: signature.setPhotoIsDeleted(true) ");
                        signatureModel.setSignatureIsDeleted(true);
                    }

                    TaskDetailModel taskDetailModel = DatabaseManager.getInstance(getContext()).getTaskDetailModelWithTaskID(ActivityBase.mRealm, taskID, true);
                    taskDetailModel.setTaskPhotosModifiedDate(Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime());

                }
            });
        }
    }

    public void confirmNoSignature() {

        // set id
        int newSignatureID = -1;
        Date date = Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime();

        signatureImageView.setVisibility(View.VISIBLE);
        signatureImageView.setImageResource(R.drawable.no_signature);
        signatureImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);

        SignatureFolderModel signatureFolderModelUpdate = DatabaseManager.getInstance(getContext()).getSignatureFolderModelFromTaskIDWithSignatureFolderID(ActivityBase.mRealm, taskID, formID, true);

        if (signatureFolderModelUpdate != null) {

            for (SignatureModel signatureModelSave : signatureFolderModelUpdate.getSignatureFolderContents()) {
                try {
                    int existingPhotoID = Integer.parseInt(signatureModelSave.getSignatureId());


                    if (existingPhotoID <= newSignatureID) {
                        newSignatureID = existingPhotoID - 1;
                    }

                } catch (Exception e) {
                    CommonFunction.print("Exception");
                }
            }


            SignatureModel tmpSig = new SignatureModel();
            if (signatureModel == null) {

                tmpSig.setTaskID(taskID);
                tmpSig.setSignatureID(String.valueOf(newSignatureID));
                tmpSig.setFormID(formID);
                tmpSig.setQuestionID(questionModel.getQuestionID());
                tmpSig.setSignatureLocalPath(null);
                tmpSig.setSignatureItemUpdated(true);
                tmpSig.setSignatureIsDeleted(false);
                tmpSig.setSignatureLastModifiedDate(date);
                tmpSig.setSignatureSignedUpdated(true);
                tmpSig.setCannotUploadMandatory(true);
                tmpSig.setSignedBy(signature_signedBy_editText.getText().toString());

                //CommonFunction.print("Uploaded signatures count: NO SIGNATURE :NOT EXIST " + taskID + " : " + formID + " : " + signatureFolderModel.getSignatureFolderID() + " : " + signatureFolderModel.getSignatureFolderContents().size());

                appendToSignatureFolderContents(signatureFolderModelUpdate, tmpSig);
            } else {
                Realm realm = Realm.getInstance(DatabaseManager.realmConfiguration);
                realm.beginTransaction();
                signatureModel.setSignedBy(signature_signedBy_editText.getText().toString());
                signatureModel.setSignatureLastModifiedDate(date);
                signatureModel.setCannotUploadMandatory(true);
                realm.commitTransaction();

                CommonFunction.print("Uploaded signatures count: NO SIGNATURE :EXIST " + taskID + " : " + formID + " : " + signatureFolderModel.getSignatureFolderID() + " : " + signatureFolderModel.getSignatureFolderContents().size());

            }

        }

    }

    private void startInsertOrUpdateSignatureDetailDirect(Bitmap compressedBitmap) {
        CommonFunction.enableUserInteraction(requireActivity());
        CommonFunction.getInstance(getContext()).showProgressIndicator(getString(R.string.ProgressDialogAddingTitleText));

       // CommonFunction.print("Signature Folders : Saving : Upload : SIGNATURE : SAVED A " + (signatureModel == null));

//        confirmDeleted();

//        CommonFunction.print("Signature Folders : Savings : UPLOADS : DELETESSS : UPLOADS : FOLDERS : 6 " + signatureFolderModel.getSignatureFolderContents().size());
        File imageFile = null;

        //mo, you cannot use photoModel here in doInBackground() >> raise error.
        //choseBitmap is null when image is 'unable to load photo'.

        if (compressedBitmap != null) {

//            CommonFunction.print("Signature Folders : Savings : UPLOADS : DELETESSS : UPLOADS : FOLDERS : 1 " + " : " + compressedBitmap.getByteCount());
            imageFile = CommonFunction.saveSignatureToExternalPictureDirectory(compressedBitmap, Long.toString(System.currentTimeMillis()) + ".PNG");

        }

        Date date = Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime();
        if (imageFile != null) {

//            CommonFunction.print("Signature Folders : Savings : UPLOADS : DELETESSS : UPLOADS : FOLDERS : 3 :  " + imageFile.getAbsolutePath());

            CommonFunction.saveImageFileToGallery(getContext(), imageFile);

            // Preparing new record

            // find photo folder by task id.

            SignatureFolderModel signatureFolderModelUpdate = DatabaseManager.getInstance(getContext()).getSignatureFolderModelFromTaskIDWithSignatureFolderID(ActivityBase.mRealm, taskID, formID, true);
            if (signatureFolderModelUpdate != null) {

//                CommonFunction.print("Signature Folders : Savings : UPLOADS : DELETESSS : UPLOADS : FOLDERS : 5 " + signatureFolderModel.getSignatureFolderID() + " : " + signatureFolderModel.getSignatureFolderContents().size());

                if (imageFile.exists()) {

                    // set id
                    int newSignatureID = -1;


                    for (SignatureModel signatureModelSave : signatureFolderModelUpdate.getSignatureFolderContents()) {
                        try {
                            int existingPhotoID = Integer.parseInt(signatureModelSave.getSignatureId());

                            if (existingPhotoID <= newSignatureID) {
                                newSignatureID = existingPhotoID - 1;
                            }

                        } catch (Exception e) {
                            CommonFunction.print("Exception");
                        }
                    }


                    SignatureModel tmpSig = new SignatureModel();
                    tmpSig.setTaskID(taskID);

                    tmpSig.setSignatureID(String.valueOf(newSignatureID));
                    tmpSig.setFormID(formID);
                    tmpSig.setQuestionID(questionModel.getQuestionID());
                    tmpSig.setSignatureLocalPath(imageFile.getAbsolutePath());
                    tmpSig.setSignatureItemUpdated(true);
                    tmpSig.setSignatureIsDeleted(false);
                    tmpSig.setSignatureLastModifiedDate(date);
                    tmpSig.setSignatureSignedUpdated(true);
                    tmpSig.setCannotUploadMandatory(false);


                    //give empty caption, otherwise you may have error when converting to cl (GalleryPhoto) obj.
                    if (signature_signedBy_editText.getText() != null) {
                        tmpSig.setSignedBy(signature_signedBy_editText.getText().toString());
                    } else {
                        tmpSig.setSignedBy("");
                    }
                    appendToSignatureFolderContents(signatureFolderModel, tmpSig);
                }
            }else {
                CommonFunction.getInstance(getContext()).hideProgressIndicator();
                Toast.makeText(getContext(),"Signature Folder not found.",Toast.LENGTH_LONG).show();
            }
        }


        if (signatureFolderModel.getSignatureFolderContents().size() >= 1) {
            CommonFunction.getInstance(getContext()).hideProgressIndicator();
            popFragment();
        }

    }

    private void appendToSignatureFolderContents(SignatureFolderModel signatureFolderModelTmp, SignatureModel signatureModelTmp) {

        Realm realm = Realm.getInstance(DatabaseManager.realmConfiguration);
        realm.beginTransaction();

        RealmList<SignatureModel> signatureFolderContents = signatureFolderModelTmp.getSignatureFolderContents();
        signatureFolderContents.add(signatureModelTmp);

        TaskDetailModel taskDetailModel = DatabaseManager.getInstance(getContext()).getTaskDetailModelWithTaskID(ActivityBase.mRealm, taskID, true);
        taskDetailModel.setTaskSignaturesModifiedDate(Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime());

        realm.commitTransaction();
    }

}
