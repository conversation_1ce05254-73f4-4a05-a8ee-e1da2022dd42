import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

class ElapsedTimeDialog extends StatelessWidget {
  final String elapsedTime;
  final VoidCallback? onOk;

  const ElapsedTimeDialog({
    super.key,
    required this.elapsedTime,
    this.onOk,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: <PERSON>umn(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Total Time',
              style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              'Elapsed Time: $elapsedTime',
              textAlign: TextAlign.center,
              style: Theme.of(context)
                  .textTheme
                  .montserratTitleExtraSmall
                  .copyWith(
                    color: AppColors.black.withAlpha(196),
                  ),
            ),
            const SizedBox(height: 20),
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  onOk?.call();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                child: Text(
                  'OK',
                  style: Theme.of(context)
                      .textTheme
                      .montserratTitleExtraSmall
                      .copyWith(
                        color: Colors.white,
                      ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Future<void> show({
    required BuildContext context,
    required String elapsedTime,
    VoidCallback? onOk,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return ElapsedTimeDialog(
          elapsedTime: elapsedTime,
          onOk: onOk,
        );
      },
    );
  }
}
