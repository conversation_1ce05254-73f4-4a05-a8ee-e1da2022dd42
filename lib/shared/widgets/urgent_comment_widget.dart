import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'comment_widget.dart';

/// A widget for displaying urgent/priority comments
class UrgentCommentWidget extends StatelessWidget {
  /// The comment text content
  final String text;

  /// Custom background color (optional)
  final Color? backgroundColor;

  /// Custom text color (optional)
  final Color? textColor;

  /// Enable HTML rendering
  final bool enableHtml;

  const UrgentCommentWidget({
    super.key,
    required this.text,
    this.backgroundColor,
    this.textColor,
    this.enableHtml = false,
  });

  @override
  Widget build(BuildContext context) {
    return CommentWidget(
      icon: SvgPicture.asset(
        'assets/icons/fast.svg',
        width: 12,
        height: 12,
        colorFilter:
            const ColorFilter.mode(AppColors.richOrange, BlendMode.srcIn),
      ),
      text: text,
      backgroundColor:
          backgroundColor ?? AppColors.richOrange15, // rgba(244, 107, 38, 0.15)
      textColor: textColor ?? AppColors.darkOrange50,
      enableHtml: enableHtml,
    );
  }
}
