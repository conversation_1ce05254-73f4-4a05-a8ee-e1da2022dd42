import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

/// A base widget for displaying comment cards with different styles
class CommentWidget extends StatefulWidget {
  /// The icon to display in the comment
  final Widget icon;

  /// The comment text content
  final String text;

  /// Background color of the comment card
  final Color backgroundColor;

  /// Text color for the comment
  final Color textColor;

  /// Border radius for the card
  final double borderRadius;

  /// Padding around the card
  final EdgeInsets padding;

  /// Gap between icon and text
  final double gap;
  final bool? isInfo;

  /// Optional custom text builder for complex text layouts
  final Widget Function(BuildContext context, String text)? customTextBuilder;

  /// Enable HTML rendering
  final bool enableHtml;

  const CommentWidget({
    super.key,
    required this.icon,
    required this.text,
    required this.backgroundColor,
    required this.textColor,
    this.borderRadius = 10.0,
    this.padding = const EdgeInsets.all(8.0),
    this.gap = 8.0,
    this.customTextBuilder,
    this.isInfo,
    this.enableHtml = false,
  });

  @override
  State<CommentWidget> createState() => _CommentWidgetState();
}

class _CommentWidgetState extends State<CommentWidget> {
  final bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    print('is---${widget.isInfo}');
    return GestureDetector(
      onTap: () {
        // setState(() {
        //   _isExpanded = !_isExpanded;
        // });
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          vertical: 16,
          horizontal: 16,
        ),
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(10),
          border: widget.isInfo == true
              ? Border.all(color: Colors.grey.shade400, width: 1)
              : null,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start, // Top align the icon
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: widget.icon,
            ),
            const Gap(8),
            Expanded(
              child: widget.customTextBuilder != null
                  ? widget.customTextBuilder!(context, widget.text)
                  : widget.enableHtml
                      ? Html(
                          data: widget.text,
                          style: {
                            "body": Style(
                              margin: Margins.zero,
                              padding: HtmlPaddings.zero,
                              fontSize: FontSize(13),
                              color: widget.textColor,
                              lineHeight: const LineHeight(1.4),
                              fontFamily: 'Montserrat',
                            ),
                            "p": Style(
                              margin: Margins.only(bottom: 4),
                              fontSize: FontSize(13),
                              color: widget.textColor,
                              lineHeight: const LineHeight(1.4),
                              fontFamily: 'Montserrat',
                            ),
                            "div": Style(
                              fontSize: FontSize(13),
                              color: widget.textColor,
                              lineHeight: const LineHeight(1.4),
                              fontFamily: 'Montserrat',
                            ),
                          },
                        )
                      : _isExpanded
                          ? Text(
                              widget.text,
                              style: Theme.of(context)
                                  .textTheme
                                  .montserratParagraphXsmall
                                  .copyWith(
                                    color: widget.textColor,
                                  ),
                            )
                          : Text(
                              widget.text,
                              style: Theme.of(context)
                                  .textTheme
                                  .montserratParagraphXsmall
                                  .copyWith(
                                    color: widget.textColor,
                                  ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
            ),
          ],
        ),
      ),
    );
  }
}
