import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/shared/models/downloaded_file_model.dart';
import 'package:storetrack_app/di/service_locator.dart';

/// A reusable widget for displaying images with offline support
/// Automatically checks for cached files and falls back to network loading
class OfflineImageWidget extends StatefulWidget {
  /// The image URL to display
  final String? url;

  /// Width of the image (optional)
  final double? width;

  /// Height of the image (optional)
  final double? height;

  /// How to fit the image within its bounds
  final BoxFit fit;

  /// Border radius for the image
  final double borderRadius;

  /// Whether to show a placeholder when no image is available
  final bool showPlaceholder;

  const OfflineImageWidget({
    super.key,
    this.url,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius = 8.0,
    this.showPlaceholder = false,
  });

  @override
  State<OfflineImageWidget> createState() => _OfflineImageWidgetState();
}

class _OfflineImageWidgetState extends State<OfflineImageWidget> {
  Future<String>? _displayPathFuture;

  @override
  void initState() {
    super.initState();
    _initializeDisplayPath();
  }

  @override
  void didUpdateWidget(OfflineImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only reinitialize if the URL changed
    if (oldWidget.url != widget.url) {
      _initializeDisplayPath();
    }
  }

  /// Initialize the display path future only when needed
  void _initializeDisplayPath() {
    if (widget.url != null && widget.url!.isNotEmpty) {
      _displayPathFuture = _getDisplayPath(widget.url!);
    } else {
      _displayPathFuture = null;
    }
  }

  /// Gets the local file path if available, otherwise returns the original URL
  Future<String> _getDisplayPath(String url) async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final cached = realm.find<DownloadedFileModel>(url);

      if (cached?.isDownloaded == true) {
        final file = File(cached!.localPath);
        if (await file.exists()) {
          // Update last access date for cache management
          realm.write(() => cached.lastAccessDate = DateTime.now());
          return cached.localPath;
        }
      }

      return url; // Fallback to network URL
    } catch (e) {
      return url; // Fallback on error
    }
  }

  /// Determines if the image path is a network URL
  bool _isNetworkImage(String imagePath) {
    return imagePath.startsWith('http://') || imagePath.startsWith('https://');
  }

  /// Builds the appropriate image widget based on the image source
  Widget _buildImageWidget(String imagePath) {
    if (_isNetworkImage(imagePath)) {
      return _buildNetworkImage(imagePath);
    } else {
      return _buildLocalImage(imagePath);
    }
  }

  /// Builds a network image with caching and error handling
  Widget _buildNetworkImage(String imageUrl) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      placeholder: (context, url) => _buildLoadingPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorPlaceholder(),
    );
  }

  /// Builds a local file image with error handling
  Widget _buildLocalImage(String imagePath) {
    final file = File(imagePath);

    return Image.file(
      file,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      errorBuilder: (context, error, stackTrace) => _buildErrorPlaceholder(),
    );
  }

  /// Builds a loading placeholder
  Widget _buildLoadingPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: AppColors.lightGrey2,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
          ),
        ),
      ),
    );
  }

  /// Builds an error placeholder
  Widget _buildErrorPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: AppColors.lightGrey2,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Icon(
        Icons.broken_image_outlined,
        color: AppColors.blackTint1,
        size: (widget.width != null && widget.height != null)
            ? (widget.width! < widget.height!
                    ? widget.width!
                    : widget.height!) *
                0.4
            : 24.0,
      ),
    );
  }

  /// Builds a placeholder when no image is available
  Widget _buildPlaceholder() {
    if (!widget.showPlaceholder) {
      return const SizedBox.shrink();
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: AppColors.lightGrey2,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Icon(
        Icons.image_outlined,
        color: AppColors.blackTint1,
        size: (widget.width != null && widget.height != null)
            ? (widget.width! < widget.height!
                    ? widget.width!
                    : widget.height!) *
                0.4
            : 24.0,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Don't render anything if no URL and no placeholder requested
    if ((widget.url == null || widget.url!.isEmpty) &&
        !widget.showPlaceholder) {
      return const SizedBox.shrink();
    }

    // Show placeholder if no URL
    if (widget.url == null || widget.url!.isEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: _buildPlaceholder(),
      );
    }

    // Use the cached future to prevent flickering on rebuilds
    return ClipRRect(
      borderRadius: BorderRadius.circular(widget.borderRadius),
      child: FutureBuilder<String>(
        future: _displayPathFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingPlaceholder();
          }

          if (snapshot.hasError || !snapshot.hasData) {
            return _buildErrorPlaceholder();
          }

          return _buildImageWidget(snapshot.data!);
        },
      ),
    );
  }
}
