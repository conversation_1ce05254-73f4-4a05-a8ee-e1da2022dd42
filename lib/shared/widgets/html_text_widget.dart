import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:auto_route/auto_route.dart';
import '../../config/routes/app_router.gr.dart';

/// A widget that renders HTML content if it contains HTML tags,
/// otherwise renders it as plain text.
class HtmlTextWidget extends StatelessWidget {
  final String? text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final void Function(String? url, BuildContext context)? onLinkTap;

  const HtmlTextWidget({
    super.key,
    required this.text,
    this.style,
    this.maxLines,
    this.overflow,
    this.textAlign,
    this.onLinkTap,
  });

  @override
  Widget build(BuildContext context) {
    if (text == null || text!.isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if the text contains HTML tags
    if (_containsHtmlTags(text!)) {
      return Html(
        data: text!,
        style: {
          "body": Style(
            margin: Margins.zero,
            padding: HtmlPaddings.zero,
            fontSize:
                style?.fontSize != null ? FontSize(style!.fontSize!) : null,
            color: style?.color,
            fontWeight: style?.fontWeight,
            fontFamily: style?.fontFamily,
            textAlign: _getHtmlTextAlign(textAlign),
          ),
          "p": Style(
            margin: Margins.zero,
            padding: HtmlPaddings.zero,
          ),
          "div": Style(
            margin: Margins.zero,
            padding: HtmlPaddings.zero,
          ),
        },
        onLinkTap: (url, attributes, element) {
          _handleLinkTap(url, context);
        },
      );
    } else {
      // Render as plain text
      return Text(
        text!,
        style: style,
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
      );
    }
  }

  /// Check if the text contains HTML tags
  bool _containsHtmlTags(String text) {
    // Simple regex to check for HTML tags
    final htmlTagRegex = RegExp(r'<[^>]+>');
    return htmlTagRegex.hasMatch(text);
  }

  /// Convert Flutter TextAlign to HTML TextAlign
  TextAlign _getHtmlTextAlign(TextAlign? textAlign) {
    switch (textAlign) {
      case TextAlign.left:
        return TextAlign.left;
      case TextAlign.right:
        return TextAlign.right;
      case TextAlign.center:
        return TextAlign.center;
      case TextAlign.justify:
        return TextAlign.justify;
      case TextAlign.start:
        return TextAlign.start;
      case TextAlign.end:
        return TextAlign.end;
      default:
        return TextAlign.start;
    }
  }

  /// Handles link tap events
  void _handleLinkTap(String? url, BuildContext context) {
    if (url == null || url.isEmpty) return;

    // If custom onLinkTap is provided, use it
    if (onLinkTap != null) {
      onLinkTap!(url, context);
      return;
    }

    // Default behavior: open in WebBrowserPage
    if (_isValidWebUrl(url)) {
      context.router.push(WebBrowserRoute(
        url: url,
        title: 'Web Link',
      ));
    }
  }

  /// Check if the URL is a valid web URL
  bool _isValidWebUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
}
