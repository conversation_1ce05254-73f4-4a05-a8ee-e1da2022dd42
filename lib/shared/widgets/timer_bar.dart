import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:get_it/get_it.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/services/timer_service.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/timer/timer_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/timer/timer_state.dart';

class TimerBar extends StatefulWidget {
  final int? taskId;
  final Duration? budget;

  const TimerBar({
    super.key,
    this.taskId,
    this.budget,
  });

  @override
  State<TimerBar> createState() => _TimerBarState();
}

class _TimerBarState extends State<TimerBar> {
  Duration? _previousSessionDuration;

  @override
  void initState() {
    super.initState();
    _loadPreviousSessionDuration();
  }

  Future<void> _loadPreviousSessionDuration() async {
    if (widget.taskId == null) return;

    try {
      final timerService = GetIt.instance<TimerService>();
      final taskDetail = await timerService.getTaskById(widget.taskId!);

      if (taskDetail != null &&
          taskDetail.taskCommencementTimeStamp != null &&
          taskDetail.taskStoppedTimeStamp != null) {
        final duration = timerService.calculateElapsedTime(taskDetail);
        if (mounted) {
          setState(() {
            _previousSessionDuration = duration;
          });
        }
      }
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TimerCubit, TimerState>(
      listener: (context, state) {
        // Refresh previous session data when timer transitions to initial state
        if (state is TimerInitial) {
          _loadPreviousSessionDuration();
        }
      },
      child: BlocBuilder<TimerCubit, TimerState>(
        builder: (context, state) {
          return Container(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom:
                    BorderSide(color: AppColors.appBarBorderBlack, width: 0.5),
              ),
            ),
            child: _buildContent(context, state),
          );
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, TimerState state) {
    if (state is TimerError) {
      return _buildErrorView(context, state.message);
    }

    if (state is TimerRunning || state is TimerPaused) {
      return _buildRunningPausedView(context, state);
    }

    return _buildInitialView(context);
  }

  Widget _buildErrorView(BuildContext context, String message) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: AppColors.loginRed.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.loginRed),
      ),
      child: Center(
        child: Text(
          message,
          style: Theme.of(context).textTheme.montserratSemiBold.copyWith(
                color: AppColors.loginRed,
                fontSize: 14,
              ),
        ),
      ),
    );
  }

  Widget _buildInitialView(BuildContext context) {
    return InkWell(
      onTap: () {
        if (widget.taskId != null) {
          context.read<TimerCubit>().startTimer(widget.taskId!);
        }
      },
      borderRadius: BorderRadius.circular(10),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.primaryBlue),
        ),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.play_arrow_rounded,
                color: AppColors.primaryBlue,
              ),
              const Gap(8),
              Text(
                _getStartTimerText(context),
                style: Theme.of(context).textTheme.montserratSemiBold.copyWith(
                      color: AppColors.primaryBlue,
                      fontSize: 14,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRunningPausedView(BuildContext context, TimerState state) {
    final isRunning = state is TimerRunning;

    Duration elapsed = Duration.zero;
    Duration budgetDuration = Duration.zero;

    if (state is TimerRunning) {
      elapsed = state.elapsed;
      budgetDuration = state.budget;
    } else if (state is TimerPaused) {
      elapsed = state.elapsed;
      budgetDuration = state.budget;
    }

    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Time elapsed',
                style: Theme.of(context)
                    .textTheme
                    .montserratFormsField
                    .copyWith(color: AppColors.blackTint1),
              ),
              Text(
                _formatElapsedTime(elapsed, budgetDuration),
                style: Theme.of(context)
                    .textTheme
                    .montserratTitleSmall
                    .copyWith(
                        color: AppColors.black,
                        letterSpacing: 0.5,
                        fontSize: 14),
              ),
            ],
          ),
          Row(
            children: [
              _buildControlButton(
                icon: isRunning ? Icons.pause : Icons.play_arrow,
                onTap: () {
                  if (isRunning) {
                    context.read<TimerCubit>().pauseTimer();
                  } else {
                    context.read<TimerCubit>().resumeTimer();
                  }
                },
                backgroundColor:
                    isRunning ? AppColors.warningBgColor : AppColors.green15,
                iconColor:
                    isRunning ? AppColors.richOrange : AppColors.loginGreen,
              ),
              const Gap(12),
              _buildControlButton(
                icon: Icons.stop,
                onTap: () {
                  context.read<TimerCubit>().stopTimer();
                },
                backgroundColor: AppColors.loginRed.withValues(alpha: 0.15),
                iconColor: AppColors.loginRed,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color iconColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(icon, color: iconColor),
      ),
    );
  }

  String _formatElapsedTime(Duration elapsed, Duration budget) {
    final budgetMinutes = budget.inMinutes;

    if (elapsed.inMinutes > 0) {
      return "${elapsed.inMinutes}m ${elapsed.inSeconds.remainder(60)}s / ${budgetMinutes}m";
    }
    return "${elapsed.inSeconds}s / ${budgetMinutes}m";
  }

  String _getStartTimerText(BuildContext context) {
    if (widget.taskId == null) return 'Start timer';

    if (_previousSessionDuration != null) {
      final formatted = _formatPreviousDuration(_previousSessionDuration!);
      return 'Start timer (Prev: $formatted)';
    }

    return 'Start timer';
  }

  String _formatPreviousDuration(Duration duration) {
    if (duration.inMinutes > 0) {
      return "${duration.inMinutes}m ${duration.inSeconds.remainder(60)}s";
    }
    return "${duration.inSeconds}s";
  }
}
