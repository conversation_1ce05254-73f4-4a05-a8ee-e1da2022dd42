import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';

/// A reusable search bar widget with barcode scanner functionality
///
/// This widget combines a search TextField with a barcode scanner button,
/// providing consistent search functionality across different pages.
class SearchBarWithScanner extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final Function(String) onScanResult;
  final String? scanNotFoundMessage;

  const SearchBarWithScanner({
    super.key,
    required this.controller,
    this.hintText = 'Search...',
    required this.onScanResult,
    this.scanNotFoundMessage,
  });

  /// Handle barcode scanning
  Future<void> _handleBarcodeScan(BuildContext context) async {
    try {
      final result = await context.pushRoute<String>(
        const BarcodeScannerRoute(),
      );

      if (result != null && result.isNotEmpty) {
        // Update search field with scanned code
        controller.text = result;

        // Call the callback with the scanned result
        onScanResult(result);
      }
    } catch (e) {
      if (context.mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to scan barcode: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: hintText,
                prefixIcon: const Icon(Icons.search),
                fillColor: Colors.white,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    color: Colors.grey.shade300,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    color: Colors.grey.shade300,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    color: AppColors.primaryBlue,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          const Gap(8),
          Container(
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: BorderRadius.circular(10),
            ),
            width: 46,
            height: 46,
            child: IconButton(
              onPressed: () => _handleBarcodeScan(context),
              icon: const Icon(
                Icons.qr_code_scanner,
                color: Colors.white,
              ),
              tooltip: 'Scan barcode',
            ),
          ),
        ],
      ),
    );
  }
}
