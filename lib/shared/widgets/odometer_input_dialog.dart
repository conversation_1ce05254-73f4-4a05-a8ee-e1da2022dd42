import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:intl/intl.dart';

class OdometerInputDialog extends StatefulWidget {
  final String title;
  final String message;
  final Function(int) onConfirm;
  final VoidCallback? onCancel;
  final bool autoFocus;

  const OdometerInputDialog({
    super.key,
    required this.title,
    required this.message,
    required this.onConfirm,
    this.onCancel,
    this.autoFocus = true,
  });

  @override
  State<OdometerInputDialog> createState() => _OdometerInputDialogState();

  static Future<void> show({
    required BuildContext context,
    required String title,
    required String message,
    required Function(int) onConfirm,
    VoidCallback? onCancel,
    bool autoFocus = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return OdometerInputDialog(
          title: title,
          message: message,
          onConfirm: onConfirm,
          onCancel: onCancel,
          autoFocus: autoFocus,
        );
      },
    );
  }
}

class _OdometerInputDialogState extends State<OdometerInputDialog> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isValid = true;
  bool _isLoading = false;
  String? _errorMessage;
  final NumberFormat _numberFormat = NumberFormat('#,###');

  @override
  void initState() {
    super.initState();
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _validateAndSubmit() async {
    if (_isLoading) return;

    final text = _controller.text.replaceAll(',', '').trim();

    if (text.isEmpty) {
      setState(() {
        _isValid = false;
        _errorMessage = 'Please enter your odometer reading';
      });
      return;
    }

    final kms = int.tryParse(text);
    if (kms == null || kms < 0) {
      setState(() {
        _isValid = false;
        _errorMessage = 'Please enter a valid number (0 or greater)';
      });
      return;
    }

    if (kms > 999999) {
      setState(() {
        _isValid = false;
        _errorMessage = 'Odometer reading must be less than 999,999 km';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _isValid = true;
      _errorMessage = null;
    });

    try {
      Navigator.pop(context);
      widget.onConfirm(kms);
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isValid = false;
        _errorMessage = 'An error occurred. Please try again.';
      });
    }
  }

  void _cancel() {
    if (_isLoading) return;
    Navigator.pop(context);
    widget.onCancel?.call();
  }

  void _formatInput(String value) {
    if (value.isEmpty) return;

    final numericValue = value.replaceAll(',', '');
    final number = int.tryParse(numericValue);

    if (number != null) {
      final formatted = _numberFormat.format(number);
      if (formatted != value) {
        _controller.value = TextEditingValue(
          text: formatted,
          selection: TextSelection.collapsed(offset: formatted.length),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.title,
              style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              widget.message,
              textAlign: TextAlign.center,
              style: Theme.of(context)
                  .textTheme
                  .montserratTitleExtraSmall
                  .copyWith(
                    color: AppColors.black.withAlpha(196),
                  ),
            ),
            const SizedBox(height: 20),
            TextField(
              controller: _controller,
              focusNode: _focusNode,
              keyboardType: TextInputType.number,
              enabled: !_isLoading,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9,]')),
                LengthLimitingTextInputFormatter(10),
              ],
              decoration: InputDecoration(
                labelText: 'Odometer Reading',
                hintText: 'e.g., 12,345 km',
                helperText: 'Enter your current odometer reading in kilometers',
                suffixText: 'km',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: AppColors.primaryBlue,
                    width: 2,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Colors.red,
                    width: 2,
                  ),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Colors.red,
                    width: 2,
                  ),
                ),
                errorText: _isValid ? null : _errorMessage,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
              style: Theme.of(context).textTheme.bodyLarge,
              onChanged: (value) {
                if (!_isValid) {
                  setState(() {
                    _isValid = true;
                    _errorMessage = null;
                  });
                }
                _formatInput(value);
              },
              onSubmitted: (_) => _validateAndSubmit(),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isLoading ? null : _cancel,
                  child: Text(
                    'Cancel',
                    style: Theme.of(context)
                        .textTheme
                        .montserratTitleExtraSmall
                        .copyWith(
                          color: _isLoading ? Colors.grey : null,
                        ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _validateAndSubmit,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isLoading
                        ? Colors.grey.shade400
                        : AppColors.primaryBlue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    elevation: _isLoading ? 0 : 2,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : Text(
                          'Confirm',
                          style: Theme.of(context)
                              .textTheme
                              .montserratTitleExtraSmall
                              .copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
