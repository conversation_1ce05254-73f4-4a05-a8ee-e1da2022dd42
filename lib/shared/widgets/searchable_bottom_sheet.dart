import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';

class SearchableBottomSheet<T> extends StatefulWidget {
  final String title;
  final String searchHint;
  final List<T> items;
  final String Function(T) itemDisplayText;
  final bool Function(T, T)? isSelected;
  final void Function(T) onItemSelected;
  final String Function(T, String) searchFilter;

  const SearchableBottomSheet({
    super.key,
    required this.title,
    required this.searchHint,
    required this.items,
    required this.itemDisplayText,
    required this.onItemSelected,
    required this.searchFilter,
    this.isSelected,
  });

  @override
  State<SearchableBottomSheet<T>> createState() =>
      _SearchableBottomSheetState<T>();
}

class _SearchableBottomSheetState<T> extends State<SearchableBottomSheet<T>> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  List<T> get _filteredItems {
    if (_searchQuery.isEmpty) {
      return widget.items;
    }
    return widget.items
        .where((item) => widget
            .searchFilter(item, _searchQuery.toLowerCase())
            .toLowerCase()
            .contains(_searchQuery.toLowerCase()))
        .toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setBottomSheetState) {
        // Add listener for real-time search filtering
        void onSearchChanged() {
          setBottomSheetState(() {
            _searchQuery = _searchController.text;
          });
        }

        // Remove existing listener and add new one
        _searchController.removeListener(onSearchChanged);
        _searchController.addListener(onSearchChanged);

        // Calculate dynamic height based on content
        const itemHeight = 56.0;
        const headerHeight = 80.0;
        const searchBarHeight = 70.0;
        const bottomPadding = 16.0;
        final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
        final calculatedHeight = headerHeight +
            searchBarHeight +
            (_filteredItems.length * itemHeight) +
            bottomPadding;
        final finalHeight = calculatedHeight > maxContentHeight
            ? maxContentHeight
            : calculatedHeight;

        return Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: finalHeight,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12),
                  decoration: BoxDecoration(
                    color: AppColors.blackTint2,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Header
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    widget.title,
                    style: AppTypography.montserratTitleExtraSmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                // Search bar
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: widget.searchHint,
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                setBottomSheetState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide:
                            const BorderSide(color: AppColors.blackTint2),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
                const Gap(16),

                // Items list
                Expanded(
                  child: _filteredItems.isEmpty
                      ? Center(
                          child: Text(
                            'No items found',
                            style:
                                AppTypography.montserratParagraphSmall.copyWith(
                              color: AppColors.blackTint1,
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          itemCount: _filteredItems.length,
                          itemBuilder: (context, index) {
                            final item = _filteredItems[index];
                            final isItemSelected =
                                widget.isSelected?.call(item, item) ?? false;

                            return InkWell(
                              onTap: () {
                                widget.onItemSelected(item);
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12.0),
                                decoration: const BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      color: AppColors.blackTint2,
                                      width: 0.5,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        widget.itemDisplayText(item),
                                        style: AppTypography
                                            .montserratParagraphSmall
                                            .copyWith(
                                          color: AppColors.black,
                                        ),
                                      ),
                                    ),
                                    if (isItemSelected)
                                      const Icon(
                                        Icons.radio_button_checked,
                                        color: AppColors.primaryBlue,
                                        size: 20,
                                      ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// Helper function to show searchable bottom sheet
Future<T?> showSearchableBottomSheet<T>({
  required BuildContext context,
  required String title,
  required String searchHint,
  required List<T> items,
  required String Function(T) itemDisplayText,
  required String Function(T, String) searchFilter,
  bool Function(T, T)? isSelected,
}) {
  return showModalBottomSheet<T>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    useRootNavigator: true,
    builder: (context) => SearchableBottomSheet<T>(
      title: title,
      searchHint: searchHint,
      items: items,
      itemDisplayText: itemDisplayText,
      searchFilter: searchFilter,
      isSelected: isSelected,
      onItemSelected: (item) {
        Navigator.pop(context, item);
      },
    ),
  );
}
