import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';

class AppDatePickerTheme {
  static ThemeData getTheme(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Theme.of(context).copyWith(
      colorScheme: Theme.of(context).colorScheme.copyWith(
            primary: AppColors.primaryBlue,
            onPrimary: Colors.white,
            surface: Colors.white,
            onSurface: AppColors.black,
            primaryContainer: AppColors.primaryBlue.withValues(alpha: 0.1),
            onPrimaryContainer: AppColors.primaryBlue,
            secondary: AppColors.primaryBlue.withValues(alpha: 0.8),
            onSecondary: Colors.white,
            outline: AppColors.blackTint2.withValues(alpha: 0.3),
          ),
      datePickerTheme: DatePickerThemeData(
        backgroundColor: Colors.white,
        elevation: 24,
        shadowColor: AppColors.black10.withValues(alpha: 0.3),
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        headerBackgroundColor: AppColors.primaryBlue,
        headerForegroundColor: Colors.white,
        headerHeadlineStyle: textTheme.headlineSmall?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 24,
        ),
        headerHelpStyle: textTheme.bodySmall?.copyWith(
          color: Colors.white.withValues(alpha: 0.9),
          fontSize: 14,
        ),
        weekdayStyle: textTheme.bodySmall?.copyWith(
          color: AppColors.blackTint1,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        dayStyle: textTheme.bodyMedium?.copyWith(
          color: AppColors.black,
          fontSize: 14,
        ),
        dayForegroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.white;
          }
          if (states.contains(WidgetState.disabled)) {
            return AppColors.blackTint2;
          }
          return AppColors.black;
        }),
        dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.black;
          }
          if (states.contains(WidgetState.hovered)) {
            return AppColors.primaryBlue.withValues(alpha: 0.1);
          }
          return Colors.transparent;
        }),
        dayOverlayColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.pressed)) {
            return AppColors.primaryBlue.withValues(alpha: 0.2);
          }
          if (states.contains(WidgetState.focused)) {
            return AppColors.primaryBlue.withValues(alpha: 0.1);
          }
          return Colors.transparent;
        }),
        todayBackgroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.black;
          }
          return AppColors.primaryBlue;
        }),
        todayForegroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.white;
          }
          return Colors.white;
        }),
        todayBorder: BorderSide.none,
        yearStyle: textTheme.bodyLarge?.copyWith(
          color: AppColors.black,
          fontSize: 16,
        ),
        yearForegroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.white;
          }
          return AppColors.black;
        }),
        yearBackgroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primaryBlue;
          }
          return Colors.transparent;
        }),
        yearOverlayColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.pressed)) {
            return AppColors.primaryBlue.withValues(alpha: 0.2);
          }
          return Colors.transparent;
        }),
        rangePickerBackgroundColor: Colors.white,
        rangePickerElevation: 12,
        rangePickerShadowColor: AppColors.black10.withValues(alpha: 0.2),
        rangePickerSurfaceTintColor: Colors.transparent,
        rangePickerShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        rangePickerHeaderBackgroundColor: AppColors.primaryBlue,
        rangePickerHeaderForegroundColor: Colors.white,
        rangeSelectionBackgroundColor:
            AppColors.primaryBlue.withValues(alpha: 0.1),
        rangeSelectionOverlayColor: WidgetStateProperty.all(
          AppColors.primaryBlue.withValues(alpha: 0.1),
        ),
        dividerColor: AppColors.blackTint2.withValues(alpha: 0.2),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: Colors.grey.shade50,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(
              color: AppColors.blackTint2.withValues(alpha: 0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              color: AppColors.primaryBlue,
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        confirmButtonStyle: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: Colors.white,
          elevation: 4,
          shadowColor: AppColors.primaryBlue.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
          textStyle: textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        cancelButtonStyle: TextButton.styleFrom(
          foregroundColor: AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
          textStyle: textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
