class ServerErrorResponse {
  final List<ServerError> error;

  ServerErrorResponse({
    required this.error,
  });

  factory ServerErrorResponse.fromJson(Map<String, dynamic> json) =>
      ServerErrorResponse(
        error: List<ServerError>.from(
          json["error"]?.map((x) => ServerError.fromJson(x)) ?? [],
        ),
      );

  Map<String, dynamic> toJson() => {
        "error": List<dynamic>.from(error.map((x) => x.toJson())),
      };

  /// Format all error messages into a single string for display
  String get formattedMessage {
    if (error.isEmpty) return "Unknown error occurred";

    if (error.length == 1) {
      return error.first.message;
    }

    // Multiple errors - format as bulleted list
    return error.map((e) => "• ${e.message}").join("\n");
  }
}

class ServerError {
  final int code;
  final String message;
  final int? wapi3LogId;
  final String? lastDebugDump;

  ServerError({
    required this.code,
    required this.message,
    this.wapi3LogId,
    this.lastDebugDump,
  });

  factory ServerError.fromJson(Map<String, dynamic> json) => ServerError(
        code: json["code"] ?? 0,
        message: json["message"] ?? "",
        wapi3LogId: json["wapi3_log_id"],
        lastDebugDump: json["last_debug_dump"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "message": message,
        "wapi3_log_id": wapi3LogId,
        "last_debug_dump": lastDebugDump,
      };
}
