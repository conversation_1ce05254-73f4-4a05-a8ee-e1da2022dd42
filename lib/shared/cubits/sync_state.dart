import 'package:equatable/equatable.dart';

abstract class SyncState extends Equatable {
  const SyncState();

  @override
  List<Object> get props => [];
}

class SyncInitial extends SyncState {}

class SyncInProgress extends SyncState {}

class SyncSuc<PERSON> extends SyncState {}

class SyncUserStateValidationRequired extends SyncState {
  final String action;
  final String title;
  final String message;
  final bool kickOut;
  final bool navigateToLogin;
  final bool navigateToProfile;
  final bool checkAddressSkills;

  const SyncUserStateValidationRequired({
    required this.action,
    required this.title,
    required this.message,
    this.kickOut = false,
    this.navigateToLogin = false,
    this.navigateToProfile = false,
    this.checkAddressSkills = false,
  });

  @override
  List<Object> get props => [
        action,
        title,
        message,
        kickOut,
        navigateToLogin,
        navigateToProfile,
        checkAddressSkills,
      ];
}
