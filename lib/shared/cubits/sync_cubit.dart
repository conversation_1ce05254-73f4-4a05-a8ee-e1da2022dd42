import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/shared/cubits/sync_state.dart';

export 'sync_state.dart';

class SyncCubit extends Cubit<SyncState> {
  final SyncService _syncService;
  bool _wasSyncing = false;

  SyncCubit(this._syncService) : super(SyncInitial()) {
    _wasSyncing = _syncService.isSyncing.value;
    _syncService.isSyncing.addListener(_onSyncStatusChanged);
    _syncService.userStateValidationResult
        .addListener(_onUserStateValidationChanged);
  }

  void _onSyncStatusChanged() {
    final isSyncing = _syncService.isSyncing.value;
    if (isSyncing) {
      emit(SyncInProgress());
    } else {
      if (_wasSyncing) {
        // Don't emit SyncSuccess immediately, wait to see if there's a validation result
        Timer(const Duration(milliseconds: 100), () {
          // Only emit SyncSuccess if no validation state has been emitted
          if (state is SyncInProgress) {
            emit(SyncSuccess());
          }
        });
      }
    }
    _wasSyncing = isSyncing;
  }

  void _onUserStateValidationChanged() {
    final validationResult = _syncService.userStateValidationResult.value;
    if (validationResult != null) {
      // Emit user state validation required
      emit(SyncUserStateValidationRequired(
        action: validationResult['action'] ?? '',
        title: validationResult['title'] ?? '',
        message: validationResult['message'] ?? '',
        kickOut: validationResult['kickOut'] ?? false,
        navigateToLogin: validationResult['navigateToLogin'] ?? false,
        navigateToProfile: validationResult['navigateToProfile'] ?? false,
        checkAddressSkills: validationResult['checkAddressSkills'] ?? false,
      ));

      // Clear the validation result after emitting the state
      _syncService.userStateValidationResult.value = null;
    }
  }

  /// Called by UI after handling user state validation
  void markUserStateValidationHandled() {
    if (state is SyncUserStateValidationRequired) {
      emit(SyncSuccess());
    }
  }

  @override
  Future<void> close() {
    _syncService.isSyncing.removeListener(_onSyncStatusChanged);
    _syncService.userStateValidationResult
        .removeListener(_onUserStateValidationChanged);
    return super.close();
  }
}
