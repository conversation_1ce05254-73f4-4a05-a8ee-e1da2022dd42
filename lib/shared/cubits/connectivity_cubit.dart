import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:storetrack_app/core/network/network_info.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/shared/cubits/connectivity_state.dart';

export 'connectivity_state.dart';

class ConnectivityCubit extends Cubit<ConnectivityState> {
  final NetworkInfo _networkInfo;
  final DataManager _dataManager;
  StreamSubscription<InternetStatus>? _connectivitySubscription;
  bool _workOfflineEnabled = false;

  ConnectivityCubit(this._networkInfo, this._dataManager)
      : super(ConnectivityInitial()) {
    _initializeConnectivity();
    _loadWorkOfflineSetting();
  }

  void _initializeConnectivity() {
    // Check initial connectivity
    _checkConnectivity();

    // Listen for connectivity changes
    _connectivitySubscription = InternetConnection().onStatusChange.listen(
      (InternetStatus status) {
        switch (status) {
          case InternetStatus.connected:
            if (!_workOfflineEnabled) {
              emit(ConnectivityOnline());
            }
            break;
          case InternetStatus.disconnected:
            if (!_workOfflineEnabled) {
              emit(ConnectivityOffline());
            }
            break;
        }
      },
    );
  }

  Future<void> _checkConnectivity() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      if (!isClosed && !_workOfflineEnabled) {
        emit(isConnected ? ConnectivityOnline() : ConnectivityOffline());
      }
    } catch (e) {
      // If there's an error checking connectivity, assume offline for safety
      if (!isClosed && !_workOfflineEnabled) {
        emit(ConnectivityOffline());
      }
    }
  }

  /// Check connectivity manually if needed
  Future<void> checkConnectivity() => _checkConnectivity();

  /// Returns true if currently online
  bool get isOnline => state is ConnectivityOnline;

  /// Returns true if currently offline
  bool get isOffline => state is ConnectivityOffline;

  /// Returns true if work offline mode is enabled
  bool get isWorkOfflineEnabled => _workOfflineEnabled;

  /// Load work offline setting from data manager
  Future<void> _loadWorkOfflineSetting() async {
    try {
      _workOfflineEnabled = await _dataManager.getWorkOfflineEnabled();
      _updateState();
    } catch (e) {
      // Handle error loading setting, keep default false
    }
  }

  /// Set work offline mode and save to storage
  Future<void> setWorkOfflineMode(bool enabled) async {
    try {
      await _dataManager.saveWorkOfflineEnabled(enabled);
      _workOfflineEnabled = enabled;
      _updateState();
    } catch (e) {
      // Handle error saving setting
    }
  }

  /// Update state based on both connectivity and work offline mode
  void _updateState() {
    if (_workOfflineEnabled) {
      emit(ConnectivityWorkOffline());
    } else if (state is ConnectivityOnline ||
        state is ConnectivityWorkOffline) {
      emit(ConnectivityOnline());
    } else {
      emit(ConnectivityOffline());
    }
  }

  @override
  Future<void> close() {
    _connectivitySubscription?.cancel();
    return super.close();
  }
}
