import 'package:package_info_plus/package_info_plus.dart';

class AppInfoService {
  PackageInfo? _packageInfo;

  Future<PackageInfo> _getPackageInfo() async {
    _packageInfo ??= await PackageInfo.fromPlatform();
    return _packageInfo!;
  }

  Future<String> getVersion() async {
    try {
      final packageInfo = await _getPackageInfo();
      return packageInfo.version;
    } catch (e) {
      return "Unknown";
    }
  }

  Future<String> getBuildNumber() async {
    try {
      final packageInfo = await _getPackageInfo();
      return packageInfo.buildNumber;
    } catch (e) {
      return "Unknown";
    }
  }

  Future<String> getAppName() async {
    try {
      final packageInfo = await _getPackageInfo();
      return packageInfo.appName;
    } catch (e) {
      return "StoreTrack";
    }
  }

  Future<String> getPackageName() async {
    try {
      final packageInfo = await _getPackageInfo();
      return packageInfo.packageName;
    } catch (e) {
      return "Unknown";
    }
  }

  Future<String> getFullVersionString() async {
    try {
      final version = await getVersion();
      final buildNumber = await getBuildNumber();
      return "$version+$buildNumber";
    } catch (e) {
      return "Unknown";
    }
  }
}
