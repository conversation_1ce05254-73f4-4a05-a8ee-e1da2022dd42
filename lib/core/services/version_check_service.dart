import 'dart:io';

import '../network/api_client.dart';
import '../storage/data_manager.dart';
import '../services/firebase_service.dart';
import '../services/app_info_service.dart';
import '../../shared/models/result.dart';

abstract class VersionCheckService {
  Future<Result<void>> checkAppVersion();
  Future<String?> getNewVersionDownloadUrl();
}

class VersionCheckServiceImpl implements VersionCheckService {
  VersionCheckServiceImpl({
    required this.apiClient,
    required this.dataManager,
    required this.appInfoService,
  });

  final ApiClient apiClient;
  final DataManager dataManager;
  final AppInfoService appInfoService;

  @override
  Future<Result<void>> checkAppVersion() async {
    try {
      // Get required data
      final appVersion = await appInfoService.getVersion();
      final fcmToken = await FirebaseService.getToken() ?? '';
      final userId = await dataManager.getUserId() ?? '';

      // Get device platform
      final platform = Platform.isAndroid ? 'Android' : 'iOS';

      // Build request URL with query parameters
      final queryParams = {
        'device_platform': platform,
        'appversion': appVersion,
        'token': fcmToken,
        'personID': userId,
      };

      final response = await apiClient.instance.get(
        '/version_information_paramV2',
        queryParameters: queryParams,
      );

      if (response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Get latest version for current platform
        final latestVersion = Platform.isAndroid
            ? responseData['android_version'] as String?
            : responseData['ios_version'] as String?;

        if (latestVersion != null) {
          final isOutdated = _isLowerVersionThan(appVersion, latestVersion);

          if (!isOutdated) {
            // Clear any stored download URL
            await dataManager.saveNewVersionDownloadUrl('');
          } else {
            // App is outdated, store download URL
            final downloadUrl = Platform.isAndroid
                ? responseData['downloadLinkAndroid'] as String?
                : responseData['downloadLinkIOS'] as String?;

            if (downloadUrl != null && downloadUrl.isNotEmpty) {
              await dataManager.saveNewVersionDownloadUrl(downloadUrl);
            }
          }
        }
      }

      return Result.success(null);
    } catch (e) {
      return Result.failure(
          'Cannot get version information at the moment. Please try again later.');
    }
  }

  @override
  Future<String?> getNewVersionDownloadUrl() async {
    return await dataManager.getNewVersionDownloadUrl();
  }

  bool _isLowerVersionThan(String currentVersion, String latestVersion) {
    try {
      final currentParts = currentVersion.split('.').map(int.parse).toList();
      final latestParts = latestVersion.split('.').map(int.parse).toList();

      // Pad arrays to same length
      final maxLength = [currentParts.length, latestParts.length]
          .reduce((a, b) => a > b ? a : b);
      while (currentParts.length < maxLength) {
        currentParts.add(0);
      }
      while (latestParts.length < maxLength) {
        latestParts.add(0);
      }

      // Compare version parts
      for (int i = 0; i < maxLength; i++) {
        if (currentParts[i] < latestParts[i]) return true;
        if (currentParts[i] > latestParts[i]) return false;
      }

      return false; // Equal versions
    } catch (e) {
      return false; // If parsing fails, assume not outdated
    }
  }
}
