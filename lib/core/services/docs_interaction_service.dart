import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/entities/opened_doc_entity.dart';
import 'package:storetrack_app/features/home/<USER>/models/opened_doc_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/opened_doc_item_mapper.dart';

/// Service for handling document interaction operations
abstract class DocsInteractionService {
  /// Save an opened document interaction locally
  Future<bool> saveOpenedDoc(OpenedDocItemEntity item);

  /// Get all saved opened document interactions
  Future<List<OpenedDocItemEntity>> getOpenedDocs();

  /// Clear all saved opened document interactions
  Future<bool> clearOpenedDocs();

  /// Get count of saved opened document interactions
  Future<int> getOpenedDocsCount();
}

/// Implementation of [DocsInteractionService] using Realm database
class DocsInteractionServiceImpl implements DocsInteractionService {
  final RealmDatabase _realmDatabase;

  DocsInteractionServiceImpl({required RealmDatabase realmDatabase})
      : _realmDatabase = realmDatabase;

  @override
  Future<bool> saveOpenedDoc(OpenedDocItemEntity item) async {
    try {
      final model = OpenedDocItemMapper.toModel(item);
      await _realmDatabase.realm.writeAsync(() {
        _realmDatabase.realm.add(model, update: true);
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<OpenedDocItemEntity>> getOpenedDocs() async {
    try {
      final models = _realmDatabase.realm.all<OpenedDocItemModel>();
      return models
          .map((model) => OpenedDocItemMapper.toEntity(model))
          .toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> clearOpenedDocs() async {
    try {
      await _realmDatabase.realm.writeAsync(() {
        final models = _realmDatabase.realm.all<OpenedDocItemModel>();
        _realmDatabase.realm.deleteMany(models);
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<int> getOpenedDocsCount() async {
    try {
      return _realmDatabase.realm.all<OpenedDocItemModel>().length;
    } catch (e) {
      return 0;
    }
  }
}
