import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

/// Service for getting device information
abstract class DeviceInfoService {
  /// Get unique device ID
  Future<String> getDeviceId();

  /// Get device platform (ios/android)
  String getDevicePlatform();

  /// Get device name
  Future<String> getDeviceName();

  /// Get device model
  Future<String> getDeviceModel();

  /// Get device version (OS version)
  Future<String> getDeviceVersion();
}

/// Implementation of DeviceInfoService
class DeviceInfoServiceImpl implements DeviceInfoService {
  final DeviceInfoPlugin _deviceInfoPlugin;

  DeviceInfoServiceImpl(this._deviceInfoPlugin);

  @override
  Future<String> getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        // Use androidId as primary identifier, fallback to a combination if null
        return androidInfo.id.isNotEmpty
            ? androidInfo.id
            : '${androidInfo.brand}_${androidInfo.model}_${androidInfo.fingerprint}'
                .hashCode
                .toString();
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        // Use identifierForVendor as primary identifier, fallback to a combination if null
        return iosInfo.identifierForVendor ??
            '${iosInfo.name}_${iosInfo.model}_${iosInfo.systemVersion}'
                .hashCode
                .toString();
      } else {
        // Fallback for other platforms
        return 'unknown_device_${DateTime.now().millisecondsSinceEpoch}';
      }
    } catch (e) {
      // Fallback in case of any error
      return 'fallback_device_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  @override
  String getDevicePlatform() {
    if (Platform.isAndroid) {
      return 'Android';
    } else if (Platform.isIOS) {
      return 'iOS';
    } else {
      return 'App';
    }
  }

  @override
  Future<String> getDeviceName() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        return androidInfo.model;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        return iosInfo.name;
      } else {
        return 'Unknown Device';
      }
    } catch (e) {
      return 'Unknown Device';
    }
  }

  @override
  Future<String> getDeviceModel() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        return androidInfo.model;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        return iosInfo.model;
      } else {
        return 'Unknown Model';
      }
    } catch (e) {
      return 'Unknown Model';
    }
  }

  @override
  Future<String> getDeviceVersion() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        return androidInfo.version.release;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        return iosInfo.systemVersion;
      } else {
        return 'Unknown Version';
      }
    } catch (e) {
      return 'Unknown Version';
    }
  }
}
