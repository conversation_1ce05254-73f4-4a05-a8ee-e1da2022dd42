import 'dart:math';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../../firebase_options.dart';
import '../utils/logger.dart';
import '../network/api_client.dart';
import '../storage/data_manager.dart';
import 'device_info_service.dart';
import '../../di/service_locator.dart';
import 'app_info_service.dart';
import '../../config/routes/app_router.dart';
import '../../config/routes/app_router.gr.dart';
import '../../features/home/<USER>/blocs/alert_count/alert_count_cubit.dart';

// This handler must be a top-level function (not a class method).
@pragma('vm:entry-point')
Future<void> handleBackgroundMessage(RemoteMessage message) async {
  // You MUST initialize Firebase in the background isolate.
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  logger('🔑 [Background] Received message: ${message.messageId}');

  logger(
      '🔄 [Background] Processing message without DataManager due to isolate limitations');
  final data = message.data;
  final notification = message.notification;
  final String title = data['message'] ?? notification?.title ?? "StoreTrack";
  final String body =
      data['context'] ?? notification?.body ?? "You have a new notification!";

  logger('📱 [Background] Notification: $title - $body');
}

class FirebaseService {
  static FirebaseMessaging? _messaging;
  static final FlutterLocalNotificationsPlugin _localNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static const AndroidNotificationDetails _androidNotificationDetails =
      AndroidNotificationDetails(
    'storetrack',
    'storetrack',
    channelDescription: 'Default channel for notifications',
    importance: Importance.max,
    priority: Priority.high,
    showWhen: true,
  );
  static const DarwinNotificationDetails _iosNotificationDetails =
      DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
  );
  static const NotificationDetails _platformNotificationDetails =
      NotificationDetails(
    android: _androidNotificationDetails,
    iOS: _iosNotificationDetails,
  );

  /// One-time setup for Firebase core services. Call this in main.dart before runApp().
  static Future<void> setupApp() async {
    logger('🚀 [setupApp] Starting Firebase initialization...');
    logger('🔍 [setupApp] Platform: ${Platform.operatingSystem}');
    logger('🔍 [setupApp] Is iOS: ${Platform.isIOS}');

    try {
      logger(
          '🔍 [setupApp] Project ID: ${DefaultFirebaseOptions.currentPlatform.projectId}');
      logger(
          '🔍 [setupApp] App ID: ${DefaultFirebaseOptions.currentPlatform.appId}');

      if (Platform.isIOS) {
        logger(
            '📱 [setupApp] iOS Bundle ID: ${DefaultFirebaseOptions.currentPlatform.iosBundleId}');
        logger(
            '📱 [setupApp] iOS Client ID: ${DefaultFirebaseOptions.currentPlatform.iosClientId}');
        logger(
            '📱 [setupApp] iOS API Key: ${DefaultFirebaseOptions.currentPlatform.apiKey.substring(0, 10)}...');
      }

      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      logger('✅ [setupApp] Firebase initialized successfully');
      logger(
          '🔍 [setupApp] Firebase apps count after init: ${Firebase.apps.length}');

      _messaging = _getFirebaseMessaging();
      if (_messaging != null) {
        logger('✅ [setupApp] FirebaseMessaging instance created successfully');
        FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);
        logger('✅ [setupApp] Background message handler registered');
      } else {
        logger('❌ [setupApp] Failed to create FirebaseMessaging instance');
      }
    } catch (e) {
      logger('❌ [setupApp] Firebase initialization failed: $e');
      logger('❌ [setupApp] Error type: ${e.runtimeType}');
    }
  }

  /// Initializes listeners and permissions. Call this from a widget with a BuildContext.
  static Future<void> initialize(BuildContext context) async {
    logger('🚀 [initialize] Starting Firebase messaging setup...');

    if (_messaging == null) {
      logger('❌ [initialize] Firebase messaging not available, skipping setup');
      logger('🔍 [initialize] Attempting to create messaging instance...');
      _messaging = _getFirebaseMessaging();
      if (_messaging == null) {
        logger('❌ [initialize] Failed to create messaging instance');
        return;
      }
      logger('✅ [initialize] Successfully created messaging instance');
    }

    try {
      logger('🔍 [initialize] Step 1: Printing FCM token');
      _printFcmToken();

      logger('🔍 [initialize] Step 2: Requesting push permissions');
      final permissionGranted = await requestPushPermissions();
      logger('🔍 [initialize] Permission granted: $permissionGranted');

      logger('🔍 [initialize] Step 3: Initializing local notifications');
      await _initLocalNotifications();

      logger('🔍 [initialize] Step 4: Initializing listeners');
      await _initListeners();

      logger('🔍 [initialize] Step 5: Setting up token refresh listener');
      await _setupTokenRefreshListener();

      logger('🔍 [initialize] Step 6: Refreshing token on startup');
      await _refreshTokenOnStartup();

      logger('🔍 [initialize] Step 7: Checking for initial message');
      // Handle app launch from a terminated state
      final initialMessage = await _messaging!.getInitialMessage();
      if (initialMessage != null) {
        logger(
            '📱 [initialize] Initial message found: ${initialMessage.messageId}');
        _handleNotificationTap(initialMessage, isTerminated: true);
      } else {
        logger('🔍 [initialize] No initial message found');
      }

      logger('✅ [initialize] Firebase messaging setup completed successfully');
    } catch (e) {
      logger('❌ [initialize] Error initializing push notifications: $e');
      logger('❌ [initialize] Error type: ${e.runtimeType}');
    }
  }

  static FirebaseMessaging? _getFirebaseMessaging() {
    try {
      logger('🔍 [_getFirebaseMessaging] Checking Firebase apps...');
      logger(
          '🔍 [_getFirebaseMessaging] Firebase.apps.isNotEmpty: ${Firebase.apps.isNotEmpty}');
      logger(
          '🔍 [_getFirebaseMessaging] Firebase.apps.length: ${Firebase.apps.length}');

      if (Firebase.apps.isNotEmpty) {
        logger('✅ [_getFirebaseMessaging] Creating FirebaseMessaging.instance');
        final messaging = FirebaseMessaging.instance;
        logger(
            '✅ [_getFirebaseMessaging] FirebaseMessaging.instance created successfully');
        return messaging;
      } else {
        logger('❌ [_getFirebaseMessaging] No Firebase apps available');
        return null;
      }
    } catch (e) {
      logger(
          '❌ [_getFirebaseMessaging] Firebase not available for messaging: $e');
      logger('❌ [_getFirebaseMessaging] Error type: ${e.runtimeType}');
      return null;
    }
  }

  static Future<bool> requestPushPermissions() async {
    if (_messaging == null) {
      logger('❌ [requestPushPermissions] Messaging instance is null');
      return false;
    }

    try {
      logger(
          '🔍 [requestPushPermissions] Requesting permissions (alert: true, badge: true, sound: true)...');
      final settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      logger('📱 [requestPushPermissions] Permission result:');
      logger(
          '📱 [requestPushPermissions] - Authorization Status: ${settings.authorizationStatus}');
      logger('📱 [requestPushPermissions] - Alert Setting: ${settings.alert}');
      logger('📱 [requestPushPermissions] - Badge Setting: ${settings.badge}');
      logger('📱 [requestPushPermissions] - Sound Setting: ${settings.sound}');
      logger(
          '📱 [requestPushPermissions] - Critical Alert Setting: ${settings.criticalAlert}');

      final isAuthorized =
          settings.authorizationStatus == AuthorizationStatus.authorized;
      logger(
          '${isAuthorized ? '✅' : '❌'} [requestPushPermissions] Permission granted: $isAuthorized');

      return isAuthorized;
    } catch (e) {
      logger(
          '❌ [requestPushPermissions] Error requesting push permissions: $e');
      logger('❌ [requestPushPermissions] Error type: ${e.runtimeType}');
      return false;
    }
  }

  static Future<void> _initLocalNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true);
    const initSettings =
        InitializationSettings(android: androidSettings, iOS: iosSettings);

    await _localNotificationsPlugin.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (response) =>
          _handleNotificationTap(null),
    );
  }

  static Future<void> _initListeners() async {
    if (_messaging == null) return;

    // On iOS, enable system notifications in foreground to avoid duplicates
    // On Android, disable system notifications and use local notifications only
    if (Platform.isIOS) {
      await _messaging!.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );
    } else {
      await _messaging!.setForegroundNotificationPresentationOptions(
        alert: false,
        badge: false,
        sound: false,
      );
    }

    // App is in the background and user taps the notification.
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      _handleNotificationTap(message, isBackground: true);
    });

    // App is in the foreground when a notification arrives.
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      logger('📱 [Foreground] Received message: ${message.messageId}');

      final notificationContent = await processRemoteMessage(
        message: message,
        dataManager: sl<DataManager>(),
        isForeground: true, // Notify UI to update
      );

      // On iOS, system handles foreground notifications, so skip local notification
      // On Android, create local notification since system notifications are disabled
      if (!Platform.isIOS) {
        await createPush(
          title: notificationContent['title']!,
          body: notificationContent['body']!,
        );
      }
    });
  }

  /// Centralized logic to process a message payload.
  /// This can be called from both foreground and background handlers.
  static Future<Map<String, String>> processRemoteMessage({
    required RemoteMessage message,
    required DataManager dataManager,
    bool isForeground = false,
  }) async {
    final data = message.data;
    final notification = message.notification;

    final String title = data['message'] ?? notification?.title ?? "StoreTrack";
    final String body =
        data['context'] ?? notification?.body ?? "You have a new notification!";

    if (data['type']?.toLowerCase() == 'alert') {
      try {
        final currentCount = await dataManager.getAlertCount();
        await dataManager.saveAlertCount(currentCount + 1);
        logger('📊 Alert count incremented to: ${currentCount + 1}');

        if (isForeground) {
          sl<AlertCountCubit>().loadAlertCount();
          logger('🔄 UI notified of alert count change');
        }
      } catch (e) {
        logger('⚠️ Error incrementing alert count: $e');
      }
    }

    return {'title': title, 'body': body};
  }

  /// Centralized handler for any action that should result from tapping a notification.
  static void _handleNotificationTap(RemoteMessage? message,
      {bool isTerminated = false, bool isBackground = false}) {
    String state = isTerminated
        ? "terminated"
        : (isBackground ? "background" : "foreground");
    logger('🔔 Notification tapped while app was in $state.');
    _navigateToNotifications();
  }

  static Future<void> createPush(
      {required String title, required String body}) async {
    try {
      final bool notificationEnabled =
          await sl<DataManager>().getNotificationEnabled();
      if (!notificationEnabled) {
        logger('Notifications disabled by user, skipping.');
        return;
      }
      _localNotificationsPlugin.show(
        Random().nextInt(100000),
        title,
        body,
        _platformNotificationDetails,
      );
    } catch (e) {
      logger('Error showing local notification: $e');
    }
  }

  static void _navigateToNotifications() {
    try {
      sl<AppRouter>().replaceAll(
        [
          HomeRoute(children: [
            DashboardHolderRoute(children: [
              DashboardRoute(skipSync: true),
              const NotificationsRoute(),
            ]),
          ]),
        ],
        updateExistingRoutes: false,
      );
      logger('🔔 Navigated to notifications page.');
    } catch (e) {
      logger('❌ Error navigating to notifications page: $e');
    }
  }

  static Future<String?> getToken() async {
    logger('🔍 [getToken] Starting FCM token retrieval');
    logger('🔍 [getToken] Platform: ${Platform.operatingSystem}');
    logger('🔍 [getToken] Is iOS: ${Platform.isIOS}');

    // Check Firebase apps status
    try {
      logger('🔍 [getToken] Firebase apps available: ${Firebase.apps.length}');
      for (var app in Firebase.apps) {
        logger(
            '🔍 [getToken] Firebase app: ${app.name}, options: ${app.options.projectId}');
        logger(
            '🔍 [getToken] Firebase app bundle ID: ${app.options.iosBundleId}');
        logger(
            '🔍 [getToken] Firebase app client ID: ${app.options.iosClientId}');
      }
    } catch (e) {
      logger('❌ [getToken] Error checking Firebase apps: $e');
    }

    // Check messaging instance
    if (_messaging == null) {
      logger('❌ [getToken] FirebaseMessaging instance is null');
      logger('🔍 [getToken] Attempting to get messaging instance...');
      _messaging = _getFirebaseMessaging();
      if (_messaging == null) {
        logger('❌ [getToken] Failed to get FirebaseMessaging instance');
        return null;
      }
      logger('✅ [getToken] Successfully got FirebaseMessaging instance');
    }

    try {
      logger('🔍 [getToken] Requesting FCM token from FirebaseMessaging...');
      final token = await _messaging!.getToken();

      if (token != null) {
        logger(
            '✅ [getToken] FCM token retrieved successfully: ${token.substring(0, 20)}...');
        await sl<DataManager>().saveFcmToken(token);
        logger('✅ [getToken] FCM token saved to DataManager');
      } else {
        logger('❌ [getToken] FCM token is null');

        // Check authorization status
        try {
          final settings = await _messaging!.getNotificationSettings();
          logger(
              '🔍 [getToken] Notification settings - Authorization: ${settings.authorizationStatus}');
          logger(
              '🔍 [getToken] Notification settings - Alert: ${settings.alert}');
          logger(
              '🔍 [getToken] Notification settings - Badge: ${settings.badge}');
          logger(
              '🔍 [getToken] Notification settings - Sound: ${settings.sound}');
        } catch (settingsError) {
          logger(
              '❌ [getToken] Error getting notification settings: $settingsError');
        }
      }

      return token;
    } catch (e) {
      logger('❌ [getToken] Error getting FCM token: $e');
      logger('❌ [getToken] Error type: ${e.runtimeType}');
      if (e is StateError) {
        logger('❌ [getToken] StateError details: ${e.message}');
      }
      return null;
    }
  }

  static Future<void> _printFcmToken() async {
    final token = await getToken();
    logger('FCM Token: ${token.toString()}');
  }

  static Future<void> _setupTokenRefreshListener() async {
    if (_messaging == null) return;
    _messaging!.onTokenRefresh.listen((fcmToken) async {
      logger('🔄 FCM Token refreshed: $fcmToken');
      final storedToken = await sl<DataManager>().getFcmToken();
      if (storedToken != fcmToken) {
        await updateDeviceToken(fcmToken);
      }
    }).onError((e) => logger('❌ Error in token refresh listener: $e'));
  }

  static Future<void> _refreshTokenOnStartup() async {
    try {
      final currentToken = await getToken();
      if (currentToken != null && await sl<DataManager>().getUserId() != null) {
        await updateDeviceToken(currentToken);
      }
    } catch (e) {
      logger('⚠️ Error refreshing token on startup: $e');
    }
  }

  static Future<void> updateDeviceToken(String newDeviceToken) async {
    try {
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId();
      final fcmToken = await getToken();

      if (userId == null || fcmToken == null) {
        logger('❌ Cannot update token: missing user ID or FCM token.');
        return;
      }

      final deviceInfo = sl<DeviceInfoService>();
      final appInfoService = sl<AppInfoService>();

      final requestBody = {
        'user_id': userId,
        'token': fcmToken,
        'device_uid': await dataManager.getOrCreateDeviceId(),
        'device_token': newDeviceToken,
        'device_platform': deviceInfo.getDevicePlatform(),
        'appversion': await appInfoService.getVersion(),
      };

      logger('🔄 Updating device token on server...');
      final response = await sl<ApiClient>().instance.post(
            '/update_device_token',
            data: requestBody,
          );

      if (response.statusCode == 200) {
        logger('✅ Device token updated successfully');
      } else {
        logger('❌ Device token update failed: ${response.statusCode}');
      }
    } catch (e) {
      logger('❌ Error updating device token: $e');
    }
  }
}
