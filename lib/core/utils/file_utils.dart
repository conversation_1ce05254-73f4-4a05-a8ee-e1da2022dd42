class FileUtils {
  FileUtils._();

  static String getFileNameFromUrl(String fileUrl) {
    if (fileUrl.isEmpty) return 'Unnamed File';

    // Special handling for YouTube URLs
    if (fileUrl.contains('youtube.com') || fileUrl.contains('youtu.be')) {
      // Extract video ID for YouTube URLs
      String videoId = '';

      if (fileUrl.contains('youtu.be/')) {
        // Format: https://youtu.be/VIDEO_ID
        final parts = fileUrl.split('youtu.be/');
        if (parts.length > 1) {
          videoId = parts[1].split('?').first; // Remove query parameters
        }
      } else if (fileUrl.contains('youtube.com')) {
        // Format: https://www.youtube.com/watch?v=VIDEO_ID
        final uri = Uri.tryParse(fileUrl);
        if (uri != null) {
          videoId = uri.queryParameters['v'] ?? '';
        }
      }

      return videoId.isNotEmpty ? 'youtube.com/$videoId' : fileUrl;
    }

    // For other URLs, extract filename after last '/' and remove query parameters
    final url = fileUrl.split('?').first; // Remove query parameters
    final segments = url.split('/');

    if (segments.isNotEmpty) {
      final fileName = segments.last;
      if (fileName.isNotEmpty) {
        return fileName;
      }
    }

    return 'Unnamed File';
  }
}
