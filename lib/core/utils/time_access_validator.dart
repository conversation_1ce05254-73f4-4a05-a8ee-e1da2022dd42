import 'package:flutter/material.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/datasources/home_local_datasource.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';

class TimeAccessValidator {
  /// Validates if a task can be accessed based on early/late hour settings
  /// Returns true if access is allowed, false if blocked
  /// Shows appropriate dialog when access is denied
  /// [skipDialog] - if true, skips showing dialogs (useful for reopened tasks)
  static Future<bool> canAccessTask(BuildContext context, TaskDetail task,
      {bool skipDialog = false}) async {
    try {
      // Get MiscSetting from database
      final homeLocalDataSource = sl<HomeLocalDataSource>();
      final miscSettingResponse = await homeLocalDataSource.getMiscSetting();

      // If no settings found, allow access (default behavior)
      if (miscSettingResponse?.data == null) {
        logger('canAccessTask: No misc setting found, allowing access');
        return true;
      }

      final miscSetting = miscSettingResponse!.data;

      logger(
          'canAccessTask: ${miscSetting.reportEarlyHours} ${miscSetting.reportLateHours}');

      // Parse early and late hours (stored as strings)
      final earlyHours = miscSetting.reportEarlyHours;
      final lateHours = miscSetting.reportLateHours;

      // If no time restrictions, allow access
      if (earlyHours == 0 && lateHours == 0) {
        return true;
      }

      // Get current date and task scheduled date (ignoring time)
      final now = DateTime.now();
      final currentDate = DateTime(now.year, now.month, now.day);
      final scheduledTime = task.scheduledTimeStamp;

      // If task has no scheduled time, allow access
      if (scheduledTime == null) {
        return true;
      }

      final scheduledDate =
          DateTime(scheduledTime.year, scheduledTime.month, scheduledTime.day);

      // Calculate day-based windows (convert hours to days)
      final earlyDays = (earlyHours / 24).ceil();
      final lateDays = (lateHours / 24).ceil();

      final earlyAccessStartDate =
          scheduledDate.subtract(Duration(days: earlyDays));
      final lateAccessEndDate = scheduledDate.add(Duration(days: lateDays));

      // Check if current date is within allowed window
      if (currentDate.isBefore(earlyAccessStartDate)) {
        // Too early - show dialog only if not skipped
        if (!skipDialog && context.mounted) {
          ConfirmDialog.show(
            context: context,
            title: 'Task not available',
            message: 'This task will be available on the day it is scheduled',
            confirmText: 'OK',
            cancelText: '',
            onConfirm: () {}, // Just close dialog
          );
        }
        return false;
      }

      if (currentDate.isAfter(lateAccessEndDate)) {
        // Too late - show dialog only if not skipped
        if (!skipDialog && context.mounted) {
          ConfirmDialog.show(
            context: context,
            title: 'Task no longer available',
            message:
                'Only tasks scheduled in the last $lateDays days can be opened',
            confirmText: 'OK',
            cancelText: '',
            onConfirm: () {}, // Just close dialog
          );
        }
        return false;
      }

      // Within allowed time window
      return true;
    } catch (e) {
      // On any error, allow access (fail-safe behavior)
      return true;
    }
  }
}
