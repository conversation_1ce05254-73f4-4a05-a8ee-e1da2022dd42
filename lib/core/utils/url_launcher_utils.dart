import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;
import 'package:auto_route/auto_route.dart';
import '../../config/routes/app_router.gr.dart';
import 'snackbar_service.dart';
import 'logger.dart';

class UrlLauncherUtils {
  /// Launches Google Maps with the specified coordinates
  ///
  /// Attempts to open the native Maps app first, falls back to WebBrowser if needed
  static Future<void> launchGoogleMaps({
    required BuildContext context,
    required double latitude,
    required double longitude,
  }) async {
    logger(
        'UrlLauncherUtils.launchGoogleMaps called with lat: $latitude, lng: $longitude');

    try {
      // Validate coordinates
      if (latitude == 0.0 && longitude == 0.0) {
        logger(
            'UrlLauncherUtils.launchGoogleMaps: Invalid coordinates (0.0, 0.0)');
        SnackBarService.warning(
          context: context,
          message: 'Location coordinates not available for this task.',
        );
        return;
      }

      final googleMapsUrl =
          'https://maps.google.com/maps?daddr=$latitude,$longitude';
      final Uri uri = Uri.parse(googleMapsUrl);
      logger(
          'UrlLauncherUtils.launchGoogleMaps: Generated URL: $googleMapsUrl');

      // Try to launch with external app (native Maps app)
      if (await url_launcher.canLaunchUrl(uri)) {
        logger(
            'UrlLauncherUtils.launchGoogleMaps: Launching with external app');
        await url_launcher.launchUrl(uri,
            mode: url_launcher.LaunchMode.externalApplication);
        logger(
            'UrlLauncherUtils.launchGoogleMaps: Successfully launched with external app');
      } else {
        logger(
            'UrlLauncherUtils.launchGoogleMaps: Cannot launch with external app, falling back to WebBrowser');
        // Fallback to WebBrowser if external launch fails
        if (context.mounted) {
          context.router
              .push(WebBrowserRoute(url: googleMapsUrl, title: 'Google Maps'));
          logger(
              'UrlLauncherUtils.launchGoogleMaps: Launched WebBrowser fallback');
        }
      }
    } catch (e) {
      logger(
          'UrlLauncherUtils.launchGoogleMaps: Error occurred: $e, falling back to WebBrowser');
      // Fallback to WebBrowser on any error
      if (context.mounted) {
        final googleMapsUrl =
            'https://maps.google.com/maps?daddr=$latitude,$longitude';
        context.router
            .push(WebBrowserRoute(url: googleMapsUrl, title: 'Google Maps'));
        logger(
            'UrlLauncherUtils.launchGoogleMaps: Error fallback WebBrowser launched');
      }
    }
  }

  /// Launches any URL with external app preference and WebBrowser fallback
  ///
  /// Generic method for launching URLs with consistent fallback behavior
  static Future<void> launchUrl({
    required BuildContext context,
    required String url,
    String? title,
  }) async {
    logger('UrlLauncherUtils.launchUrl called with URL: $url, title: $title');

    try {
      final Uri uri = Uri.parse(url);
      logger('UrlLauncherUtils.launchUrl: Parsed URI: $uri');

      // Try to launch with external app
      if (await url_launcher.canLaunchUrl(uri)) {
        logger('UrlLauncherUtils.launchUrl: Launching with external app');
        await url_launcher.launchUrl(uri,
            mode: url_launcher.LaunchMode.externalApplication);
        logger(
            'UrlLauncherUtils.launchUrl: Successfully launched with external app');
      } else {
        logger(
            'UrlLauncherUtils.launchUrl: Cannot launch with external app, falling back to WebBrowser');
        // Fallback to WebBrowser if external launch fails
        if (context.mounted) {
          context.router
              .push(WebBrowserRoute(url: url, title: title ?? 'Link'));
          logger('UrlLauncherUtils.launchUrl: Launched WebBrowser fallback');
        }
      }
    } catch (e) {
      logger(
          'UrlLauncherUtils.launchUrl: Error occurred: $e, falling back to WebBrowser');
      // Fallback to WebBrowser on any error
      if (context.mounted) {
        context.router.push(WebBrowserRoute(url: url, title: title ?? 'Link'));
        logger(
            'UrlLauncherUtils.launchUrl: Error fallback WebBrowser launched');
      }
    }
  }
}
