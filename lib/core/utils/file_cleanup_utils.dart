import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:storetrack_app/core/utils/logger.dart';

class FileCleanupUtils {
  FileCleanupUtils._();

  static Future<void> clearLocalFiles() async {
    try {
      await Future.wait([
        _clearImagesDirectory(),
        _clearFilesDirectory(),
        _clearTempDirectory(),
      ]);
      logger('Successfully cleared all local files');
    } catch (e) {
      logger('Error clearing local files: $e');
      throw Exception('Failed to clear local files: $e');
    }
  }

  static Future<void> _clearImagesDirectory() async {
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory(path.join(appDocDir.path, 'images'));

      if (await imagesDir.exists()) {
        await for (final entity in imagesDir.list()) {
          if (entity is File) {
            await entity.delete();
            logger('Deleted image file: ${entity.path}');
          }
        }
        logger('Cleared images directory');
      }
    } catch (e) {
      logger('Error clearing images directory: $e');
      throw Exception('Failed to clear images directory: $e');
    }
  }

  static Future<void> _clearFilesDirectory() async {
    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final filesDir = Directory(path.join(appDocDir.path, 'files'));

      if (await filesDir.exists()) {
        await for (final entity in filesDir.list(recursive: true)) {
          if (entity is File) {
            await entity.delete();
            logger('Deleted file: ${entity.path}');
          }
        }
        logger('Cleared files directory');
      }
    } catch (e) {
      logger('Error clearing files directory: $e');
      throw Exception('Failed to clear files directory: $e');
    }
  }

  static Future<void> _clearTempDirectory() async {
    try {
      final tempDir = await getTemporaryDirectory();

      if (await tempDir.exists()) {
        await for (final entity in tempDir.list()) {
          if (entity is File) {
            await entity.delete();
            logger('Deleted temp file: ${entity.path}');
          }
        }
        logger('Cleared temp directory');
      }
    } catch (e) {
      logger('Error clearing temp directory: $e');
      throw Exception('Failed to clear temp directory: $e');
    }
  }
}
