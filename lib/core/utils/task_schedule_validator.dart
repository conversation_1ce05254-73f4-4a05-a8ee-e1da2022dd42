import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// A utility class for validating task scheduling dates based on task constraints.
///
/// Implements the scheduling logic with the following constraints:
/// - Date must not be in the past (must be today or future)
/// - If task has range constraints, date must be within range (rangeStart to rangeEnd)
/// - If task has expiry, date must be before or equal to expiry date
/// - All applicable constraints must be satisfied for valid scheduling
class TaskScheduleValidator {
  // Private constructor to prevent instantiation
  TaskScheduleValidator._();

  /// Validates if a selected date is valid for scheduling a specific task.
  ///
  /// Validates that:
  /// - For extended tasks (expires > rangeEnd): validate rangeStart <= selectedDate <= expiry
  /// - For regular tasks: validate rangeStart <= selectedDate <= rangeEnd AND selectedDate <= expiry
  /// - All applicable constraints must be satisfied
  ///
  /// [selectedDate]: The date to validate for scheduling
  /// [task]: The task to validate against
  ///
  /// Returns `true` if the date is valid for scheduling, `false` otherwise.
  static bool isDateValidForScheduling(DateTime selectedDate, TaskDetail task) {
    final rangeStart = task.rangeStart;
    final rangeEnd = task.rangeEnd;
    final expires = task.expires;

    // Strip time components for date-only comparison
    final selectedDateOnly =
        DateTime(selectedDate.year, selectedDate.month, selectedDate.day);

    // Check if this is an extended task
    final isExtended = _isTaskExtended(task);

    // Check range start constraint (applies to both extended and regular tasks)
    bool afterRangeStart = true; // Default to true if no range start constraint
    if (rangeStart != null) {
      final rangeStartOnly =
          DateTime(rangeStart.year, rangeStart.month, rangeStart.day);
      afterRangeStart = selectedDateOnly.isAfter(rangeStartOnly) ||
          selectedDateOnly.isAtSameMomentAs(rangeStartOnly);
    }

    if (isExtended) {
      // For extended tasks, validate rangeStart <= selectedDate <= expiry
      bool beforeExpiry = true; // Default to true if no expiry constraint
      if (expires != null) {
        final expiresOnly = DateTime(expires.year, expires.month, expires.day);
        beforeExpiry = selectedDateOnly.isBefore(expiresOnly) ||
            selectedDateOnly.isAtSameMomentAs(expiresOnly);
      }
      return afterRangeStart && beforeExpiry;
    } else {
      // For regular tasks, validate rangeStart <= selectedDate <= rangeEnd AND selectedDate <= expiry
      bool beforeRangeEnd = true; // Default to true if no range end constraint
      if (rangeEnd != null) {
        final rangeEndOnly =
            DateTime(rangeEnd.year, rangeEnd.month, rangeEnd.day);
        beforeRangeEnd = selectedDateOnly.isBefore(rangeEndOnly) ||
            selectedDateOnly.isAtSameMomentAs(rangeEndOnly);
      }

      bool beforeExpiry = true; // Default to true if no expiry constraint
      if (expires != null) {
        final expiresOnly = DateTime(expires.year, expires.month, expires.day);
        beforeExpiry = selectedDateOnly.isBefore(expiresOnly) ||
            selectedDateOnly.isAtSameMomentAs(expiresOnly);
      }

      return afterRangeStart && beforeRangeEnd && beforeExpiry;
    }
  }

  /// Gets a list of tasks that are invalid for the selected date.
  ///
  /// [selectedDate]: The date to validate against
  /// [tasks]: List of tasks to validate
  ///
  /// Returns a list of tasks that cannot be scheduled on the selected date.
  static List<TaskDetail> getInvalidTasksForDate(
      DateTime selectedDate, List<TaskDetail> tasks) {
    return tasks
        .where((task) => !isDateValidForScheduling(selectedDate, task))
        .toList();
  }

  /// Gets a list of tasks that are valid for the selected date.
  ///
  /// [selectedDate]: The date to validate against
  /// [tasks]: List of tasks to validate
  ///
  /// Returns a list of tasks that can be scheduled on the selected date.
  static List<TaskDetail> getValidTasksForDate(
      DateTime selectedDate, List<TaskDetail> tasks) {
    return tasks
        .where((task) => isDateValidForScheduling(selectedDate, task))
        .toList();
  }

  /// Creates a human-readable description of why a task cannot be scheduled on a date.
  ///
  /// [selectedDate]: The date that was selected
  /// [task]: The task that failed validation
  ///
  /// Returns a descriptive string explaining the validation failure.
  static String getValidationFailureReason(
      DateTime selectedDate, TaskDetail task) {
    final rangeStart = task.rangeStart;
    final rangeEnd = task.rangeEnd;
    final expires = task.expires;
    final storeName = task.storeName ?? 'Unknown Store';

    final selectedDateOnly =
        DateTime(selectedDate.year, selectedDate.month, selectedDate.day);

    List<String> reasons = [];

    final isExtended = _isTaskExtended(task);

    // Check range start constraint (applies to both extended and regular tasks)
    if (rangeStart != null) {
      final rangeStartOnly =
          DateTime(rangeStart.year, rangeStart.month, rangeStart.day);
      if (selectedDateOnly.isBefore(rangeStartOnly)) {
        reasons.add(
            'Selected date is before task range start (${_formatDate(rangeStart)})');
      }
    }

    if (isExtended) {
      // For extended tasks, only check expiry constraint (not range end)
      if (expires != null) {
        final expiresOnly = DateTime(expires.year, expires.month, expires.day);
        if (selectedDateOnly.isAfter(expiresOnly)) {
          reasons.add(
              'Selected date is after extended task expiry (${_formatDate(expires)})');
        }
      }
    } else {
      // For regular tasks, check range end constraint
      if (rangeEnd != null) {
        final rangeEndOnly =
            DateTime(rangeEnd.year, rangeEnd.month, rangeEnd.day);
        if (selectedDateOnly.isAfter(rangeEndOnly)) {
          reasons.add(
              'Selected date is after task range end (${_formatDate(rangeEnd)})');
        }
      }

      // Check expiry constraint for regular tasks
      if (expires != null) {
        final expiresOnly = DateTime(expires.year, expires.month, expires.day);
        if (selectedDateOnly.isAfter(expiresOnly)) {
          reasons.add(
              'Selected date is after task expiry (${_formatDate(expires)})');
        }
      }
    }

    if (reasons.isEmpty) {
      reasons.add('Date validation failed');
    }

    return '$storeName: ${reasons.join(', ')}';
  }

  /// Determines if a task is extended by checking if expiry date is after range end date.
  ///
  /// A task is considered extended if:
  /// - Both expires and rangeEnd dates are not null
  /// - The task's expiry date is after the range end date
  ///
  /// [task]: The task to check for extension
  ///
  /// Returns `true` if the task is extended, `false` otherwise.
  static bool _isTaskExtended(TaskDetail task) {
    final DateTime? expires = task.expires;
    final DateTime? rangeEnd = task.rangeEnd;

    if (expires != null && rangeEnd != null) {
      return expires.isAfter(rangeEnd);
    }

    return false;
  }

  /// Formats a DateTime for user-friendly display.
  static String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}
