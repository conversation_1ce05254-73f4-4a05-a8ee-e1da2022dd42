  if (isUserActive) {
                        //mo, 2/12/16 address/skills error will be checked first
                        if (app_other_warning && StorageManager.getAutoScheduleFromSharedPreference(getContext())) {
                            //if app never synced, we postpone Address/Skills validation.
                            if (FragmentBase.isNeverSyncedBefore()) {
                                continueSyncWrite();
                            } else {
                                stopSyncing();  //hide progress bar etc
                                kickOutUserWithCustomMessage(warning_message, warning_title, kick_out_user);
                            }
                        } else if (app_old_version && !grace_period) {
                            //it's not grace period, should i kick out user as well?
                            if (kick_out_user) {
                                //kick out user warning message.
                                kickOutUserWithCustomMessage(warning_message, warning_title, true);
                            } else {
                                //just show warning, continue sync
                                continueSyncWrite();
                                kickOutUserWithCustomMessage(warning_message, warning_title, false);
                            }
                        } else {
                            //(new version) or (old version but it is grace period) >> do nothing >> continue synchronisation
                            continueSyncWrite();
                        }
                    } else {
                        //user is inactive >> org code to kick out user
                        kickOutUser();
                    }


                    public void kickOutUser() {
        // Kick user out (user is inactive)
        CommonFunction.getInstance(getContext()).hideProgressIndicator();
        CommonFunction.getInstance(getContext()).dismissAllAlertDialog();
        hideTaskLastUpdateInformation();

        API.getInstance(getContext()).cancelAllRequest();

        StorageManager.clearUserRelatedData(getContext());
        DatabaseManager.getInstance(getContext()).clearUserRelatedData(ActivityBase.mRealm);

        // Stopping data usage service
        Intent dataUsageService = new Intent(getContext(), DataUsageService.class);
        getActivity().stopService(dataUsageService);

        // Post delaying to prevent IllegalStateException after onSavedInstance
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                replaceFragment(new InitialLoginFragment(), FragmentBase.SLIDE_IN_UP_ANIMATION_KEY);
            }
        }, Constant.FRAGMENT_COMMIT_DELAY);

        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("Invalid User Session");
        builder.setCancelable(false);
        builder.setMessage("You have been logged out as we have detected multiple user session on a similar device.");
//                        builder.setMessage("You have been logged out because of multiple user session on a similar device or other reasons.");

        builder.setPositiveButton(R.string.OK, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {
            }
        });

        builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
            }
        });

        AlertDialog alertDialog = builder.create();
        alertDialog.show();

        CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);

        isSynchronising = false;
        rootActivity.signOut();
    }

      public void kickOutUserWithCustomMessage(String wMessage, final String wTitle, boolean kicout) {
        if (kicout) {
            //(a) all process related to the kick out
            // Kick user out (user is inactive)
            CommonFunction.getInstance(getContext()).hideProgressIndicator();
            CommonFunction.getInstance(getContext()).dismissAllAlertDialog();
            hideTaskLastUpdateInformation();

            API.getInstance(getContext()).cancelAllRequest();

            StorageManager.clearUserRelatedData(getContext());
            DatabaseManager.getInstance(getContext()).clearUserRelatedData(ActivityBase.mRealm);

            // Stopping data usage service
            Intent dataUsageService = new Intent(getContext(), DataUsageService.class);
            getActivity().stopService(dataUsageService);

            // Post delaying to prevent IllegalStateException after onSavedInstance
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    replaceFragment(new InitialLoginFragment(), FragmentBase.SLIDE_IN_UP_ANIMATION_KEY);
                }
            }, Constant.FRAGMENT_COMMIT_DELAY);


            isSynchronising = false;
            //Sign Out MSAL
            rootActivity.signOut();
        }

        //(b) show alert
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle(wTitle);
        builder.setCancelable(false);
        builder.setMessage(wMessage);

        //error message ok button clicked.
        builder.setPositiveButton(R.string.OK, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {
                CommonFunction.print("Ok button clicked, wTitle:" + wTitle);

                //check if address/skills set up. In Android, you must sync at least once before validating Address/Skills. Otherwise, local database will not be ready and show nothing in Edit Profile and Skills.
                checkAddressSkillsValidation();
            }
        });

        builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                CommonFunction.getInstance(getContext()).dismissAlertDialog((AlertDialog) dialog);
            }
        });

        AlertDialog alertDialog = builder.create();
        alertDialog.show();

        CommonFunction.getInstance(getContext()).recordShownAlertDialog(alertDialog);


    }

       public void checkAddressSkillsValidation() {

        boolean address_skills_validated = DatabaseManager.getInstance(getContext()).getAddressSkillsValidated();
        boolean isShowAutoSchedule = StorageManager.getAutoScheduleFromSharedPreference(getContext());
        if (!address_skills_validated && isShowAutoSchedule) {
            redirect2ProfileTab();
        }
    }