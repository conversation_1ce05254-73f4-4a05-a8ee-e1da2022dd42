req
---
{
  "device_uid": "8b7a6774c878a206",
  "user_id": "64366",
  "device_platform": "Android",
  "appversion": "10.0.3",
  "token": "dUwKTcueQ7WnQYAiJvC2xB:APA91bH1ScGl1kD17drWPcEEgDXaq8Gq55-28P7IB5ZFuxhj8WXJ7PjmsuByDjEq3FtuKjilbEaYiICwxLJ-6JZI5tlZ3WEWsebeinnH4NeyM0gM_nVAlsU"
}

res
---
{
  "data": {
    "active": true,
    "app_old_version": false,
    "grace_period": false,
    "support_number": "02 94320882",
    "warning_message": "",
    "warning_title": "",
    "kick_out_user": false,
    "app_other_warning": false,
    "warning_message_id": 0,
    "latest_version": "9.9.9",
    "admin_access": false,
    "country_id": 14,
    "create_task": true
  }
}

api
---
POST
https://webservice2.storetrack.com.au/api/user_state_available

instructions
------------
create UserStateRequestEntity
create UserStateResponseEntity
