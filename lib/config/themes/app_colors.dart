import 'package:flutter/material.dart';

class AppColors {
  static const Color lightGrey1 = Color(0xFFF1F3F5);
  // Used in text
  static const Color black = Color(0xFF0E1D29);
  static Color black10 = black.withValues(alpha: 0.1);
  static Color black20 = black.withValues(alpha: 0.2);

  // Used in bottom navigation inactive
  static Color black60 = black.withValues(alpha: 0.6);

  // Used below appbar
  static Color appBarBorderBlack = Colors.black.withValues(alpha: 0.3);

  // Used in text especially time eg - 60min and in floatingbar icon
  static const Color blackTint1 = Color(0xFF6E777F);

  // Used in list dividers and in card border
  static const Color blackTint2 = Color(0xFFCFD2D4);
  static const Color borderColor = Color(0xFFE5E5EA);

  // Used in alert card text
  static const Color darkOrange50 = Color(0xFF7A3613);
  static const Color darkTextColor = Color(0xFF1D1D1F);
  static const Color lightBlue = Color(0xFF00BBF0);
  // Used in background of pages and in alert card bg
  static const Color lightGrey2 = Color(0xFFEEF1F3);

  // Used in floating action button bg
  static const Color midGrey = Color(0xFFB4C3CE);

  // Used as primary color
  static const Color primaryBlue = Color(0xFF0079C0);

  // Used in alert card icon
  static const Color richOrange = Color(0xFFF46B26);
  // Used in alert card background
  static Color richOrange15 = richOrange.withValues(alpha: 0.15);
  static const Color secondaryTextColor = Color(0xFF6E6E73);
  static const Color warningBgColor = Color(0xFFFFF4E5);
  static const Color warningTextColor = Color(0xFFFF9500);

  // Used in alert card text
  static const Color darkYellow50 = Color(0xFF806107);

  // Used in alert card icon
  static const Color darkYellow15 = Color(0xFFCC9A0B);

  // Used in alert card background
  static Color richYellow15 = const Color(0xFFFFC10E).withValues(alpha: 0.15);

  // Used in snack bar background
  static const Color green15 = Color(0xFFDBF4EF);
  // Used in snack bar text
  static const Color green50 = Color(0xFF085B4A);

  // Login red
  static const Color loginRed = Color(0xFFDE3814);

  // Login green
  static const Color loginGreen = Color(0xFF339A63);

  // Border black
  static const Color borderBlack = Color(0xFFE7E8EA);

  static const Color completedGreenDark = Color(0xFFDBF4EF);
  static const Color completedGreenLight = Color(0xFFF1FbF9);

  //////
  var urgentIcon = AppColors.richOrange;
  var urgentBg = AppColors.richOrange15;
  var urgentText = AppColors.darkOrange50;

  var moneyIcon = AppColors.darkYellow15;
  var moneyBg = AppColors.richYellow15;
  var moneyText = AppColors.darkYellow50;

  var messageIcon = AppColors.black;
  var messageBg = AppColors.lightGrey2;
  var messageText = AppColors.black;
  //////
}
