package com.au.storetrack.Utilities.PushNofitication;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.TaskStackBuilder;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import android.provider.Settings;
import android.util.Log;

import com.au.storetrack.Activities.ActivityBase;
import com.au.storetrack.Model.EventModel.TabBarBadgeEvent;
import com.au.storetrack.R;
import com.au.storetrack.Utilities.CommonFunction;
import com.au.storetrack.Utilities.Constant;
import com.au.storetrack.Utilities.Storage.StorageManager;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import org.greenrobot.eventbus.EventBus;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;
import java.util.Random;

/**
 * Created by matthewoh on 11/7/18.
 * This file is based on tutorial page
 * https://www.codementor.io/flame3/send-push-notifications-to-android-with-firebase-du10860kb
 *
 * But above tutorial was not enough, below site saved my life.
 * https://www.pluralsight.com/guides/push-notifications-with-firebase-cloud-messaging
 */

public class MyFirebaseMessagingService extends FirebaseMessagingService {
    private static final String TAG = "FCM Service";

    private static final String NOTIFICATION_ID_EXTRA = "notificationId";
    private static final String IMAGE_URL_EXTRA = "imageUrl";
    private static final String ADMIN_CHANNEL_ID ="admin_channel";
    private NotificationManager notificationManager;
    private int pushNotifForAlertData;

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        // TODO: Handle FCM messages here.
        // If the application is in the foreground handle both data and notification messages here.
        // Also if you intend on generating your own notifications as a result of a received FCM
        // message, here is where that should be initiated.
        //Handle messages in foreground as well
        Log.d(TAG, "From: " + remoteMessage.getFrom());


        // Check if message contains a notification payload.
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());
        }


        super.onMessageReceived(remoteMessage);



        //create notification
        //createNotification(remoteMessage.getNotification().getBody());
        int notifID = Constant.ALERT_PUSH_NOTIFICATION_ID;
        myNotification(notifID, remoteMessage);




        //increase badge number
        if ((StorageManager.getPushNotifEnabledStatusFromSharedPreferences(getApplicationContext())) &&
                (!CommonFunction.isEmptyStringField(StorageManager.getActiveUserIDFromSharedPreference(getApplicationContext())))) {

            if (remoteMessage.getData() != null) {
                String pushType = remoteMessage.getData().get("type");

                if (!CommonFunction.isEmptyStringField(pushType)) {

                    if (pushType.equalsIgnoreCase("Alert")) {

                        pushNotifForAlertData = StorageManager.getPushNotifForAlertDataFromSharedPreference(getApplicationContext());
                        pushNotifForAlertData++;

                        StorageManager.savePushNotifForAlertDataToSharedPreferences(getApplicationContext(), pushNotifForAlertData);
                        EventBus.getDefault().post(new TabBarBadgeEvent(pushNotifForAlertData));
                    }
                }
            }
        }
    }

    public int returnPushAlertCount()
    {
        return pushNotifForAlertData;
    }


    public void myNotification(int notifID, RemoteMessage remoteMessage) {

        Map<String, String> data = remoteMessage.getData();
        String alertTitle = data.get("context");


        //setup intent (just open storetrack app)
        pushNotifForAlertData++;
        int requestID = (int) System.currentTimeMillis();

        Intent notificationIntent = new Intent(this,  ActivityBase.class);

        //Nirvik, added this to send alertTitle along with ActivityBase
        notificationIntent.putExtra("alertTitle" , alertTitle);
        PendingIntent fullScreenPendingIntent = null;

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            fullScreenPendingIntent = PendingIntent.getActivity(this, 0,
                    notificationIntent, PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT );
        }else{
            fullScreenPendingIntent = PendingIntent.getActivity(this, 0,
                    notificationIntent, PendingIntent.FLAG_UPDATE_CURRENT );
        }

        notificationIntent.removeExtra("alertTitle");
        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        //Setting up Notification channels for android O and above
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            setupChannels();

        }

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(getApplicationContext(), ADMIN_CHANNEL_ID);
        notificationBuilder.setSmallIcon(R.mipmap.notification_icon);
        notificationBuilder.setColor(getResources().getColor(R.color.activeTextColor));
        notificationBuilder.setContentTitle(remoteMessage.getData().get("message"));
        notificationBuilder.setContentText(remoteMessage.getData().get("context"));
        notificationBuilder.setContentIntent(fullScreenPendingIntent);
        notificationBuilder.setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION));
        notificationBuilder.setAutoCancel(true);
        notificationBuilder.setPriority(NotificationCompat.PRIORITY_HIGH);
        notificationBuilder.setDefaults(Notification.DEFAULT_ALL);
        notificationBuilder.setFullScreenIntent(fullScreenPendingIntent, true);
        notificationManager.notify(requestID /* ID of notification */, notificationBuilder.build());



    }


    @RequiresApi(api = Build.VERSION_CODES.O)
    private void setupChannels(){
        CharSequence adminChannelName = "Storetrack Channel";
        String adminChannelDescription = "Storetrack Description";

        NotificationChannel adminChannel;
        adminChannel = new NotificationChannel(ADMIN_CHANNEL_ID, adminChannelName, NotificationManager.IMPORTANCE_LOW);
        adminChannel.setDescription(adminChannelDescription);
        adminChannel.enableLights(true);
        adminChannel.setLightColor(Color.RED);
        adminChannel.enableVibration(true);
        if (notificationManager != null) {
            notificationManager.createNotificationChannel(adminChannel);
        }
    }



    public Bitmap getBitmapfromUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoInput(true);
            connection.connect();
            InputStream input = connection.getInputStream();
            return BitmapFactory.decodeStream(input);

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
