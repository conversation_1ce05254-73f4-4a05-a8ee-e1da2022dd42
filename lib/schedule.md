 if (
                                    ((selectedDate.after(taskDetailModel.getTaskStartDate()) || selectedDate.equals(taskDetailModel.getTaskStartDate())) &&
                                            (selectedDate.before(taskDetailModel.getTaskEndDate()) || selectedDate.equals(taskDetailModel.getTaskEndDate())))
                                            || (selectedDate.before(taskDetailModel.getTaskExpiresDate()) || selectedDate.equals(taskDetailModel.getTaskExpiresDate()))
                            )

                            allow schedule reschedule only within this range

ie, if ((date after task.rangeStart || date == task.rangeStart) && (date before task.rangeEnd || date == task.rangeEnd) || (date before task.expires || date == task.expires))