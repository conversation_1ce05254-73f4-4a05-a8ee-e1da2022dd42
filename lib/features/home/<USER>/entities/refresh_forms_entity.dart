import 'dart:convert';

import 'package:equatable/equatable.dart';

// ignore: must_be_immutable
class RefreshFormsEntity extends Equatable {
  String? token;
  String? userId;
  String? deviceUid;
  String? appversion;
  List<RefreshTaskEntity>? tasks;

  RefreshFormsEntity({
    this.token,
    this.userId,
    this.deviceUid,
    this.appversion,
    this.tasks,
  });

  factory RefreshFormsEntity.fromRawJson(String str) =>
      RefreshFormsEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RefreshFormsEntity.fromJson(Map<String, dynamic> json) =>
      RefreshFormsEntity(
        token: json["token"],
        userId: json["user_id"],
        deviceUid: json["device_uid"],
        appversion: json["appversion"],
        tasks: json["tasks"] == null
            ? null
            : List<RefreshTaskEntity>.from(
                json["tasks"]!.map((x) => RefreshTaskEntity.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "token": token,
        "user_id": userId,
        "device_uid": deviceUid,
        "appversion": appversion,
        "tasks": tasks == null
            ? []
            : List<dynamic>.from(tasks!.map((x) => x.toJson())),
      };

  @override
  List<Object?> get props => [token, userId, deviceUid, appversion, tasks];
}

class RefreshTaskEntity extends Equatable {
  String? taskId;
  DateTime? scheduledTimeStamp;
  DateTime? submissionTimeStamp;
  DateTime? modifiedTimeStampTask;
  DateTime? modifiedTimeStampPhotos;
  DateTime? modifiedTimeStampPhototypes;
  DateTime? modifiedTimeStampForms;
  DateTime? modifiedTimeStampDocuments;
  DateTime? modifiedTimeStampSignatures;
  DateTime? modifiedTimeStampSignaturetypes;
  List<int>? phototypeIds;
  bool? forceImportFollowupTask;
  List<int>? signaturetypeIds;

  RefreshTaskEntity({
    this.taskId,
    this.scheduledTimeStamp,
    this.submissionTimeStamp,
    this.modifiedTimeStampTask,
    this.modifiedTimeStampPhotos,
    this.modifiedTimeStampPhototypes,
    this.modifiedTimeStampForms,
    this.modifiedTimeStampDocuments,
    this.modifiedTimeStampSignatures,
    this.modifiedTimeStampSignaturetypes,
    this.phototypeIds,
    this.forceImportFollowupTask,
    this.signaturetypeIds,
  });

  factory RefreshTaskEntity.fromRawJson(String str) =>
      RefreshTaskEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RefreshTaskEntity.fromJson(Map<String, dynamic> json) =>
      RefreshTaskEntity(
        taskId: json["task_id"],
        scheduledTimeStamp: json["scheduled_time_stamp"] == null
            ? null
            : DateTime.parse(json["scheduled_time_stamp"]),
        submissionTimeStamp: json["submission_time_stamp"] == null
            ? null
            : DateTime.parse(json["submission_time_stamp"]),
        modifiedTimeStampTask: json["modified_time_stamp_task"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_task"]),
        modifiedTimeStampPhotos: json["modified_time_stamp_photos"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_photos"]),
        modifiedTimeStampPhototypes:
            json["modified_time_stamp_phototypes"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_phototypes"]),
        modifiedTimeStampForms: json["modified_time_stamp_forms"] == null
            ? null
            : DateTime.parse(json["modified_time_stamp_forms"]),
        modifiedTimeStampDocuments:
            json["modified_time_stamp_documents"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_documents"]),
        modifiedTimeStampSignatures:
            json["modified_time_stamp_signatures"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signatures"]),
        modifiedTimeStampSignaturetypes:
            json["modified_time_stamp_signaturetypes"] == null
                ? null
                : DateTime.parse(json["modified_time_stamp_signaturetypes"]),
        phototypeIds: json["phototype_ids"] == null
            ? null
            : List<int>.from(json["phototype_ids"]!.map((x) => x)),
        forceImportFollowupTask: json["force_import_followup_task"],
        signaturetypeIds: json["signaturetype_ids"] == null
            ? null
            : List<int>.from(json["signaturetype_ids"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "scheduled_time_stamp": scheduledTimeStamp?.toIso8601String(),
        "submission_time_stamp": submissionTimeStamp?.toIso8601String(),
        "modified_time_stamp_task": modifiedTimeStampTask?.toIso8601String(),
        "modified_time_stamp_photos":
            modifiedTimeStampPhotos?.toIso8601String(),
        "modified_time_stamp_phototypes":
            modifiedTimeStampPhototypes?.toIso8601String(),
        "modified_time_stamp_forms": modifiedTimeStampForms?.toIso8601String(),
        "modified_time_stamp_documents":
            modifiedTimeStampDocuments?.toIso8601String(),
        "modified_time_stamp_signatures":
            modifiedTimeStampSignatures?.toIso8601String(),
        "modified_time_stamp_signaturetypes":
            modifiedTimeStampSignaturetypes?.toIso8601String(),
        "phototype_ids": phototypeIds ?? [],
        "force_import_followup_task": forceImportFollowupTask ?? false,
        "signaturetype_ids": signaturetypeIds ?? [],
      };

  @override
  List<Object?> get props => [
        taskId,
        scheduledTimeStamp,
        submissionTimeStamp,
        modifiedTimeStampTask,
        modifiedTimeStampPhotos,
        modifiedTimeStampPhototypes,
        modifiedTimeStampForms,
        modifiedTimeStampDocuments,
        modifiedTimeStampSignatures,
        modifiedTimeStampSignaturetypes,
        phototypeIds,
        forceImportFollowupTask,
        signaturetypeIds,
      ];
}
