import 'package:equatable/equatable.dart';

class AvailabilityRequestEntity extends Equatable {
  final String userId;
  final String token;
  final String deviceuid;
  final List<DayRequestEntity> days;

  const AvailabilityRequestEntity({
    required this.userId,
    required this.token,
    required this.deviceuid,
    required this.days,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'token': token,
      'deviceuid': deviceuid,
      'days': days.map((day) => day.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [userId, token, deviceuid, days];
}

class DayRequestEntity extends Equatable {
  final List<DaySpanRequestEntity> daySpans;
  final String dayDescription;
  final int dayNumber;

  const DayRequestEntity({
    required this.daySpans,
    required this.dayDescription,
    required this.dayNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'day_spans': daySpans.map((span) => span.toJson()).toList(),
      'day_description': dayDescription,
      'day_number': dayNumber,
    };
  }

  @override
  List<Object?> get props => [daySpans, dayDescription, dayNumber];
}

class DaySpanRequestEntity extends Equatable {
  final String startHour;
  final String endHour;
  final String dayEntryNumber;

  const DaySpanRequestEntity({
    required this.startHour,
    required this.endHour,
    required this.dayEntryNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'start_hour': startHour,
      'end_hour': endHour,
      'day_entry_number': dayEntryNumber,
    };
  }

  @override
  List<Object?> get props => [startHour, endHour, dayEntryNumber];
}
