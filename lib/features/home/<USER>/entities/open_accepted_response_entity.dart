class OpenAcceptedResponseEntity {
  final List<OpenAcceptedTask> tasksAvailable;
  final bool success;
  final String? message;

  OpenAcceptedResponseEntity({
    required this.tasksAvailable,
    required this.success,
    this.message,
  });

  factory OpenAcceptedResponseEntity.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>? ?? {};
    return OpenAcceptedResponseEntity(
      tasksAvailable: (data['tasks_available'] as List<dynamic>?)
              ?.map((task) => OpenAcceptedTask.fromJson(task))
              .toList() ??
          [],
      success: json['success'] as bool? ?? true,
      message: json['message'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': {
        'tasks_available': tasksAvailable.map((task) => task.toJson()).toList(),
      },
      'success': success,
      'message': message,
    };
  }
}

class OpenAcceptedTask {
  final String taskId;
  final String client;
  final int clientId;
  final String storeName;
  final int budget;
  final String location;
  final double latitude;
  final double longitude;
  final String cycle;
  final int cycleId;
  final String scheduledTimeStamp;
  final bool disallowReschedule;
  final String expires;
  final String rangeStart;
  final String rangeEnd;
  final bool reOpened;
  final String reOpenedReason;
  final String taskNote;
  final double distance;
  final int teamlead;

  OpenAcceptedTask({
    required this.taskId,
    required this.client,
    required this.clientId,
    required this.storeName,
    required this.budget,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.cycle,
    required this.cycleId,
    required this.scheduledTimeStamp,
    required this.disallowReschedule,
    required this.expires,
    required this.rangeStart,
    required this.rangeEnd,
    required this.reOpened,
    required this.reOpenedReason,
    required this.taskNote,
    required this.distance,
    required this.teamlead,
  });

  factory OpenAcceptedTask.fromJson(Map<String, dynamic> json) {
    return OpenAcceptedTask(
      taskId: json['task_id'].toString(),
      client: json['client'] as String? ?? '',
      clientId: json['client_id'] as int? ?? 0,
      storeName: json['store_name'] as String? ?? '',
      budget: json['budget'] as int? ?? 0,
      location: json['location'] as String? ?? '',
      latitude: (json['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (json['longitude'] as num?)?.toDouble() ?? 0.0,
      cycle: json['cycle'] as String? ?? '',
      cycleId: json['cycle_id'] as int? ?? 0,
      scheduledTimeStamp: json['scheduled_time_stamp'] as String? ?? '',
      disallowReschedule: json['disallow_reschedule'] as bool? ?? false,
      expires: json['expires'] as String? ?? '',
      rangeStart: json['range_start'] as String? ?? '',
      rangeEnd: json['range_end'] as String? ?? '',
      reOpened: json['re_opened'] as bool? ?? false,
      reOpenedReason: json['re_opened_reason'] as String? ?? '',
      taskNote: json['task_note'] as String? ?? '',
      distance: (json['distance'] as num?)?.toDouble() ?? 0.0,
      teamlead: json['teamlead'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'task_id': taskId,
      'client': client,
      'client_id': clientId,
      'store_name': storeName,
      'budget': budget,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'cycle': cycle,
      'cycle_id': cycleId,
      'scheduled_time_stamp': scheduledTimeStamp,
      'disallow_reschedule': disallowReschedule,
      'expires': expires,
      'range_start': rangeStart,
      'range_end': rangeEnd,
      're_opened': reOpened,
      're_opened_reason': reOpenedReason,
      'task_note': taskNote,
      'distance': distance,
      'teamlead': teamlead,
    };
  }
}
