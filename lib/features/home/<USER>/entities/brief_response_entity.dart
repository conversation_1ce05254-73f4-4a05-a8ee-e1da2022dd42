import 'package:storetrack_app/core/utils/logger.dart';

class BriefResponseEntity {
  final bool success;
  final String message;
  final List<BriefDocument> documents;

  BriefResponseEntity({
    required this.success,
    required this.message,
    required this.documents,
  });

  factory BriefResponseEntity.fromJson(Map<String, dynamic> json) {
    logger("BriefResponseEntity: Parsing JSON: $json");

    // Try different possible response structures
    List<BriefDocument> documents = [];

    // Structure 1: documents array
    if (json['documents'] != null) {
      logger("BriefResponseEntity: Found 'documents' key");
      documents = (json['documents'] as List<dynamic>?)
              ?.map((doc) => BriefDocument.fromJson(doc))
              .toList() ??
          [];
    }
    // Structure 2: alDocuments array (from Android code)
    else if (json['alDocuments'] != null) {
      logger("BriefResponseEntity: Found 'alDocuments' key");
      documents = (json['alDocuments'] as List<dynamic>?)
              ?.map((doc) => BriefDocument.fromJson(doc))
              .toList() ??
          [];
    }
    // Structure 3: data.documents
    else if (json['data'] != null && json['data']['documents'] != null) {
      logger("BriefResponseEntity: Found 'data.documents' key");
      documents = (json['data']['documents'] as List<dynamic>?)
              ?.map((doc) => BriefDocument.fromJson(doc))
              .toList() ??
          [];
    }
    // Structure 4: data.alDocuments
    else if (json['data'] != null && json['data']['alDocuments'] != null) {
      logger("BriefResponseEntity: Found 'data.alDocuments' key");
      documents = (json['data']['alDocuments'] as List<dynamic>?)
              ?.map((doc) => BriefDocument.fromJson(doc))
              .toList() ??
          [];
    }
    // Structure 5: Direct array of documents
    else if (json is List) {
      logger("BriefResponseEntity: JSON is a direct list");
      documents = (json as List<dynamic>)
          .map((doc) => BriefDocument.fromJson(doc))
          .toList();
    }

    logger("BriefResponseEntity: Parsed ${documents.length} documents");

    return BriefResponseEntity(
      success:
          json['success'] as bool? ?? true, // Default to true if not specified
      message: json['message'] as String? ?? '',
      documents: documents,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'documents': documents.map((doc) => doc.toJson()).toList(),
    };
  }
}

class BriefDocument {
  final String documentId;
  final String documentName;
  final String documentType;
  final String? projectId;
  final int? documentTypeId;
  final String? documentIconLink;
  final String? modifiedTimeStampDocument;
  final List<BriefFile> files;

  BriefDocument({
    required this.documentId,
    required this.documentName,
    required this.documentType,
    this.projectId,
    this.documentTypeId,
    this.documentIconLink,
    this.modifiedTimeStampDocument,
    required this.files,
  });

  factory BriefDocument.fromJson(Map<String, dynamic> json) {
    logger("BriefDocument: Parsing JSON: $json");

    return BriefDocument(
      documentId: json['document_id']?.toString() ??
          json['documentId']?.toString() ??
          json['id']?.toString() ??
          '',
      documentName: json['document_name'] as String? ??
          json['documentName'] as String? ??
          json['name'] as String? ??
          '',
      documentType: json['document_type'] as String? ??
          json['documentType'] as String? ??
          json['type'] as String? ??
          '',
      projectId:
          json['project_id']?.toString() ?? json['projectId']?.toString(),
      documentTypeId:
          json['document_type_id'] as int? ?? json['documentTypeId'] as int?,
      documentIconLink: json['document_icon_link'] as String? ??
          json['documentIconLink'] as String?,
      modifiedTimeStampDocument:
          json['modified_time_stamp_document'] as String? ??
              json['modifiedTimeStampDocument'] as String?,
      files: (json['files'] as List<dynamic>?)
              ?.map((file) => BriefFile.fromJson(file))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'document_id': documentId,
      'document_name': documentName,
      'document_type': documentType,
      'project_id': projectId,
      'document_type_id': documentTypeId,
      'document_icon_link': documentIconLink,
      'modified_time_stamp_document': modifiedTimeStampDocument,
      'files': files.map((file) => file.toJson()).toList(),
    };
  }
}

class BriefFile {
  final String fileId;
  final String fileName;
  final String fileUrl;
  final String? fileType;
  final String? localPath;
  final String? documentId;
  final String? projectId;
  final String? modifiedTimeStampFile;

  BriefFile({
    required this.fileId,
    required this.fileName,
    required this.fileUrl,
    this.fileType,
    this.localPath,
    this.documentId,
    this.projectId,
    this.modifiedTimeStampFile,
  });

  factory BriefFile.fromJson(Map<String, dynamic> json) {
    logger("BriefFile: Parsing JSON: $json");

    // Extract file name from URL if not provided
    String fileName = json['file_name'] as String? ??
        json['fileName'] as String? ??
        json['name'] as String? ??
        '';

    if (fileName.isEmpty) {
      // Extract filename from document_file_link
      final fileUrl = json['document_file_link'] as String? ??
          json['file_url'] as String? ??
          json['fileUrl'] as String? ??
          json['url'] as String? ??
          '';
      if (fileUrl.isNotEmpty) {
        final uri = Uri.parse(fileUrl);
        fileName = uri.pathSegments.last;
      }
    }

    return BriefFile(
      fileId: json['file_id']?.toString() ??
          json['fileId']?.toString() ??
          json['id']?.toString() ??
          '',
      fileName: fileName,
      fileUrl: json['document_file_link'] as String? ??
          json['file_url'] as String? ??
          json['fileUrl'] as String? ??
          json['url'] as String? ??
          '',
      fileType: json['file_type'] as String? ??
          json['fileType'] as String? ??
          json['type'] as String?,
      localPath: json['local_path'] as String? ?? json['localPath'] as String?,
      documentId:
          json['document_id']?.toString() ?? json['documentId']?.toString(),
      projectId:
          json['project_id']?.toString() ?? json['projectId']?.toString(),
      modifiedTimeStampFile: json['modified_time_stamp_file'] as String? ??
          json['modifiedTimeStampFile'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'file_id': fileId,
      'file_name': fileName,
      'file_url': fileUrl,
      'file_type': fileType,
      'local_path': localPath,
      'document_id': documentId,
      'project_id': projectId,
      'modified_time_stamp_file': modifiedTimeStampFile,
    };
  }
}
