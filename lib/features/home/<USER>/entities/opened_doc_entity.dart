import 'package:equatable/equatable.dart';

class OpenedDocRequestEntity extends Equatable {
  final String userId;
  final String deviceToken;
  final List<OpenedDocItemEntity> messageRead;

  const OpenedDocRequestEntity({
    required this.userId,
    required this.deviceToken,
    required this.messageRead,
  });

  @override
  List<Object?> get props => [userId, deviceToken, messageRead];
}

class OpenedDocItemEntity extends Equatable {
  final String docId;
  final String timeStamp;

  const OpenedDocItemEntity({
    required this.docId,
    required this.timeStamp,
  });

  @override
  List<Object?> get props => [docId, timeStamp];
}

class OpenedDocResponseEntity extends Equatable {
  final bool success;
  final String message;

  const OpenedDocResponseEntity({
    required this.success,
    required this.message,
  });

  @override
  List<Object?> get props => [success, message];
}
