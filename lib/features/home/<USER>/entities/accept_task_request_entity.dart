class AcceptTaskRequestEntity {
  final String token;
  final String userId;
  final String taskId;
  final String dateScheduled;

  AcceptTaskRequestEntity({
    required this.token,
    required this.userId,
    required this.taskId,
    required this.dateScheduled,
  });

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'user_id': userId,
      'task_id': taskId,
      'date_scheduled': dateScheduled,
    };
  }
}
