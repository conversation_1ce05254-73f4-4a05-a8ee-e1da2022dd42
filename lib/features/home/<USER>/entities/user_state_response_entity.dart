import 'package:equatable/equatable.dart';

class UserStateResponseEntity extends Equatable {
  final UserStateDataEntity? data;

  const UserStateResponseEntity({
    this.data,
  });

  factory UserStateResponseEntity.fromJson(Map<String, dynamic> json) {
    return UserStateResponseEntity(
      data: json['data'] != null
          ? UserStateDataEntity.fromJson(json['data'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data?.toJson(),
    };
  }

  @override
  List<Object?> get props => [data];
}

class UserStateDataEntity extends Equatable {
  final bool active;
  final bool appOldVersion;
  final bool gracePeriod;
  final String supportNumber;
  final String warningMessage;
  final String warningTitle;
  final bool kickOutUser;
  final bool appOtherWarning;
  final int warningMessageId;
  final String latestVersion;
  final bool adminAccess;
  final int countryId;
  final bool createTask;

  const UserStateDataEntity({
    required this.active,
    required this.appOldVersion,
    required this.gracePeriod,
    required this.supportNumber,
    required this.warningMessage,
    required this.warningTitle,
    required this.kickOutUser,
    required this.appOtherWarning,
    required this.warningMessageId,
    required this.latestVersion,
    required this.adminAccess,
    required this.countryId,
    required this.createTask,
  });

  factory UserStateDataEntity.fromJson(Map<String, dynamic> json) {
    return UserStateDataEntity(
      active: json['active'] ?? false,
      appOldVersion: json['app_old_version'] ?? false,
      gracePeriod: json['grace_period'] ?? false,
      supportNumber: json['support_number'] ?? '',
      warningMessage: json['warning_message'] ?? '',
      warningTitle: json['warning_title'] ?? '',
      kickOutUser: json['kick_out_user'] ?? false,
      appOtherWarning: json['app_other_warning'] ?? false,
      warningMessageId: json['warning_message_id'] ?? 0,
      latestVersion: json['latest_version'] ?? '',
      adminAccess: json['admin_access'] ?? false,
      countryId: json['country_id'] ?? 0,
      createTask: json['create_task'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'active': active,
      'app_old_version': appOldVersion,
      'grace_period': gracePeriod,
      'support_number': supportNumber,
      'warning_message': warningMessage,
      'warning_title': warningTitle,
      'kick_out_user': kickOutUser,
      'app_other_warning': appOtherWarning,
      'warning_message_id': warningMessageId,
      'latest_version': latestVersion,
      'admin_access': adminAccess,
      'country_id': countryId,
      'create_task': createTask,
    };
  }

  @override
  List<Object?> get props => [
        active,
        appOldVersion,
        gracePeriod,
        supportNumber,
        warningMessage,
        warningTitle,
        kickOutUser,
        appOtherWarning,
        warningMessageId,
        latestVersion,
        adminAccess,
        countryId,
        createTask,
      ];
}
