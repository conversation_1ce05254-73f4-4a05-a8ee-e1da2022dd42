import 'package:equatable/equatable.dart';

class UserStateRequestEntity extends Equatable {
  final String deviceUid;
  final String userId;
  final String devicePlatform;
  final String appversion;
  final String token;

  const UserStateRequestEntity({
    required this.deviceUid,
    required this.userId,
    required this.devicePlatform,
    required this.appversion,
    required this.token,
  });

  Map<String, dynamic> toJson() {
    return {
      'device_uid': deviceUid,
      'user_id': userId,
      'device_platform': devicePlatform,
      'appversion': appversion,
      'token': token,
    };
  }

  factory UserStateRequestEntity.fromJson(Map<String, dynamic> json) {
    return UserStateRequestEntity(
      deviceUid: json['device_uid'] ?? '',
      userId: json['user_id'] ?? '',
      devicePlatform: json['device_platform'] ?? '',
      appversion: json['appversion'] ?? '',
      token: json['token'] ?? '',
    );
  }

  @override
  List<Object?> get props =>
      [deviceUid, userId, devicePlatform, appversion, token];
}
