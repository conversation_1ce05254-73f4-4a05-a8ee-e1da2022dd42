class AcceptTaskResponseEntity {
  final bool success;
  final String message;
  final String? taskId;

  AcceptTaskResponseEntity({
    required this.success,
    required this.message,
    this.taskId,
  });

  factory AcceptTaskResponseEntity.fromJson(Map<String, dynamic> json) {
    return AcceptTaskResponseEntity(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      taskId: json['task_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'task_id': taskId,
    };
  }
}
