import 'dart:convert';

class VersionResponseEntity {
  String? androidVersion;
  String? androidVersionUat;
  String? iOsVersion;
  String? iOsVersionUat;
  String? downloadLink;
  String? downloadLinkIos;
  String? downloadLinkAndroid;
  String? currentVersionDateStr;

  VersionResponseEntity({
    this.androidVersion,
    this.androidVersionUat,
    this.iOsVersion,
    this.iOsVersionUat,
    this.downloadLink,
    this.downloadLinkIos,
    this.downloadLinkAndroid,
    this.currentVersionDateStr,
  });

  factory VersionResponseEntity.fromRawJson(String str) =>
      VersionResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory VersionResponseEntity.fromJson(Map<String, dynamic> json) =>
      VersionResponseEntity(
        androidVersion: json["android_version"],
        androidVersionUat: json["android_version_uat"],
        iOsVersion: json["iOS_version"],
        iOsVersionUat: json["iOS_version_uat"],
        downloadLink: json["downloadLink"],
        downloadLinkIos: json["downloadLinkIOS"],
        downloadLinkAndroid: json["downloadLinkAndroid"],
        currentVersionDateStr: json["current_version_date_str"],
      );

  Map<String, dynamic> toJson() => {
        "android_version": androidVersion,
        "android_version_uat": androidVersionUat,
        "iOS_version": iOsVersion,
        "iOS_version_uat": iOsVersionUat,
        "downloadLink": downloadLink,
        "downloadLinkIOS": downloadLinkIos,
        "downloadLinkAndroid": downloadLinkAndroid,
        "current_version_date_str": currentVersionDateStr,
      };
}
