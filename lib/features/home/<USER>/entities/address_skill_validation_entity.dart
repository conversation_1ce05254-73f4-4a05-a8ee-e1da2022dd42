import 'package:equatable/equatable.dart';

class AddressSkillValidationEntity extends Equatable {
  final bool isAddressValidated;
  final bool isSkillValidated;
  final bool isAvailabilityValidated;

  const AddressSkillValidationEntity({
    required this.isAddressValidated,
    required this.isSkillValidated,
    required this.isAvailabilityValidated,
  });

  // Warning title constants matching the Java implementation
  static const String warningTitleAddressSkillsAvailable =
      "Address/Skills/Availability Required";
  static const String warningTitleAddressSkills = "Address/Skills Required";
  static const String warningTitleAddressAvailable =
      "Address/Availability Required";
  static const String warningTitleSkillsAvailable =
      "Skills/Availability Required";
  static const String warningTitleAddress = "Address Required";
  static const String warningTitleSkills = "Skills Required";
  static const String warningTitleAvailability = "Availability Required";

  factory AddressSkillValidationEntity.fromWarningTitle(String warningTitle) {
    switch (warningTitle) {
      case warningTitleAddressSkillsAvailable:
        return const AddressSkillValidationEntity(
          isAddressValidated: false,
          isSkillValidated: false,
          isAvailabilityValidated: false,
        );
      case warningTitleAddressSkills:
        return const AddressSkillValidationEntity(
          isAddressValidated: false,
          isSkillValidated: false,
          isAvailabilityValidated: true,
        );
      case warningTitleAddressAvailable:
        return const AddressSkillValidationEntity(
          isAddressValidated: false,
          isSkillValidated: true,
          isAvailabilityValidated: false,
        );
      case warningTitleSkillsAvailable:
        return const AddressSkillValidationEntity(
          isAddressValidated: true,
          isSkillValidated: false,
          isAvailabilityValidated: false,
        );
      case warningTitleAddress:
        return const AddressSkillValidationEntity(
          isAddressValidated: false,
          isSkillValidated: true,
          isAvailabilityValidated: true,
        );
      case warningTitleSkills:
        return const AddressSkillValidationEntity(
          isAddressValidated: true,
          isSkillValidated: false,
          isAvailabilityValidated: true,
        );
      case warningTitleAvailability:
        return const AddressSkillValidationEntity(
          isAddressValidated: true,
          isSkillValidated: true,
          isAvailabilityValidated: false,
        );
      default:
        return const AddressSkillValidationEntity(
          isAddressValidated: true,
          isSkillValidated: true,
          isAvailabilityValidated: true,
        );
    }
  }

  factory AddressSkillValidationEntity.fromJson(Map<String, dynamic> json) {
    return AddressSkillValidationEntity(
      isAddressValidated: json['is_address_validated'] ?? true,
      isSkillValidated: json['is_skill_validated'] ?? true,
      isAvailabilityValidated: json['is_availability_validated'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_address_validated': isAddressValidated,
      'is_skill_validated': isSkillValidated,
      'is_availability_validated': isAvailabilityValidated,
    };
  }

  @override
  List<Object?> get props => [
        isAddressValidated,
        isSkillValidated,
        isAvailabilityValidated,
      ];
}
