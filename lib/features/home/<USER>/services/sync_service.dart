import 'package:flutter/material.dart';
import 'package:storetrack_app/core/services/device_info_service.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/network/network_info.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/pos_response_item_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/pos_response_item_model.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/cubits/connectivity_cubit.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/services/app_info_service.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_profile_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_misc_setting_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_history_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_alerts_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_availability_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_leave_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_skills_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_induction_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/update_pos_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/pos_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_user_state_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/user_state_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/address_skill_validation_entity.dart';
import 'package:storetrack_app/features/home/<USER>/models/address_skill_validation_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/address_skill_validation_mapper.dart';
import 'package:storetrack_app/core/utils/sync_utils.dart';
import 'package:storetrack_app/core/services/msal_auth_service.dart';
import 'package:storetrack_app/core/services/docs_interaction_service.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/send_opened_docs_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/opened_doc_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_country_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_state_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/check_version_usecase.dart';
import 'dart:io';

class SyncService {
  final NetworkInfo _networkInfo;

  SyncService._internal() : _networkInfo = sl<NetworkInfo>();

  static final SyncService _instance = SyncService._internal();

  factory SyncService() => _instance;

  final ValueNotifier<bool> isSyncing = ValueNotifier(false);

  // User state validation notifications
  final ValueNotifier<Map<String, dynamic>?> userStateValidationResult =
      ValueNotifier(null);

  // Version update notifications
  final ValueNotifier<Map<String, dynamic>?> versionUpdateResult =
      ValueNotifier(null);

  /// @deprecated This method is no longer needed as sync() manages its own indicator
  /// Start submission process and immediately show sync indicator
  @Deprecated(
      'Use sync() method directly - it manages the sync indicator internally')
  void startSubmission() {
    logger('🚀 Starting task submission - sync indicator activated');
    isSyncing.value = true;
  }

  Future<void> sync({
    String? userId,
    BuildContext? context,
    bool force = false,
    bool isEmulate = false,
  }) async {
    isEmulate = await sl<DataManager>().isEmulating();

    if (isSyncing.value && !force) {
      logger('⏸️ Sync already in progress, skipping');
      return;
    }

    // Check if work offline mode is enabled
    final connectivityCubit = sl<ConnectivityCubit>();
    if (connectivityCubit.isWorkOfflineEnabled) {
      logger('⏸️ Work offline mode is enabled, sync disabled');
      if (context != null && context.mounted) {
        SnackBarService.error(
          context: context,
          message: 'Sync is disabled while working offline',
        );
      }
      return;
    }

    // Check internet connectivity before starting sync
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected && context != null && context.mounted) {
      logger('❌ No internet connection available for sync');
      await _showNoInternetDialog(context);
      return;
    } else if (!isConnected) {
      logger(
          '❌ No internet connection available for sync (no context provided)');
      throw Exception('No internet connection available for sync');
    }

    final kUserId = userId ?? await sl<DataManager>().getUserId() ?? '';
    final syncStartTime = DateTime.now();

    logger('🚀 Starting full sync process for user: $kUserId');

    try {
      isSyncing.value = true;

      // Sequential photo and signature synchronization
      final phase1StartTime = DateTime.now();
      logger('📸 Phase 1: Starting photo and signature sync');
      await _syncPhotosAndSignatures(emulatedUserId: userId);
      final phase1Duration = DateTime.now().difference(phase1StartTime);
      logger(
          '✅ Phase 1: Photo and signature sync completed in ${phase1Duration.inMilliseconds}ms');

      // Final step: Submit reports for completed tasks
      final phase2StartTime = DateTime.now();
      logger('📋 Phase 2: Starting report submission');
      await _submitReports(emulatedUserId: userId);
      final phase2Duration = DateTime.now().difference(phase2StartTime);
      logger(
          '✅ Phase 2: Report submission completed in ${phase2Duration.inMilliseconds}ms');

      // Run basic sync operations in parallel
      final phase3StartTime = DateTime.now();
      logger('🔄 Phase 3: Starting parallel data sync operations');
      await Future.wait([
        _getTasks(emulatedUserId: userId),
        _syncCalendar(emulatedUserId: userId),
        _syncProfile(emulatedUserId: userId),
        _syncMiscSetting(emulatedUserId: userId),
        // Add new sync methods
        _syncHistory(emulatedUserId: userId),
        _syncAlerts(emulatedUserId: userId),
        _syncAvailability(emulatedUserId: userId),
        _syncLeave(emulatedUserId: userId),
        _syncSkills(emulatedUserId: userId),
        _syncInduction(emulatedUserId: userId),
        _updatePos(userId: userId),
        _syncOpenedDocs(emulatedUserId: userId),
        _syncCountry(emulatedUserId: userId),
        _syncState(emulatedUserId: userId),
      ]);
      final phase3Duration = DateTime.now().difference(phase3StartTime);
      logger(
          '✅ Phase 3: Parallel data sync operations completed in ${phase3Duration.inMilliseconds}ms');

      // Final step: Sync user state and process validation data
      final phase4StartTime = DateTime.now();
      logger('👤 Phase 4: Starting user state sync');
      if (!isEmulate) await _syncUserState(emulatedUserId: userId);
      final phase4Duration = DateTime.now().difference(phase4StartTime);
      logger(
          '✅ Phase 4: User state sync completed in ${phase4Duration.inMilliseconds}ms');

      // Perform user state validation after sync is complete
      final phase5StartTime = DateTime.now();
      logger('🔍 Phase 5: Starting user state validation');
      if (!isEmulate) await _performUserStateValidation(emulatedUserId: userId);
      final phase5Duration = DateTime.now().difference(phase5StartTime);
      logger(
          '✅ Phase 5: User state validation completed in ${phase5Duration.inMilliseconds}ms');

      // Perform version check after sync is complete
      final phase6StartTime = DateTime.now();
      logger('📱 Phase 6: Starting version check');
      if (!isEmulate) await _performVersionCheck(emulatedUserId: userId);
      final phase6Duration = DateTime.now().difference(phase6StartTime);
      logger(
          '✅ Phase 6: Version check completed in ${phase6Duration.inMilliseconds}ms');

      // Save sync completion time after successful sync
      if (!isEmulate) await sl<DataManager>().saveLastSyncTime(DateTime.now());

      final syncDuration = DateTime.now().difference(syncStartTime);
      logger(
          '🎉 Full sync completed successfully for user: $kUserId in ${syncDuration.inSeconds}s');
    } catch (e) {
      final syncDuration = DateTime.now().difference(syncStartTime);
      logger(
          '❌ Full sync failed for user: $kUserId after ${syncDuration.inSeconds}s - Error: $e');
      rethrow;
    } finally {
      isSyncing.value = false;
      logger('🏁 Sync process finalized, sync indicator deactivated');
    }
  }

  Future<void> _getTasks({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';
    final taskSyncStartTime = DateTime.now();

    try {
      logger('📋 Starting tasks sync for user: $userId');

      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken() ?? '';
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
      final appInfoService = sl<AppInfoService>();
      final String actualAppVersion = await appInfoService.getVersion();

      final request = TasksRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        appversion: actualAppVersion,
        tasks: const [],
        token: token,
      );

      logger('📡 Calling tasks_optimize API for user: $userId');
      await sl<GetTasksUseCase>().call(request, isSync: true);
      final taskSyncDuration = DateTime.now().difference(taskSyncStartTime);
      logger(
          '✅ Tasks sync completed successfully for user: $userId in ${taskSyncDuration.inMilliseconds}ms');

      // The repository now handles caching, so no need to save here.
      // if (result.isSuccess && result.data != null) {
      //   await sl<HomeLocalDataSource>().saveTasks(result.data!);
      // }
    } catch (e) {
      final taskSyncDuration = DateTime.now().difference(taskSyncStartTime);
      logger(
          '❌ Tasks sync failed for user: $userId after ${taskSyncDuration.inMilliseconds}ms - Error: $e');
      rethrow;
    }
  }

  Future<void> _syncCalendar({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('📅 Starting calendar sync for user: $userId');

      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      final params = GetCalendarParams(token: token, userId: id);
      await sl<GetCalendarUseCase>().call(params, isSync: true);

      logger('✅ Calendar sync completed successfully for user: $userId');
    } catch (e) {
      logger('❌ Calendar sync failed for user: $userId - Error: $e');
      rethrow;
    }
  }

  Future<void> _syncProfile({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('👤 Starting profile sync for user: $userId');

      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetProfileUseCase>()
          .call(token: token, userId: id, isSync: true);

      logger('✅ Profile sync completed successfully for user: $userId');
    } catch (e) {
      logger('❌ Profile sync failed for user: $userId - Error: $e');
      rethrow;
    }
  }

  Future<void> _syncMiscSetting({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('⚙️ Starting misc settings sync for user: $userId');

      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetMiscSettingUseCase>().call(token: token, userId: id);

      logger('✅ Misc settings sync completed successfully for user: $userId');
    } catch (e) {
      logger('❌ Misc settings sync failed for user: $userId - Error: $e');
      rethrow;
    }
  }

  /// Sequential photo and signature synchronization
  ///
  /// This method performs the following operations in sequence:
  /// 1. Upload photos API endpoint
  /// 2. Sync photos API endpoint (after successful photo upload)
  /// 3. Upload signatures API endpoint
  /// 4. Sync signatures API endpoint (after successful signature upload)
  Future<void> _syncPhotosAndSignatures({String? emulatedUserId}) async {
    try {
      // Get all tasks from local database
      final tasks = await _getAllTasksFromDatabase();

      if (tasks.isEmpty) {
        logger('No tasks found for photo and signature sync');
        return;
      }

      // Step 1 & 2: Upload and sync photos
      final photoStartTime = DateTime.now();
      logger('🚀 Starting photo upload and sync workflow');
      final photoSyncResult = await sl<HomeRepository>().uploadAndSyncPhotos(
        tasks: tasks,
      );
      final photoDuration = DateTime.now().difference(photoStartTime);

      if (photoSyncResult.isSuccess) {
        logger(
            '✅ Photo upload and sync completed successfully in ${photoDuration.inMilliseconds}ms');
      } else {
        logger(
            '❌ Photo upload and sync failed after ${photoDuration.inMilliseconds}ms: ${photoSyncResult.error}');
      }

      // Step 3 & 4: Upload and sync signatures
      final signatureStartTime = DateTime.now();
      logger('🚀 Starting signature upload and sync workflow');
      final signatureSyncResult =
          await sl<HomeRepository>().uploadAndSyncSignatures(
        tasks: tasks,
      );
      final signatureDuration = DateTime.now().difference(signatureStartTime);

      if (signatureSyncResult.isSuccess) {
        logger(
            '✅ Signature upload and sync completed successfully in ${signatureDuration.inMilliseconds}ms');
      } else {
        logger(
            '❌ Signature upload and sync failed after ${signatureDuration.inMilliseconds}ms: ${signatureSyncResult.error}');
      }
    } catch (e) {
      logger('❌ Error during photo and signature sync: $e');
    }
  }

  /// Get all tasks from the local database
  Future<List<TaskDetail>> _getAllTasksFromDatabase() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final taskModels = realm.all<TaskDetailModel>();

      // Convert models to entities
      final tasks =
          taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

      logger('Retrieved ${tasks.length} tasks from local database');
      return tasks;
    } catch (e) {
      logger('❌ Error retrieving tasks from database: $e');
      return [];
    }
  }

  /// Submit reports for tasks that need to be submitted
  ///
  /// This method performs the final step in the sync process by submitting
  /// completed task reports to the server using the new submit report batch API.
  Future<void> _submitReports({String? emulatedUserId}) async {
    try {
      // Filter tasks that need to be submitted (have sync pending status)
      final tasksToSubmit = SyncUtils.getTasksToSubmit();

      if (tasksToSubmit.isEmpty) {
        logger('No tasks require report submission');
        return;
      }

      final reportSubmissionStartTime = DateTime.now();
      logger('🚀 Starting report submission for ${tasksToSubmit.length} tasks');

      // Submit each task individually
      for (final task in tasksToSubmit) {
        try {
          final taskStartTime = DateTime.now();
          // Create submit report request for this task
          final submitRequest = await SyncUtils.createSubmitReportRequest(
            task: task,
          );

          // Submit the report using the new batch API
          final submitResult = await sl<HomeRepository>().submitReportBatch(
            submitRequest,
          );
          final taskDuration = DateTime.now().difference(taskStartTime);

          if (submitResult.isSuccess) {
            logger(
                '✅ Report submitted successfully for task ${task.taskId} in ${taskDuration.inMilliseconds}ms');

            // Clear sync pending status for this task
            await _clearTaskSyncPending(task.taskId?.toString() ?? '');
          } else {
            logger(
                '❌ Report submission failed for task ${task.taskId} after ${taskDuration.inMilliseconds}ms: ${submitResult.error}');
          }
        } catch (e) {
          logger('❌ Error submitting report for task ${task.taskId}: $e');
        }
      }

      final reportSubmissionDuration =
          DateTime.now().difference(reportSubmissionStartTime);
      logger(
          '✅ Report submission process completed in ${reportSubmissionDuration.inMilliseconds}ms');
    } catch (e) {
      logger('❌ Error during report submission: $e');
    }
  }

  /// Clear sync pending status for a task
  Future<void> _clearTaskSyncPending(String taskId) async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return;
      }

      realm.write(() {
        task.syncPending = false;
        task.isSynced = true;
      });

      logger('Successfully cleared sync pending status for task $taskId');
    } catch (e) {
      logger('Error clearing sync pending status for task $taskId: $e');
    }
  }

  // Add new sync methods for emulation support
  Future<void> _syncHistory({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetHistoryUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing history: $e');
    }
  }

  Future<void> _syncAlerts({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetAlertsUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing alerts: $e');
    }
  }

  Future<void> _syncAvailability({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetAvailabilityUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing availability: $e');
    }
  }

  Future<void> _syncLeave({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetLeaveUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing leave: $e');
    }
  }

  Future<void> _syncSkills({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetSkillsUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing skills: $e');
    }
  }

  Future<void> _syncInduction({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetInductionUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing induction: $e');
    }
  }

  Future<void> _updatePos({String? userId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = userId ?? await sl<DataManager>().getUserId() ?? '';
      final String deviceUid = await sl<DataManager>().getOrCreateDeviceId();

      var items =
          RealmDatabase.instance.realm.all<PosResponseItemModel>().toList();
      final posItems = PosResponseItemMapper.toEntityList(items);
      List<PosRequestEntity> reqItems = [];

      for (var i = 0; i < posItems.length; i++) {
        reqItems.add(PosRequestEntity(
          taskId: posItems[i].taskId,
          storeName: posItems[i].storeName,
          clientName: posItems[i].clientName,
          cycle: posItems[i].cycle,
          rangeStart: posItems[i].rangeStart,
          rangeEnd: posItems[i].rangeEnd,
          scheduledDate: posItems[i].scheduledDate,
          received: posItems[i].received,
        ));
      }

      // Create a basic request to sync POS data for the emulated user
      final updatePosRequest = UpdatePosRequestEntity(
        token: token,
        userId: int.tryParse(id),
        pos: reqItems, // Empty array for sync request
        deviceuid: deviceUid,
      );

      await sl<UpdatePosUseCase>().call(updatePosRequest);
      logger('✅ POS sync completed for user: $id');
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing update pos: $e');
    }
  }

  Future<void> _syncUserState({String? emulatedUserId}) async {
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken() ?? '';
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
      final appInfoService = sl<AppInfoService>();
      final deviceInfo = sl<DeviceInfoService>();
      final String actualAppVersion = await appInfoService.getVersion();

      final request = UserStateRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        devicePlatform: deviceInfo.getDevicePlatform(),
        appversion: actualAppVersion,
        token: token,
      );

      logger('🚀 Starting user state sync');

      final result = await sl<GetUserStateUseCase>().call(request: request);

      if (result.isSuccess && result.data?.data != null) {
        final userStateData = result.data!.data!;

        // Save preferences
        await dataManager.saveAdminAccess(userStateData.adminAccess);
        await dataManager.saveCreateTask(userStateData.createTask);
        await dataManager.saveSupportNumber(userStateData.supportNumber);

        logger(
            '✅ User state preferences saved: adminAccess=${userStateData.adminAccess}, createTask=${userStateData.createTask}, supportNumber=${userStateData.supportNumber}');

        // Process address/skill validation
        await _processAddressSkillValidationDataFromServer(
            userStateData.warningTitle);

        logger('✅ User state sync completed successfully');
      } else {
        logger('❌ User state sync failed: ${result.error}');
      }
    } catch (e) {
      logger('❌ Error during user state sync: $e');
    }
  }

  Future<void> _processAddressSkillValidationDataFromServer(
      String warningTitle) async {
    try {
      final realm = sl<RealmDatabase>().realm;

      // Create entity from warning title
      final validationEntity =
          AddressSkillValidationEntity.fromWarningTitle(warningTitle);

      realm.write(() {
        // Delete existing validation records
        final existingModels = realm.all<AddressSkillValidationModel>();
        realm.deleteMany(existingModels);

        // Add new validation model
        final newModel = AddressSkillValidationMapper.toModel(validationEntity);
        realm.add(newModel);
      });

      logger(
          '✅ Address/Skill validation data processed for warningTitle: $warningTitle');
    } catch (e) {
      logger('❌ Error processing address/skill validation data: $e');
    }
  }

  /// Perform user state validation after sync completion
  Future<void> _performUserStateValidation({String? emulatedUserId}) async {
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken() ?? '';
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
      final appInfoService = sl<AppInfoService>();
      final String actualAppVersion = await appInfoService.getVersion();
      final deviceInfo = sl<DeviceInfoService>();

      final request = UserStateRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        devicePlatform: deviceInfo.getDevicePlatform(),
        appversion: actualAppVersion,
        token: token,
      );

      logger('🚀 Starting user state validation');

      final result = await sl<GetUserStateUseCase>().call(request: request);

      if (result.isSuccess && result.data?.data != null) {
        final userStateData = result.data!.data!;

        logger(
            '📊 User state data: active=${userStateData.active}, appOldVersion=${userStateData.appOldVersion}, gracePeriod=${userStateData.gracePeriod}, kickOutUser=${userStateData.kickOutUser}, appOtherWarning=${userStateData.appOtherWarning}');

        // Check if user is active
        if (!userStateData.active && (emulatedUserId == null)) {
          logger('❌ User is inactive - kicking out');
          await _kickOutUser();
          return;
        }

        // Check for address/skills validation issues first (higher priority)
        if (userStateData.appOtherWarning) {
          final loginResponse = await dataManager.getLoginResponse();
          final isAutoScheduleEnabled =
              loginResponse?.data?.premAutoSchedule ?? false;

          if (isAutoScheduleEnabled) {
            logger(
                '⚠️ Address/Skills validation required - showing custom message and redirecting');
            await _kickOutUserWithCustomMessage(
              userStateData.warningMessage,
              userStateData.warningTitle,
              userStateData.kickOutUser,
            );
            return;
          }
        }

        // Check for app version issues
        if (userStateData.appOldVersion && !userStateData.gracePeriod) {
          if (userStateData.kickOutUser) {
            logger(
                '❌ App version outdated and grace period expired - kicking out');
            await _kickOutUserWithCustomMessage(
              userStateData.warningMessage,
              userStateData.warningTitle,
              true,
            );
          } else {
            logger(
                '⚠️ App version outdated but user not being kicked out - showing warning');
            await _kickOutUserWithCustomMessage(
              userStateData.warningMessage,
              userStateData.warningTitle,
              false,
            );
          }
        }

        logger('✅ User state validation completed successfully');
      } else {
        logger('❌ User state validation failed: ${result.error}');
      }
    } catch (e) {
      logger('❌ Error during user state validation: $e');
    }
  }

  /// Kick out inactive user (user is inactive)
  Future<void> _kickOutUser() async {
    try {
      logger('🚪 Kicking out inactive user');

      // Clear all user data
      await sl<DataManager>().clearAll();
      sl<RealmDatabase>().clearAllData();

      // Notify UI layer to show kick out dialog and navigate to login
      userStateValidationResult.value = {
        'action': 'kickOut',
        'title': 'Invalid User Session',
        'message':
            'You have been logged out as we have detected multiple user session on a similar device.',
        'navigateToLogin': true,
      };

      logger('✅ User kicked out successfully');
    } catch (e) {
      logger('❌ Error during user kick out: $e');
    }
  }

  /// Kick out user with custom message (app version or address/skills issues)
  Future<void> _kickOutUserWithCustomMessage(
    String warningMessage,
    String warningTitle,
    bool kickOut,
  ) async {
    try {
      logger('🚪 Kicking out user with custom message: $warningTitle');

      if (kickOut) {
        // Clear user data if kicking out
        await sl<DataManager>().clearAll();
        sl<RealmDatabase>().clearAllData();

        // Sign out MSAL if applicable
        final dataManager = sl<DataManager>();
        final userEmail = await dataManager.getEmail();
        final isMsalUser = userEmail?.contains('@dkshsmollan.com') == true ||
            userEmail?.contains('@dksh.com') == true;

        if (isMsalUser) {
          final msalAuthService = sl<MsalAuthService>();
          if (msalAuthService.isInitialized) {
            await msalAuthService.signOut();
          }
        }
      }

      // Notify UI layer to show custom message dialog
      userStateValidationResult.value = {
        'action': 'customMessage',
        'title': warningTitle,
        'message': warningMessage,
        'kickOut': kickOut,
        'navigateToLogin': kickOut,
        'checkAddressSkills': !kickOut,
      };

      // If not kicking out, check address/skills validation after user dismisses dialog
      if (!kickOut) {
        await _checkAddressSkillsValidation();
      }

      logger('✅ User kick out with custom message completed');
    } catch (e) {
      logger('❌ Error during user kick out with custom message: $e');
    }
  }

  /// Redirect to profile for address/skills validation
  Future<void> _redirectToProfile() async {
    try {
      logger('🔄 Redirecting to profile for address/skills validation');

      // Notify UI layer to navigate to profile
      userStateValidationResult.value = {
        'action': 'redirectToProfile',
        'navigateToProfile': true,
      };

      logger('✅ Redirect to profile completed');
    } catch (e) {
      logger('❌ Error during profile redirect: $e');
    }
  }

  Future<void> _syncOpenedDocs({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('📄 Starting opened docs sync for user: $userId');

      final docsService = sl<DocsInteractionService>();
      final openedDocs = await docsService.getOpenedDocs();

      if (openedDocs.isEmpty) {
        logger('No opened docs to sync for user: $userId');
        return;
      }

      logger(
          'Found ${openedDocs.length} opened docs to sync for user: $userId');

      final dataManager = sl<DataManager>();
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String token = await dataManager.getFcmToken() ?? '';

      final request = OpenedDocRequestEntity(
        userId: id,
        deviceToken: token,
        messageRead: openedDocs,
      );

      final result = await sl<SendOpenedDocsUseCase>().call(request);

      if (result.isSuccess) {
        logger('✅ Opened docs sync successful for user: $userId');

        // Clear local interactions after successful sync
        final clearResult = await docsService.clearOpenedDocs();
        if (clearResult) {
          logger('✅ Local opened docs cleared successfully for user: $userId');
        } else {
          logger('⚠️ Failed to clear local opened docs for user: $userId');
        }
      } else {
        logger(
            '❌ Opened docs sync failed for user: $userId - Error: ${result.error}');
      }
    } catch (e) {
      logger('❌ Error syncing opened docs for user: $userId - Error: $e');
    }
  }

  /// Check if address/skills validation is needed
  Future<void> _checkAddressSkillsValidation() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final validationModel =
          realm.all<AddressSkillValidationModel>().firstOrNull;

      if (validationModel == null) {
        logger('⚠️ No address/skills validation data found');
        return;
      }

      final validationEntity =
          AddressSkillValidationMapper.toEntity(validationModel);

      // Check if any validation is missing
      final needsValidation = !validationEntity.isAddressValidated ||
          !validationEntity.isSkillValidated ||
          !validationEntity.isAvailabilityValidated;

      logger('⚠️ Address/Skills validation needed');
      logger('   - Address validated: ${validationEntity.isAddressValidated}');
      logger('   - Skills validated: ${validationEntity.isSkillValidated}');
      logger(
          '   - Availability validated: ${validationEntity.isAvailabilityValidated}');

      if (needsValidation) {
        final dataManager = sl<DataManager>();
        final loginResponse = await dataManager.getLoginResponse();
        final isAutoScheduleEnabled =
            loginResponse?.data?.premAutoSchedule ?? false;

        if (isAutoScheduleEnabled) {
          logger(
              '🔄 Address/Skills validation needed - redirecting to profile');
          await _redirectToProfile();
        }
      } else {
        logger('✅ Address/Skills validation complete');
      }
    } catch (e) {
      logger('❌ Error during address/skills validation check: $e');
    }
  }

  Future<void> _syncCountry({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetCountryUseCase>().call(
        token: token,
        userId: id,
        isSync: true,
      );
      logger('✅ Country sync completed');
    } catch (e) {
      logger('❌ Error syncing country: $e');
    }
  }

  Future<void> _syncState({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetStateUseCase>().call(
        token: token,
        userId: id,
        isSync: true,
      );
      logger('✅ State sync completed');
    } catch (e) {
      logger('❌ Error syncing state: $e');
    }
  }

  /// Perform version check after sync completion
  Future<void> _performVersionCheck({String? emulatedUserId}) async {
    try {
      logger('🚀 Starting version check');

      final result = await sl<CheckVersionUseCase>().call();

      logger('Version check result: ${result.data?.toRawJson()}');

      if (result.isSuccess && result.data != null) {
        final versionData = result.data!;

        // Get current app version
        final appInfoService = sl<AppInfoService>();
        final currentVersion = await appInfoService.getVersion();

        // Get latest version and download URL based on platform
        final latestVersion = Platform.isAndroid
            ? versionData.androidVersion
            : versionData.iOsVersion;
        final downloadUrl = Platform.isAndroid
            ? versionData.downloadLinkAndroid
            : versionData.downloadLinkIos;

        logger(
            'Current version: $currentVersion, Latest version: $latestVersion');

        if (latestVersion != null &&
            _isVersionOutdated(currentVersion, latestVersion) &&
            downloadUrl != null &&
            downloadUrl.isNotEmpty) {
          logger('⚠️ App version is outdated - notifying UI layer');

          // Save download URL to DataManager for persistent storage
          await sl<DataManager>().saveNewVersionDownloadUrl(downloadUrl);

          // Notify UI layer to show version update dialog
          versionUpdateResult.value = {
            'action': 'versionUpdate',
            'downloadUrl': downloadUrl,
          };
        } else {
          logger('✅ App version is up to date');
        }
      } else {
        logger('❌ Version check failed: ${result.error}');
      }
    } catch (e) {
      logger('❌ Error during version check: $e');
    }
  }

  /// Show no internet connection dialog with single OK button
  Future<void> _showNoInternetDialog(BuildContext context) async {
    if (!context.mounted) return;

    await ConfirmDialog.show(
      context: context,
      title: 'No Internet Connection',
      message:
          'Sync requires an internet connection. Please check your connection and try again.',
      confirmText: 'OK',
      onConfirm: () {
        // User acknowledged, do nothing
        logger('User acknowledged no internet connection');
      },
    );
  }

  /// Compare version strings to check if current version is older than latest version
  bool _isVersionOutdated(String currentVersion, String latestVersion) {
    try {
      final currentParts = currentVersion.split('.').map(int.parse).toList();
      final latestParts = latestVersion.split('.').map(int.parse).toList();

      // Pad arrays to same length
      final maxLength = [currentParts.length, latestParts.length]
          .reduce((a, b) => a > b ? a : b);
      while (currentParts.length < maxLength) {
        currentParts.add(0);
      }
      while (latestParts.length < maxLength) {
        latestParts.add(0);
      }

      // Compare version parts
      for (int i = 0; i < maxLength; i++) {
        if (currentParts[i] < latestParts[i]) return true;
        if (currentParts[i] > latestParts[i]) return false;
      }

      return false; // Equal versions
    } catch (e) {
      logger('❌ Error comparing versions: $e');
      return false; // If parsing fails, assume not outdated
    }
  }
}
