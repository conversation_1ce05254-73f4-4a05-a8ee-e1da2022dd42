import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/brief_response_entity.dart';

abstract class FullBriefState extends Equatable {
  const FullBriefState();

  @override
  List<Object?> get props => [];
}

class FullBriefInitial extends FullBriefState {}

class FullBriefLoading extends FullBriefState {}

class FullBriefSuccess extends FullBriefState {
  final BriefResponseEntity response;

  const FullBriefSuccess(this.response);

  @override
  List<Object?> get props => [response];
}

class FullBriefError extends FullBriefState {
  final String message;

  const FullBriefError(this.message);

  @override
  List<Object?> get props => [message];
}
