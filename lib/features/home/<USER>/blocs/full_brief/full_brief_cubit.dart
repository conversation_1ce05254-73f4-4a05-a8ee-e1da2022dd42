import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/brief_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/brief_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_brief_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'full_brief_state.dart';

class FullBriefCubit extends Cubit<FullBriefState> {
  final GetBriefUseCase _getBriefUseCase;

  FullBriefCubit(this._getBriefUseCase) : super(FullBriefInitial());

  Future<void> getBrief(String taskId, String token) async {
    emit(FullBriefLoading());

    try {
      logger("FullBriefCubit: Starting getBrief for taskId: $taskId");

      final request = BriefRequestEntity(
        token: token,
        taskId: taskId,
      );

      logger("FullBriefCubit: Calling API with request: ${request.toJson()}");

      final Result<BriefResponseEntity> result =
          await _getBriefUseCase(request);

      logger("FullBriefCubit: API call completed");
      logger("FullBriefCubit: Result isSuccess: ${result.isSuccess}");
      logger("FullBriefCubit: Result data: ${result.data?.toJson()}");

      if (result.isSuccess && result.data != null) {
        logger("FullBriefCubit: Emitting success state");
        emit(FullBriefSuccess(result.data!));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching brief.';
        logger("FullBriefCubit: Emitting error state: $errorMessage");
        emit(FullBriefError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in FullBriefCubit: $e");
      emit(FullBriefError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(FullBriefInitial());
  }
}
