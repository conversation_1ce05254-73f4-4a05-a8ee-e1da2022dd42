import 'package:bloc/bloc.dart';

import '../../data/models/notification_req.dart';
import '../../domain/usecases/get_notification_usecase.dart';
import 'notification_state.dart';

class NotificationCubit extends Cubit<NotificationState> {
  /// Constructor
  NotificationCubit(
    this._getNotificationUseCase,
  ) : super(NotificationInitial());

  final GetNotificationUseCase _getNotificationUseCase;

  /// Get Alerts
  Future<void> fetchAlerts(
    NotificationReqParams request, {
    bool isSync = false,
  }) async {
    emit(NotificationLoading());
    final result = await _getNotificationUseCase.call(request, isSync: isSync);

    if (result.isSuccess && result.data != null) {
      var data = result.data;
      // sort by ascending date
      data?.data.alerts.sort((a, b) => b.date.compareTo(a.date));
      emit(NotificationLoaded(result.data!));
    } else {
      emit(NotificationError(result.error ?? 'Failed to fetch notifications'));
    }
  }
}
