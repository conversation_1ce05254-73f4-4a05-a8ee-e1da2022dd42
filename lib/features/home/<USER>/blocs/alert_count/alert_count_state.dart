import 'package:equatable/equatable.dart';

abstract class AlertCountState extends Equatable {
  const AlertCountState();

  @override
  List<Object> get props => [];
}

class AlertCountInitial extends AlertCountState {}

class AlertCountLoading extends AlertCountState {}

class AlertCountLoaded extends AlertCountState {
  final int count;

  const AlertCountLoaded(this.count);

  @override
  List<Object> get props => [count];
}

class AlertCountError extends AlertCountState {
  final String message;

  const AlertCountError(this.message);

  @override
  List<Object> get props => [message];
}
