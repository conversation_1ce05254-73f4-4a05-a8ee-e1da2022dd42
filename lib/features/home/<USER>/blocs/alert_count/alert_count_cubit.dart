import 'package:bloc/bloc.dart';
import '../../../../../core/storage/data_manager.dart';
import '../../../../../core/utils/logger.dart';
import 'alert_count_state.dart';

class AlertCountCubit extends Cubit<AlertCountState> {
  AlertCountCubit(this._dataManager) : super(AlertCountInitial());

  final DataManager _dataManager;

  /// Load the current alert count from storage
  Future<void> loadAlertCount() async {
    try {
      emit(AlertCountLoading());
      final count = await _dataManager.getAlertCount();
      emit(AlertCountLoaded(count));
    } catch (e) {
      logger('Error loading alert count: $e');
      emit(const AlertCountError('Failed to load alert count'));
    }
  }

  /// Increment the alert count
  Future<void> incrementAlertCount() async {
    try {
      final currentCount = await _dataManager.getAlertCount();
      final newCount = currentCount + 1;
      await _dataManager.saveAlertCount(newCount);
      emit(AlertCountLoaded(newCount));
      logger('Alert count incremented to: $newCount');
    } catch (e) {
      logger('Error incrementing alert count: $e');
      emit(const AlertCountError('Failed to increment alert count'));
    }
  }

  /// Reset the alert count to zero
  Future<void> resetAlertCount() async {
    try {
      await _dataManager.saveAlertCount(0);
      emit(const AlertCountLoaded(0));
      logger('Alert count reset to 0');
    } catch (e) {
      logger('Error resetting alert count: $e');
      emit(const AlertCountError('Failed to reset alert count'));
    }
  }

  /// Get current count value (if state is loaded)
  int get currentCount {
    if (state is AlertCountLoaded) {
      return (state as AlertCountLoaded).count;
    }
    return 0;
  }
}
