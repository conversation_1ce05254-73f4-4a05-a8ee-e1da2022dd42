import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_available_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_available_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_accepted_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/open_accepted_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/accept_task_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/accept_task_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_open_available_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_open_accepted_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/accept_task_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'open_tasks_state.dart';

class OpenTasksCubit extends Cubit<OpenTasksState> {
  final GetTasksUseCase _getTasksUseCase;
  final GetOpenAvailableUseCase _getOpenAvailableUseCase;
  final GetOpenAcceptedUseCase _getOpenAcceptedUseCase;
  final AcceptTaskUseCase _acceptTaskUseCase;

  OpenTasksCubit(
    this._getTasksUseCase,
    this._getOpenAvailableUseCase,
    this._getOpenAcceptedUseCase,
    this._acceptTaskUseCase,
  ) : super(OpenTasksInitial());

  Future<void> getOpenTasks(TasksRequestEntity request) async {
    emit(OpenTasksLoading());

    try {
      final Result<TasksResponseEntity> result =
          await _getTasksUseCase(request);

      if (result.isSuccess && result.data != null) {
        emit(OpenTasksSuccess(result.data!));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching open tasks.';
        emit(OpenTasksError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in OpenTasksCubit: $e");
      emit(OpenTasksError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  Future<void> getOpenAvailable(String token, String userId) async {
    emit(OpenTasksLoading());

    try {
      final request = OpenAvailableRequestEntity(
        token: token,
        userId: userId,
      );

      final Result<OpenAvailableResponseEntity> result =
          await _getOpenAvailableUseCase(request);

      if (result.isSuccess && result.data != null) {
        emit(OpenAvailableSuccess(result.data!));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching open available tasks.';
        emit(OpenTasksError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in OpenTasksCubit getOpenAvailable: $e");
      emit(OpenTasksError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  Future<void> getOpenAccepted(String token, String userId) async {
    emit(OpenTasksLoading());

    try {
      final request = OpenAcceptedRequestEntity(
        token: token,
        userId: userId,
      );

      final Result<OpenAcceptedResponseEntity> result =
          await _getOpenAcceptedUseCase(request);

      if (result.isSuccess && result.data != null) {
        emit(OpenAcceptedSuccess(result.data!));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching open accepted tasks.';
        emit(OpenTasksError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in OpenTasksCubit getOpenAccepted: $e");
      emit(OpenTasksError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  Future<void> acceptTask(String taskId, String userId, String token,
      String deviceUid, String appVersion) async {
    emit(AcceptTaskLoading());

    try {
      // Create a request to accept the task
      final request = AcceptTaskRequestEntity(
        token: token,
        userId: userId,
        taskId: taskId,
        dateScheduled: DateTime.now()
            .toIso8601String()
            .split('T')[0], // Today's date in YYYY-MM-DD format
      );

      final Result<AcceptTaskResponseEntity> result =
          await _acceptTaskUseCase(request);

      if (result.isSuccess && result.data != null) {
        emit(AcceptTaskSuccess(result.data!.message));
      } else {
        final errorMessage =
            result.error?.toString() ?? 'Failed to accept task.';
        emit(AcceptTaskError(errorMessage));
      }
    } catch (e) {
      logger("Error accepting task: $e");
      emit(AcceptTaskError('Failed to accept task: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(OpenTasksInitial());
  }
}
