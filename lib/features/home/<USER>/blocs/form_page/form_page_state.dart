import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;

abstract class FormPageState extends Equatable {
  const FormPageState();

  @override
  List<Object?> get props => [];
}

class FormPageInitial extends FormPageState {}

class FormPageLoading extends FormPageState {}

class FormPageLoaded extends FormPageState {
  final entities.TaskDetail task;

  const FormPageLoaded(this.task);

  @override
  List<Object?> get props => [task];
}

class FormPageProgressLoading extends FormPageState {
  final entities.TaskDetail task;

  const FormPageProgressLoading(this.task);

  @override
  List<Object?> get props => [task];
}

class FormPageProgressLoaded extends FormPageState {
  final entities.TaskDetail task;
  final Map<int, Map<String, dynamic>> progressData;

  const FormPageProgressLoaded(this.task, this.progressData);

  @override
  List<Object?> get props => [task, progressData];
}

class FormPageRefreshing extends FormPageState {}

class FormPageError extends FormPageState {
  final String message;

  const FormPageError(this.message);

  @override
  List<Object?> get props => [message];
}
