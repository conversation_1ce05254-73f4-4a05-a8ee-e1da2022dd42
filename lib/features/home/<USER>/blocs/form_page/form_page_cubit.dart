import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/core/utils/sync_utils.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/refresh_forms_usecase.dart';

import 'form_page_state.dart';

class FormPageCubit extends Cubit<FormPageState> {
  final RefreshFormsUseCase? _refreshFormsUseCase;

  FormPageCubit({RefreshFormsUseCase? refreshFormsUseCase})
      : _refreshFormsUseCase = refreshFormsUseCase,
        super(FormPageInitial());

  // Cache for progress data to avoid recalculating on every build
  final Map<int, Map<String, dynamic>> _progressCache = {};

  /// Load TaskDetail from database using taskId
  Future<void> loadTask(int taskId) async {
    try {
      emit(FormPageLoading());

      // Use FormUtils to get task by ID
      final taskDetail = await FormUtils.getTaskById(taskId);

      if (taskDetail == null) {
        emit(const FormPageError('Task not found in database'));
        return;
      }

      emit(FormPageLoaded(taskDetail));

      // Load initial progress data
      await refreshAllProgress(taskId);
    } catch (e) {
      logger('FormPageCubit: Error loading task $taskId: $e');
      emit(FormPageError('Error loading task: ${e.toString()}'));
    }
  }

  /// Refresh progress data for all forms by clearing cache and recalculating
  Future<void> refreshAllProgress(int taskId) async {
    final currentState = state;
    entities.TaskDetail? task;

    // Extract task from any state that has it
    if (currentState is FormPageLoaded) {
      task = currentState.task;
    } else if (currentState is FormPageProgressLoading) {
      task = currentState.task;
    } else if (currentState is FormPageProgressLoaded) {
      task = currentState.task;
    }

    if (task == null) return;

    try {
      // Clear cache to force recalculation and ensure fresh data
      _progressCache.clear();

      // Emit loading state with existing data
      emit(FormPageProgressLoading(task));

      // Calculate progress for all forms
      final forms = List.from(task.forms ?? []);
      final Map<int, Map<String, dynamic>> progressData = {};

      for (final form in forms) {
        final formId = form.formId?.toInt();
        if (formId != null) {
          // Always calculate fresh progress (don't use cache during refresh)
          final progress = await _calculateFormProgressFresh(taskId, form);
          progressData[formId] = progress;
        }
      }

      // Cache the progress data after calculation
      for (final entry in progressData.entries) {
        _progressCache[entry.key] = Map<String, dynamic>.from(entry.value);
      }

      // Emit loaded state with progress data
      emit(FormPageProgressLoaded(task, progressData));
    } catch (e) {
      logger('FormPageCubit: Error refreshing progress: $e');
      emit(FormPageLoaded(task)); // Fallback to basic loaded state
    }
  }

  /// Calculate fresh progress without checking cache (used during refresh)
  Future<Map<String, dynamic>> _calculateFormProgressFresh(
      int taskId, entities.Form form) async {
    final formId = form.formId?.toInt();
    if (formId == null) {
      return {'progress': 0.0, 'progressText': '0 of 0', 'isCompleted': false};
    }

    try {
      // Use FormUtils to get task with form model
      final taskData = await FormUtils.getTaskWithFormModel(taskId, formId);

      if (taskData == null) {
        return await _getFallbackProgress(taskId, form);
      }

      final formModel = taskData['formModel'];

      // Use kTotal, kCompleted, and kIsCompleted from database if available
      final kTotal = formModel.kTotal;
      final kCompleted = formModel.kCompleted;
      final kIsCompleted = formModel.kIsCompleted;

      if (kTotal != null && kCompleted != null) {
        // Use simplified progress tracking from database
        final progress = kTotal > 0 ? kCompleted / kTotal : 0.0;
        final progressText = '$kCompleted of $kTotal';

        return {
          'progress': progress,
          'progressText': progressText,
          'isCompleted': kIsCompleted ?? false,
        };
      } else {
        // Simple fallback: 0 completed, total = question count
        final totalQuestions = form.questions?.length ?? 0;
        return {
          'progress': 0.0,
          'progressText': '0 of $totalQuestions',
          'isCompleted': false,
        };
      }
    } catch (e) {
      logger('FormPageCubit: Error calculating progress for form $formId: $e');
      return await _getFallbackProgress(taskId, form);
    }
  }

  /// Get fallback progress based on total question count using FormUtils
  Future<Map<String, dynamic>> _getFallbackProgress(
      int taskId, entities.Form form) async {
    try {
      // Use FormUtils.getQuestionPageItemsCount to get accurate visible question count
      final itemsCount = await FormUtils.getQuestionPageItemsCount(
        formId: form.formId!,
        taskId: taskId,
      );

      final totalQuestions = itemsCount.totalVisible;
      return {
        'progress': 0.0,
        'progressText': '0 of $totalQuestions',
        'isCompleted': false,
      };
    } catch (e) {
      logger(
          'FormPageCubit: Error getting fallback progress for form ${form.formId}: $e');
      // Fallback to basic question count if FormUtils fails
      final questions = form.questions ?? [];
      final totalQuestions = questions.length;
      return {
        'progress': 0.0,
        'progressText': '0 of $totalQuestions',
        'isCompleted': false,
      };
    }
  }

  /// Get cached progress data for a form
  Map<String, dynamic>? getCachedProgress(int formId) {
    return _progressCache[formId];
  }

  /// Clear the progress cache
  void clearProgressCache() {
    _progressCache.clear();
  }

  /// Force refresh by reloading task data and progress from database
  Future<void> forceRefresh(int taskId) async {
    try {
      emit(FormPageLoading());

      // Clear all cached data
      _progressCache.clear();

      // Reload task from database
      final taskDetail = await FormUtils.getTaskById(taskId);

      if (taskDetail == null) {
        emit(const FormPageError('Task not found in database'));
        return;
      }

      emit(FormPageLoaded(taskDetail));

      // Load fresh progress data
      await refreshAllProgress(taskId);
    } catch (e) {
      logger('FormPageCubit: Error force refreshing task $taskId: $e');
      emit(FormPageError('Error refreshing task: ${e.toString()}'));
    }
  }

  /// Refresh forms by calling the refreshTasks API
  Future<void> refreshForms(int taskId) async {
    if (_refreshFormsUseCase == null) {
      logger('FormPageCubit: RefreshFormsUseCase not available');
      return;
    }

    try {
      // Get current state to preserve the task
      final currentState = state;
      entities.TaskDetail? currentTask;

      if (currentState is FormPageLoaded) {
        currentTask = currentState.task;
      } else if (currentState is FormPageProgressLoaded) {
        currentTask = currentState.task;
      }

      // Show refreshing state
      emit(FormPageRefreshing());

      // Create refresh request using SyncUtils
      final refreshRequest =
          await SyncUtils().prepareRefreshTasksRequest(taskId.toString());

      // Call the API to refresh forms
      final result = await _refreshFormsUseCase(refreshRequest);

      if (result.isSuccess) {
        logger('FormPageCubit: Successfully refreshed forms');

        // Reload the task and refresh progress
        await forceRefresh(taskId);
      } else {
        logger('FormPageCubit: Failed to refresh forms: ${result.error}');

        // If refresh fails, restore previous state or show error
        if (currentTask != null) {
          emit(FormPageLoaded(currentTask));
          await refreshAllProgress(taskId);
        } else {
          emit(FormPageError('Failed to refresh forms: ${result.error}'));
        }
      }
    } catch (e) {
      logger('FormPageCubit: Error refreshing forms: ${e.toString()}');
      emit(FormPageError('Error refreshing forms: ${e.toString()}'));
    }
  }

  /// Reset cubit state
  void reset() {
    _progressCache.clear();
    emit(FormPageInitial());
  }
}
