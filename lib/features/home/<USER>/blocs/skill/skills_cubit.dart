import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/skills_response.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_skills_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/save_skills_usecase.dart';

part 'skills_state.dart';

class SkillsCubit extends Cubit<SkillsState> {
  final GetSkillsUseCase getSkillsUseCase;
  final SaveSkillsUseCase saveSkillsUseCase;

  SkillsCubit({
    required this.getSkillsUseCase,
    required this.saveSkillsUseCase,
  }) : super(SkillsLoading());

  Future<void> fetchSkills({bool isSync = false}) async {
    emit(SkillsLoading());
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken();
      final userId = await dataManager.getUserId();

      if (token != null && userId != null) {
        final result = await getSkillsUseCase.call(
          token: token,
          userId: userId,
          isSync: isSync,
        );

        if (result.isSuccess && result.data != null) {
          emit(SkillsLoaded(result.data!));
        } else {
          emit(SkillsError(result.error ?? 'Failed to fetch skills'));
        }
      } else {
        emit(const SkillsError('Authentication required'));
      }
    } catch (e) {
      emit(SkillsError(e.toString()));
    }
  }

  void toggleEditMode() {
    final currentState = state;
    if (currentState is SkillsLoaded) {
      emit(SkillsLoaded(currentState.response,
          isEditMode: !currentState.isEditMode));
    }
  }

  void toggleSkillSelection(int skillId) {
    final currentState = state;
    if (currentState is SkillsLoaded) {
      final updatedSkills = currentState.response.data?.skills.map((skill) {
        if (skill.skillId == skillId) {
          return skill.copyWith(
            has: !skill.has,
            isEdit: true,
          );
        }
        return skill;
      }).toList();

      if (updatedSkills != null) {
        final updatedData = SkillsData(skills: updatedSkills);
        final updatedResponse = SkillsResponse(data: updatedData);
        emit(
            SkillsLoaded(updatedResponse, isEditMode: currentState.isEditMode));
      }
    }
  }

  Future<void> saveSkillChanges() async {
    final currentState = state;
    if (currentState is SkillsLoaded) {
      emit(SkillsSaving(currentState.response));

      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token != null && userId != null) {
          // Prepare the skills data for API
          final skillsToSave = currentState.response.data?.skills
                  .where((skill) => skill.isEdit == true)
                  .map((skill) => {
                        'skill_id': skill.skillId.toString(),
                        'has': skill.has,
                      })
                  .toList() ??
              [];

          final result = await saveSkillsUseCase.call(
            token: token,
            userId: userId,
            skills: skillsToSave,
          );

          if (result.isSuccess) {
            // Reset edit flags after successful save
            final updatedSkills =
                currentState.response.data?.skills.map((skill) {
              return skill.copyWith(isEdit: false);
            }).toList();

            if (updatedSkills != null) {
              final updatedData = SkillsData(skills: updatedSkills);
              final updatedResponse = SkillsResponse(data: updatedData);
              emit(SkillsSaved(updatedResponse));

              // Return to loaded state
              emit(SkillsLoaded(updatedResponse, isEditMode: false));
            }
          } else {
            emit(SkillsError(result.error ?? 'Failed to save skill changes'));
          }
        } else {
          emit(const SkillsError('Authentication required'));
        }
      } catch (e) {
        emit(SkillsError(e.toString()));
      }
    }
  }
}
