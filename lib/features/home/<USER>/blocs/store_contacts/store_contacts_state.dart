part of 'store_contacts_cubit.dart';

abstract class StoreContactsState extends Equatable {
  const StoreContactsState();

  @override
  List<Object?> get props => [];
}

class StoreContactsLoading extends StoreContactsState {}

class StoreContactsLoaded extends StoreContactsState {
  final StoreContactResponse response;

  const StoreContactsLoaded(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreContactsSaving extends StoreContactsState {
  final StoreContactResponse response;

  const StoreContactsSaving(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreContactsSaved extends StoreContactsState {
  final StoreContactResponse response;

  const StoreContactsSaved(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreContactsError extends StoreContactsState {
  final String message;

  const StoreContactsError(this.message);

  @override
  List<Object?> get props => [message];
}

// Store Contact Types States
class StoreContactTypesLoading extends StoreContactsState {}

class StoreContactTypesLoaded extends StoreContactsState {
  final StoreContactTypesResponse response;

  const StoreContactTypesLoaded(this.response);

  @override
  List<Object?> get props => [response];
}

class StoreContactTypesError extends StoreContactsState {
  final String message;

  const StoreContactTypesError(this.message);

  @override
  List<Object?> get props => [message];
}

// Combined States for handling both contacts and contact types
class StoreContactsWithTypesLoaded extends StoreContactsState {
  final StoreContactResponse contactsResponse;
  final StoreContactTypesResponse typesResponse;

  const StoreContactsWithTypesLoaded(this.contactsResponse, this.typesResponse);

  @override
  List<Object?> get props => [contactsResponse, typesResponse];
}

class StoreContactsInitialLoading extends StoreContactsState {}

class StoreContactsPartiallyLoaded extends StoreContactsState {
  final bool contactsLoaded;
  final bool typesLoaded;
  final StoreContactResponse? contactsResponse;
  final StoreContactTypesResponse? typesResponse;

  const StoreContactsPartiallyLoaded({
    required this.contactsLoaded,
    required this.typesLoaded,
    this.contactsResponse,
    this.typesResponse,
  });

  @override
  List<Object?> get props =>
      [contactsLoaded, typesLoaded, contactsResponse, typesResponse];
}
