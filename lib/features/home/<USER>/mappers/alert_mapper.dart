import '../models/alert_model.dart';
import '../models/notification_response.dart';

class AlertMapper {
  static AlertModel toModel(NotificationResponse response) {
    // Convert response alerts to alert item models
    final alertItems = response.data.alerts
        .map((alert) => AlertItemModel(
              alert.alertId.toInt(),
              alert.clientId.toInt(),
              alert.userId.toInt(),
              alert.title,
              alert.shortDescription,
              alert.comment,
              alert.sender,
              alert.date,
              alert.clientLogoUrl,
              alert.isRead,
            ))
        .toList();

    final alertModel = AlertModel(
      "alerts",
      alerts: alertItems,
    );

    return alertModel;
  }

  static NotificationResponse toEntity(AlertModel model) {
    // Convert alert item models back to notification response
    final alerts = model.alerts
        .map((alertItem) => Alert(
              alertId: alertItem.alertId,
              clientId: alertItem.clientId,
              userId: alertItem.userId,
              title: alertItem.title,
              shortDescription: alertItem.shortDescription,
              comment: alertItem.comment,
              sender: alertItem.sender,
              date: alertItem.date,
              clientLogoUrl: alertItem.clientLogoUrl,
              isRead: alertItem.isRead,
            ))
        .toList();

    return NotificationResponse(
      data: NotificationData(alerts: alerts),
    );
  }

  static AlertModel updateModel({
    required AlertModel existingModel,
    required NotificationResponse response,
  }) {
    // Clear existing alerts and add new ones
    existingModel.alerts.clear();
    existingModel.alerts.addAll(
      response.data.alerts
          .map((alert) => AlertItemModel(
                alert.alertId.toInt(),
                alert.clientId.toInt(),
                alert.userId.toInt(),
                alert.title,
                alert.shortDescription,
                alert.comment,
                alert.sender,
                alert.date,
                alert.clientLogoUrl,
                alert.isRead,
              ))
          .toList(),
    );

    return existingModel;
  }
}
