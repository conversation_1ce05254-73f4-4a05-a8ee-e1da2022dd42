import '../models/opened_doc_item_model.dart';
import '../../domain/entities/opened_doc_entity.dart';

class OpenedDocItemMapper {
  static OpenedDocItemModel toModel(OpenedDocItemEntity entity) {
    return OpenedDocItemModel(
      entity.docId,
      entity.timeStamp,
    );
  }

  static OpenedDocItemEntity toEntity(OpenedDocItemModel model) {
    return OpenedDocItemEntity(
      docId: model.docId,
      timeStamp: model.timeStamp,
    );
  }
}
