import '../models/address_skill_validation_model.dart';
import '../../domain/entities/address_skill_validation_entity.dart';

class AddressSkillValidationMapper {
  static AddressSkillValidationEntity toEntity(
      AddressSkillValidationModel model) {
    return AddressSkillValidationEntity(
      isAddressValidated: model.isAddressValidated,
      isSkillValidated: model.isSkillValidated,
      isAvailabilityValidated: model.isAvailabilityValidated,
    );
  }

  static AddressSkillValidationModel toModel(
      AddressSkillValidationEntity entity) {
    return AddressSkillValidationModel(
      entity.isAddressValidated,
      entity.isSkillValidated,
      entity.isAvailabilityValidated,
    );
  }
}
