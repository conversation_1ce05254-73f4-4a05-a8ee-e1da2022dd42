import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart'
    show PhotoUploadWidget;
import 'package:storetrack_app/shared/widgets/offline_image_widget.dart';

class CheckboxWidget extends StatelessWidget {
  final Measurement measurement;
  final bool? value;
  final Function(bool?) onChanged;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final Function(int?)? onCameraTap;
  final bool isRequired;
  final String? errorText;
  final List<String> selectedImages;
  final String? photoErrorText;
  final PhotoTagsT? photoTag;
  final String? description;
  // New properties for multiple photo tags support
  final List<PhotoTagsT>? photoTags;
  final Map<int, List<String>>? photosByTag;
  // Parameters for direct image capture
  final String? taskId;
  final String? formId;
  final String? questionId;
  final String? questionPartId;
  final VoidCallback? onPhotoAdded;

  const CheckboxWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.errorText,
    this.selectedImages = const [],
    this.photoErrorText,
    this.photoTag,
    this.description,
    this.photoTags,
    this.photosByTag,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.onPhotoAdded,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: description == null ? Colors.white : AppColors.borderBlack,
          width: 1.25,
        ),
        boxShadow: description == null
            ? [
                BoxShadow(
                  color: AppColors.black10,
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ]
            : [],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator and thumbnail
          Row(
            children: [
              // Measurement thumbnail (if available)
              if (description == null &&
                  measurement.measurementImage != null &&
                  measurement.measurementImage!.isNotEmpty &&
                  measurement.measurementImage != "0")
                OfflineImageWidget(
                  url: measurement.measurementImage,
                  width: 60.0,
                  height: 60.0,
                ),
              if (description == null &&
                  measurement.measurementImage != null &&
                  measurement.measurementImage!.isNotEmpty &&
                  measurement.measurementImage != "0")
                const Gap(12),
              Expanded(
                child: Text(
                  description ??
                      measurement.measurementDescription ??
                      'Checkbox',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: AppColors.loginRed,
                    size: 16,
                  ),
                ),
            ],
          ),
          const Gap(16),
          GestureDetector(
            onTap: () {
              // Three-state cycling: null → true → false → null
              if (value == null) {
                onChanged(true);
              } else if (value == true) {
                onChanged(false);
              } else {
                onChanged(null);
              }
            },
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: value == true
                        ? AppColors.primaryBlue
                        : value == false
                            ? AppColors.loginRed
                            : Colors.white,
                    borderRadius: BorderRadius.circular(2),
                    border: Border.all(
                      color: value == true
                          ? AppColors.primaryBlue
                          : value == false
                              ? AppColors.loginRed
                              : AppColors.blackTint2,
                      width: 2,
                    ),
                  ),
                  child: value == true
                      ? const Icon(
                          Icons.check,
                          size: 14,
                          color: Colors.white,
                        )
                      : value == false
                          ? const Icon(
                              Icons.close,
                              size: 14,
                              color: Colors.white,
                            )
                          : null,
                ),
                const Gap(12),
                Expanded(
                  child: Text(
                    value == true
                        ? 'Selected'
                        : (value == false ? 'Not selected' : 'Not answered'),
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Camera section - support multiple PhotoUploadWidgets based on photoTags
          if (showCameraIcon) ...[
            const Gap(16),
            // Use multiple photo tags if available, otherwise fall back to single photo tag
            if (photoTags != null && photoTags!.isNotEmpty)
              ..._buildPhotoUploadWidgets()
            else if (photoTag != null)
              PhotoUploadWidget(
                selectedImages: selectedImages,
                errorText: photoErrorText,
                photoTag: photoTag,
                // Use direct image capture if parameters are available
                onCameraTap: (taskId != null && questionId != null)
                    ? null
                    : () {
                        if (onCameraTap != null) {
                          onCameraTap!(null);
                        }
                      },
                onImagesTap: (photoTagId) {
                  if (onCameraTap != null) {
                    onCameraTap!(photoTagId);
                  }
                },
                // Parameters for direct image capture
                taskId: taskId,
                formId: formId,
                questionId: questionId,
                questionPartId: questionPartId,
                measurementId: measurement.measurementId?.toString(),
                level: 3, // Level 3 for photo_tags_three
                onPhotoAdded: onPhotoAdded,
              ),
          ],
          if (errorText != null)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                errorText!,
                style: textTheme.montserratTableSmall.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Get photos for a specific photo tag ID
  List<String> _getPhotosForTag(int photoTagId) {
    return photosByTag?[photoTagId] ?? [];
  }

  /// Build PhotoUploadWidget for each photo tag
  List<Widget> _buildPhotoUploadWidgets() {
    if (photoTags == null || photoTags!.isEmpty) {
      return [];
    }

    final List<Widget> photoWidgets = [];

    for (int i = 0; i < photoTags!.length; i++) {
      final photoTag = photoTags![i];
      final photoTagId = photoTag.photoTagId?.toInt() ?? 0;

      photoWidgets.add(
        PhotoUploadWidget(
          selectedImages: _getPhotosForTag(photoTagId),
          errorText: photoErrorText,
          photoTag: photoTag,
          // Use direct image capture if parameters are available
          onCameraTap: (taskId != null && questionId != null)
              ? null
              : () {
                  if (onCameraTap != null) {
                    onCameraTap!(null);
                  }
                },
          onImagesTap: (photoTagId) {
            if (onCameraTap != null) {
              onCameraTap!(photoTagId);
            }
          },
          // Parameters for direct image capture
          taskId: taskId,
          formId: formId,
          questionId: questionId,
          questionPartId: questionPartId,
          measurementId: measurement.measurementId?.toString(),
          level: 3, // Level 3 for photo_tags_three
          onPhotoAdded: onPhotoAdded,
        ),
      );

      // Add gap between photo upload widgets if there are multiple
      if (i < photoTags!.length - 1) {
        photoWidgets.add(const Gap(16));
      }
    }

    return photoWidgets;
  }
}
