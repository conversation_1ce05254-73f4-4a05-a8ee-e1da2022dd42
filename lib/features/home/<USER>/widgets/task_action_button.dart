import 'package:flutter/material.dart';

import '../../../../config/themes/app_colors.dart';

class TaskActionButton extends StatelessWidget {
  final dynamic icon;
  final VoidCallback? onPressed;
  final bool isPrimary;
  final String actionType;

  const TaskActionButton({
    super.key,
    required this.icon,
    required this.onPressed,
    required this.actionType,
    this.isPrimary = false,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
        color: Colors.transparent,
        child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(10),
            child: Container(
              decoration: BoxDecoration(
                color: (Colors.transparent),
                borderRadius: BorderRadius.circular(5),
                border: Border.all(
                  color: AppColors.blackTint2,
                  width: 1,
                ),
                // boxShadow: [
                //   if (isPrimary && !isDisabled)
                //     BoxShadow(
                //       color: AppColors.primaryBlue.withValues(alpha:0.3),
                //       spreadRadius: 1,
                //       blurRadius: 3,
                //       offset: const Offset(0, 1),
                //     ),
                //   if (!isPrimary && !isDisabled)
                //     BoxShadow(
                //       color: Colors.black.withValues(alpha:0.05),
                //       spreadRadius: 1,
                //       blurRadius: 2,
                //       offset: const Offset(0, 1),
                //     ),
                // ],
              ),
              width: 32,
              height: 32,
              child: Center(
                child: icon is IconData
                    ? Icon(
                        icon,
                        size: 16,
                        color: Colors.black,
                      )
                    : Image.asset(
                        icon,
                        color: Colors.black,
                        scale: 5,
                      ),
              ),
            )));
  }
}
