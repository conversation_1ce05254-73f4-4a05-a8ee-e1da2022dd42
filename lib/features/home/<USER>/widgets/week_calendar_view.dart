import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:gap/gap.dart';

class WeekCalendarView extends StatefulWidget {
  final List<schedule.TaskDetail> allApiTasks;
  final bool isCheckboxMode;
  final bool areAllItemsSelected;
  final Function(List<TaskDetail>, List<TaskDetail>) onSelectionChanged;
  final TaskDetail Function(schedule.TaskDetail) convertScheduleToDatum;
  final String Function(List<schedule.TaskDetail>) calculateTotalHours;
  final void Function(TaskDetail task)? onTaskTap;

  const WeekCalendarView({
    super.key,
    required this.allApiTasks,
    required this.isCheckboxMode,
    required this.areAllItemsSelected,
    required this.onSelectionChanged,
    required this.convertScheduleToDatum,
    required this.calculateTotalHours,
    this.onTaskTap,
  });

  @override
  State<WeekCalendarView> createState() => _WeekCalendarViewState();
}

class _WeekCalendarViewState extends State<WeekCalendarView> {
  DateTime selectedWeekStart = DateTime.now();
  DateTime? selectedDate;
  List<DateTime> weekDays = [];

  @override
  void initState() {
    super.initState();
    _generateCurrentWeek();
    selectedDate = DateTime.now();
  }

  void _generateCurrentWeek() {
    final DateTime now = selectedWeekStart;
    final int day = now.weekday; // 1=Monday, 7=Sunday
    // Convert to Sunday-first week: Sunday=0, Monday=1, etc.
    final int sundayFirstDay =
        day % 7; // Sunday becomes 0, Monday becomes 1, etc.
    final DateTime firstDayOfWeek =
        now.subtract(Duration(days: sundayFirstDay));

    weekDays = List.generate(7, (index) {
      return firstDayOfWeek.add(Duration(days: index));
    });
  }

  void _navigateWeek(bool isNext) {
    setState(() {
      selectedWeekStart =
          selectedWeekStart.add(Duration(days: isNext ? 7 : -7));
      _generateCurrentWeek();

      // Check if today is in the new week, prioritize selecting today
      final today = DateTime.now();
      final bool isTodayInWeek = weekDays.any((day) =>
          day.day == today.day &&
          day.month == today.month &&
          day.year == today.year);

      if (isTodayInWeek) {
        // If today is in this week, select today
        selectedDate = today;
      } else {
        // If current selected date is not in new week, select first day of new week
        final bool isSelectedDateInWeek = weekDays.any((day) =>
            selectedDate != null &&
            day.day == selectedDate!.day &&
            day.month == selectedDate!.month &&
            day.year == selectedDate!.year);

        if (!isSelectedDateInWeek) {
          selectedDate = weekDays.first;
        }
      }
    });
  }

  Map<DateTime, int> _getTaskCountsForWeek() {
    Map<DateTime, int> taskCounts = {};

    for (var weekDay in weekDays) {
      final normalizedDate = DateTime(weekDay.year, weekDay.month, weekDay.day);
      taskCounts[normalizedDate] = 0;
    }

    for (var task in widget.allApiTasks) {
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      for (var weekDay in weekDays) {
        final weekDayDate = DateTime(weekDay.year, weekDay.month, weekDay.day);
        final taskDate = DateTime(task.scheduledTimeStamp!.year,
            task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

        if (weekDayDate.isAtSameMomentAs(taskDate)) {
          taskCounts[weekDayDate] = (taskCounts[weekDayDate] ?? 0) + 1;
          break;
        }
      }
    }

    return taskCounts;
  }

  List<schedule.TaskDetail> _getTasksForSelectedDate() {
    if (selectedDate == null) return [];

    List<schedule.TaskDetail> tasksForDate = [];
    final selectedDateOnly =
        DateTime(selectedDate!.year, selectedDate!.month, selectedDate!.day);

    for (var task in widget.allApiTasks) {
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      final taskDate = DateTime(task.scheduledTimeStamp!.year,
          task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

      if (taskDate.isAtSameMomentAs(selectedDateOnly)) {
        tasksForDate.add(task);
      }
    }

    return tasksForDate;
  }

  @override
  Widget build(BuildContext context) {
    final taskCounts = _getTaskCountsForWeek();
    final tasksForSelectedDate = _getTasksForSelectedDate();
    final convertedTasks = tasksForSelectedDate
        .map((task) => widget.convertScheduleToDatum(task))
        .toList();

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Week header with navigation
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: () => _navigateWeek(false),
                  icon: const Icon(Icons.chevron_left),
                  style: IconButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                ),
                Text(
                  'Current',
                  style: Theme.of(context).textTheme.montserratTitleSmall,
                ),
                IconButton(
                  onPressed: () => _navigateWeek(true),
                  icon: const Icon(Icons.chevron_right),
                  style: IconButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Week days calendar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(0),
            ),
            child: Column(
              children: [
                // Days of week header
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    children: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa']
                        .map((day) => Expanded(
                              child: Center(
                                child: Text(
                                  day,
                                  style: Theme.of(context)
                                      .textTheme
                                      .montserratMetricsAxisRegular
                                      .copyWith(fontSize: 12),
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ),

                // Week days with tasks
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                  child: Row(
                    children: weekDays.map((day) {
                      final normalizedDate =
                          DateTime(day.year, day.month, day.day);
                      final taskCount = taskCounts[normalizedDate] ?? 0;
                      final isSelected = selectedDate != null &&
                          day.day == selectedDate!.day &&
                          day.month == selectedDate!.month &&
                          day.year == selectedDate!.year;
                      final isToday = day.day == DateTime.now().day &&
                          day.month == DateTime.now().month &&
                          day.year == DateTime.now().year;

                      return Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedDate = day;
                            });
                          },
                          child: Center(
                            child: Stack(
                              children: [
                                Container(
                                  width: 36,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: isSelected
                                        ? AppColors.black
                                        : isToday
                                            ? AppColors.primaryBlue
                                            : taskCount > 0
                                                ? Colors.grey.shade200
                                                : Colors.transparent,
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    day.day.toString(),
                                    style: Theme.of(context)
                                        .textTheme
                                        .montserratTableSmall
                                        .copyWith(
                                          color: isSelected || isToday
                                              ? Colors.white
                                              : taskCount > 0
                                                  ? Colors.black
                                                  : Colors.black,
                                          fontWeight: isSelected || isToday
                                              ? FontWeight.w600
                                              : FontWeight.normal,
                                        ),
                                  ),
                                ),
                                if (taskCount > 0)
                                  Positioned(
                                    bottom: 1,
                                    left: 0,
                                    right: 0,
                                    child: Container(
                                      padding: const EdgeInsets.all(1),
                                      child: Text(
                                        widget
                                            .calculateTotalHours(
                                              widget.allApiTasks.where((task) {
                                                if (task.scheduledTimeStamp ==
                                                    null) {
                                                  return false;
                                                }
                                                final taskDate = DateTime(
                                                  task.scheduledTimeStamp!.year,
                                                  task.scheduledTimeStamp!
                                                      .month,
                                                  task.scheduledTimeStamp!.day,
                                                );
                                                return taskDate
                                                    .isAtSameMomentAs(
                                                        normalizedDate);
                                              }).toList(),
                                            )
                                            .replaceAll(' hrs', ''),
                                        style: Theme.of(context)
                                            .textTheme
                                            .montserratTableSmall
                                            .copyWith(
                                              fontSize: 8,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.grey.shade600,
                                            ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),

          const Gap(16),

          // Tasks for selected date
          tasksForSelectedDate.isEmpty
              ? EmptyState(
                  message: selectedDate != null
                      ? 'No tasks for ${DateFormat('MMM d').format(selectedDate!)}'
                      : 'No tasks')
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Date header
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            selectedDate != null
                                ? DateFormat('EEE d MMM').format(selectedDate!)
                                : '',
                            style: Theme.of(context)
                                .textTheme
                                .montserratTitleSmall,
                          ),
                          Text(
                            widget.calculateTotalHours(tasksForSelectedDate),
                            style: Theme.of(context)
                                .textTheme
                                .montserratTitleExtraSmall
                                .copyWith(color: Colors.black),
                          ),
                        ],
                      ),
                    ),

                    // Task list
                    ReorderableStoreList(
                      tasks: convertedTasks,
                      isCalendarMode: widget.isCheckboxMode,
                      showScheduledDate: false,
                      showTickIndicator: true,
                      showAllDisclosureIndicator: false,
                      permanentlyDisableAllDisclosureIndicator: false,
                      isOpenTask: true,
                      onSelectionChanged: widget.onSelectionChanged,
                      selectAll: widget.areAllItemsSelected,
                      onTaskTap: widget.onTaskTap,
                      enableTimeValidation: true,
                    ),

                    // Add bottom padding when in checkbox mode
                    if (widget.isCheckboxMode) const SizedBox(height: 120),
                  ],
                ),
        ],
      ),
    );
  }
}
