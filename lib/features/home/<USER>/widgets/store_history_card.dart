import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/previous_tasks_response_entity.dart';

class StoreHistoryCard extends StatelessWidget {
  final PreviousTaskEntity task;
  final VoidCallback? onTap;

  const StoreHistoryCard({
    super.key,
    required this.task,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(10),
        splashColor: AppColors.primaryBlue.withValues(alpha: 0.1),
        highlightColor: AppColors.lightGrey2.withValues(alpha: 0.3),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(18),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Cycle Name and Date
                    Row(
                      children: [
                        Text(
                          task.dateSchedule != null
                              ? dateFormat.format(task.dateSchedule!)
                              : 'N/A',
                          style: Theme.of(context)
                              .textTheme
                              .montserratTableSmall
                              .copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            task.cycleName ?? 'Unknown Cycle',
                            style: Theme.of(context)
                                .textTheme
                                .montserratTableSmall
                                .copyWith(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),

                    // Completed By and Comment
                    Column(
                      children: [
                        Row(
                          children: [
                            SizedBox(
                              width: 100,
                              child: Text(
                                'Completed By:',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratSemiBold
                                    .copyWith(
                                      fontSize: 13,
                                      color: Colors.black,
                                    ),
                              ),
                            ),
                            Expanded(
                              child: Text(
                                task.completedBy ?? 'N/A',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratTableSmall,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 3),
                        Row(
                          children: [
                            SizedBox(
                              width: 100,
                              child: Text(
                                'Comment:',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratSemiBold
                                    .copyWith(
                                      fontSize: 13,
                                      color: Colors.black,
                                    ),
                              ),
                            ),
                            Expanded(
                              child: Text(
                                task.taskComment ?? 'No comment',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratTableSmall,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Subtle divider
                    Container(
                      height: 1,
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.grey.withValues(alpha: 0.1),
                            Colors.grey.withValues(alpha: 0.3),
                            Colors.grey.withValues(alpha: 0.1),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),

                    // Task Details
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              SizedBox(
                                width: 60,
                                child: Text(
                                  'Task ID:',
                                  style: Theme.of(context)
                                      .textTheme
                                      .montserratSemiBold
                                      .copyWith(fontSize: 13),
                                ),
                              ),
                              Text(
                                '${task.taskId ?? 'N/A'}',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratTableSmall,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Row(
                            children: [
                              SizedBox(
                                width: 60,
                                child: Text(
                                  'Status:',
                                  style: Theme.of(context)
                                      .textTheme
                                      .montserratSemiBold
                                      .copyWith(fontSize: 13),
                                ),
                              ),
                              Text(
                                'Completed',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratTableSmall
                                    .copyWith(
                                      color: Colors.green,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 10),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.blackTint1,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
