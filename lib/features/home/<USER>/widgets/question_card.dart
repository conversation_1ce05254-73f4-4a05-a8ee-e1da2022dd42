import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/core/services/photo_service.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart';
import 'package:storetrack_app/shared/widgets/simple_image_viewer.dart';

class QuestionCard extends StatefulWidget {
  final entities.Question question;
  final double progress;
  final String progressText;
  final bool isMandatory;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final bool hasPhotoUrl;
  final num? taskId;
  final num? formId;
  final VoidCallback? onQuestionTap;
  final Function(int?)? onPhotoSectionTap;
  final VoidCallback? onPhotoAdded;
  final int? photoRefreshCounter;
  final Map<int, String?>? photoErrorsByTag;

  const QuestionCard({
    super.key,
    required this.question,
    required this.progress,
    required this.progressText,
    required this.isMandatory,
    required this.showCameraIcon,
    required this.isCameraMandatory,
    required this.hasPhotoUrl,
    this.taskId,
    this.formId,
    this.onQuestionTap,
    this.onPhotoSectionTap,
    this.onPhotoAdded,
    this.photoRefreshCounter,
    this.photoErrorsByTag,
  });

  @override
  State<QuestionCard> createState() => _QuestionCardState();
}

class _QuestionCardState extends State<QuestionCard> {
  // Photo service for loading and managing photos
  late final PhotoService _photoService;

  // State management for photos for this question, grouped by photo tag ID
  final Map<int, List<String>> _questionPhotosByTag = {};

  @override
  void initState() {
    super.initState();
    _photoService = sl<PhotoService>();
    _loadSavedPhotos();
  }

  @override
  void didUpdateWidget(QuestionCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Reload photos if the question, task, or photoRefreshCounter changed
    if (oldWidget.question.questionId != widget.question.questionId ||
        oldWidget.taskId != widget.taskId ||
        oldWidget.photoRefreshCounter != widget.photoRefreshCounter) {
      _loadSavedPhotos();
    }
  }

  /// Load saved photos for this question with level 2 (combinetypeId = '2')
  Future<void> _loadSavedPhotos() async {
    if (widget.taskId == null || widget.question.questionId == null) {
      logger('TaskId or QuestionId is null, cannot load saved photos');
      return;
    }

    try {
      // Clear existing photo data
      _questionPhotosByTag.clear();

      final taskId = widget.taskId!.toInt();
      final questionId = widget.question.questionId!.toInt();

      final savedPhotos = await _photoService.getPhotosFromTask(
        taskId: taskId,
        folderId: null,
      );

      // Debug: Log available photoTag IDs for this question
      if (widget.question.photoTagsTwo != null) {
        final photoTagIds = widget.question.photoTagsTwo!
            .map((tag) => tag.photoTagId?.toInt())
            .toList();
        logger('Question $questionId has photoTag IDs: $photoTagIds');
      }

      // Use comprehensive filtering logic similar to mpt_page.dart
      // Filter photos for this question and level 2 (photo_tags_two)
      final filteredPhotos = savedPhotos.where((photo) {
        final questionIdMatch = photo.questionId?.toInt() == questionId;
        final levelMatch =
            photo.combineTypeId?.toInt() == 2; // Level 2 for photo_tags_two

        // Check if the photo belongs to one of the photo tags for this question
        // Use photo.folderId to match with photoTag.photoTagId (like mpt_page.dart)
        bool photoTagMatch = false;
        if (widget.question.photoTagsTwo != null && photo.folderId != null) {
          photoTagMatch = widget.question.photoTagsTwo!
              .any((tag) => tag.photoTagId?.toInt() == photo.folderId?.toInt());

          // Debug: Log photo matching details
          if (questionIdMatch && levelMatch) {
            logger(
                'Photo ID ${photo.photoId}: folderId=${photo.folderId}, photoTagMatch=$photoTagMatch');
          }
        }

        return questionIdMatch && levelMatch && photoTagMatch;
      }).toList();

      if (filteredPhotos.isNotEmpty && mounted) {
        setState(() {
          // Group photos by photo tag ID using folderId (like mpt_page.dart)
          for (final photo in filteredPhotos) {
            // Check for both photoUrl (server photos) and localPath (local photos)
            final imageUrl = photo.photoUrl ?? photo.localPath;
            if (imageUrl != null && photo.folderId != null) {
              final photoTagId = photo.folderId!.toInt();
              if (!_questionPhotosByTag.containsKey(photoTagId)) {
                _questionPhotosByTag[photoTagId] = [];
              }
              _questionPhotosByTag[photoTagId]!.add(imageUrl);
            }
          }
        });
      }

      logger(
          'Loaded ${filteredPhotos.length} photos for Question ${widget.question.questionId}');
      logger('Question photos by tag: $_questionPhotosByTag');
    } catch (e) {
      logger('Error loading saved photos: $e');
    }
  }

  /// Public method to refresh photos - can be called from parent widget
  Future<void> refreshPhotos() async {
    await _loadSavedPhotos();
  }

  /// Get photos for a specific photo tag ID
  List<String> _getPhotosForTag(int photoTagId) {
    logger(
        'Count of photos for tag $photoTagId: ${_questionPhotosByTag[photoTagId]?.length}');
    return _questionPhotosByTag[photoTagId] ?? [];
  }

  /// Show image viewer with the question's photo URL
  void _showImageViewer() {
    final String? photoUrl = widget.question.photoUrl;

    if (photoUrl == null || photoUrl.isEmpty) {
      logger('No photo URL found for question ${widget.question.questionId}');
      return;
    }

    logger(
        'Opening image viewer for question ${widget.question.questionId} with photo: $photoUrl');

    SimpleImageViewer.show(
      context,
      images: [photoUrl],
      initialIndex: 0,
      heroTag: 'question_photo_icon_${widget.question.questionId}',
    );
  }

  @override
  Widget build(BuildContext context) {
    // Define text styles based on the image and typical app theming
    // Main Title Style (e.g., "Multipack Stickering Task")
    final titleTextStyle =
        Theme.of(context).textTheme.montserratTitleExtraSmall;

    // Progress Text Style (e.g., "0 of 5")
    final progressTextStyle = Theme.of(context).textTheme.montserratTableSmall;
    //  Theme.of(context).textTheme.bodyLarge?.copyWith(
    //   ---
    //           // bodyLarge is typically 16sp
    //           color: AppColors.blackTint1, // Use a grey color from AppColors
    //           fontWeight: FontWeight.w500,
    //           fontSize: 16, // As per image
    //         ) ??
    // TextStyle(
    //   // Fallback
    //   color: Colors.grey.shade600,
    //   fontWeight: FontWeight.w500,
    //   fontSize: 16,
    // );

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: widget.progress == 1
                ? AppColors.completedGreenDark
                : Colors.white,
            borderRadius: BorderRadius.circular(
                10.0), // Slightly more rounded corners for the main card
            boxShadow: [
              BoxShadow(
                color:
                    Colors.black.withValues(alpha: 0.05), // Light shadow color
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: InkWell(
            borderRadius:
                BorderRadius.circular(10.0), // Match container's border radius
            onTap: widget.onQuestionTap,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.progress == 1) const Gap(12),
                // 1. Title (e.g., "Multipack Stickering Task")
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.question.questionDescription ??
                            'Unnamed Question',
                        style: titleTextStyle,
                      ),
                    ),
                    if (widget.isMandatory) ...[
                      const Gap(4),
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.transparent,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.priority_high,
                          color: AppColors.loginRed,
                          size: 16,
                        ),
                      ),
                    ],
                    if (widget.hasPhotoUrl) ...[
                      const Gap(4),
                      InkWell(
                        onTap: _showImageViewer,
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.image,
                            color: Colors.white,
                            size: 14,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const Gap(12), // Spacing after title

                // 2. Progress bar and text row (e.g., "0 of 5")
                if (widget.progress != 1)
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 8, // Thicker progress bar
                          clipBehavior: Clip
                              .antiAlias, // Ensures rounded corners are respected by child
                          decoration: BoxDecoration(
                            color:
                                AppColors.lightGrey2, // Background of the bar
                            borderRadius: BorderRadius.circular(
                                4), // Rounded ends for the bar
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: widget.progress.clamp(0.0, 1.0),
                            child: Container(
                              decoration: BoxDecoration(
                                color: AppColors.primaryBlue, // Progress color
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const Gap(16), // Space between bar and text
                      Text(
                        widget.progressText,
                        style: progressTextStyle,
                      ),
                    ],
                  ),

                // Conditionally add space only if the "Add photos" section will be shown
                if (widget.showCameraIcon) const Gap(20),

                // 3. "Add photos" section - show multiple PhotoUploadWidgets based on photoTagsTwo
                if (widget.showCameraIcon &&
                    widget.question.photoTagsTwo != null)
                  ..._buildPhotoUploadWidgets(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build PhotoUploadWidget for each photo tag
  List<Widget> _buildPhotoUploadWidgets() {
    if (widget.question.photoTagsTwo == null ||
        widget.question.photoTagsTwo!.isEmpty) {
      return [];
    }

    final List<Widget> photoWidgets = [];

    for (int i = 0; i < widget.question.photoTagsTwo!.length; i++) {
      final photoTag = widget.question.photoTagsTwo![i];
      final photoTagId = photoTag.photoTagId?.toInt() ?? 0;

      photoWidgets.add(
        PhotoUploadWidget(
          // Use direct image capture instead of navigation
          onCameraTap: null, // Let PhotoUploadWidget handle direct capture
          onImagesTap: (photoTagId) =>
              widget.onPhotoSectionTap?.call(photoTagId),
          selectedImages: _getPhotosForTag(photoTagId),
          photoTag: photoTag,
          isComplete: widget.progress == 1,
          errorText: widget.photoErrorsByTag?[photoTagId],
          // Parameters for direct image capture
          taskId: widget.taskId?.toString(),
          formId: widget.formId?.toString(),
          questionId: widget.question.questionId?.toString(),
          level: 2, // Level 2 for photo_tags_two
          onPhotoAdded: () {
            // Refresh photos when a new photo is added
            _loadSavedPhotos();
            // Notify parent that a photo was added
            if (widget.onPhotoAdded != null) {
              widget.onPhotoAdded!();
            }
          },
        ),
      );

      // Add gap between photo upload widgets if there are multiple
      if (i < widget.question.photoTagsTwo!.length - 1) {
        photoWidgets.add(const Gap(16));
      }
    }

    return photoWidgets;
  }
}
