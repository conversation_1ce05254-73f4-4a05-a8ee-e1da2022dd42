import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';

class ListToggleButton extends StatelessWidget {
  final bool isSelected;
  final VoidCallback onPressed;

  const ListToggleButton({
    super.key,
    required this.isSelected,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryBlue : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFFE0E0E0),
            width: 1,
          ),
        ),
        child: Icon(
          Icons.format_list_bulleted_outlined,
          color: isSelected ? Colors.white : Colors.black,
          size: 20,
        ),
      ),
    );
  }
}
