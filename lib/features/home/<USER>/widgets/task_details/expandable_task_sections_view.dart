import 'package:flutter/material.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/expandable_brief_section.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/expandable_documents_section.dart';

class ExpandableTaskSectionsView extends StatelessWidget {
  final entities.TaskDetail task;
  final bool documentsInitiallyExpanded;

  const ExpandableTaskSectionsView({
    super.key,
    required this.task,
    this.documentsInitiallyExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Brief Section
          ExpandableBriefSection(task: task),

          // Documents Section
          ExpandableDocumentsSection(
            task: task,
            initialExpanded: documentsInitiallyExpanded,
          ),
        ],
      ),
    );
  }
}
