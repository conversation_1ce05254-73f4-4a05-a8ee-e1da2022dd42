import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/documents_content_widget.dart';

class ExpandableDocumentsSection extends StatefulWidget {
  final entities.TaskDetail task;
  final bool? initialExpanded;

  const ExpandableDocumentsSection({
    super.key,
    required this.task,
    this.initialExpanded,
  });

  @override
  State<ExpandableDocumentsSection> createState() =>
      _ExpandableDocumentsSectionState();
}

class _ExpandableDocumentsSectionState
    extends State<ExpandableDocumentsSection> {
  late bool isDocumentsExpanded;

  @override
  void initState() {
    super.initState();
    isDocumentsExpanded = widget.initialExpanded ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(
            color: AppColors.black10,
            height: 1,
            thickness: 1,
          ),

          // Documents Section Header
          Padding(
            padding:
                const EdgeInsets.only(bottom: 20, left: 16, right: 16, top: 20),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isDocumentsExpanded = !isDocumentsExpanded;
                });
              },
              child: Container(
                color: Colors.transparent,
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 0.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Documents', style: textTheme.montserratTitleSmall),
                    Icon(
                      isDocumentsExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_right,
                      color: AppColors.black,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Documents Section Content
          if (isDocumentsExpanded) ...[
            DocumentsContentWidget(task: widget.task),
          ],
        ],
      ),
    );
  }
}
