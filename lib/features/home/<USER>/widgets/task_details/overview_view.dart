import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/simple_notification_card.dart';

class OverviewView extends StatelessWidget {
  final entities.TaskDetail task;
  final VoidCallback? onDocumentsTap;

  const OverviewView({super.key, required this.task, this.onDocumentsTap});

  Map<String, List<entities.Taskalert>> _groupTaskAlertsByDate(
      entities.TaskDetail task) {
    final Map<String, List<entities.Taskalert>> groupedTaskAlerts = {};
    for (var alert in task.taskalerts ?? []) {
      final index = task.taskalerts!.indexOf(alert);
      final daysAgo = index % 3;
      final fakeDate = DateTime.now().subtract(Duration(days: daysAgo));
      final formattedDate = DateFormat('EEE dd MMM').format(fakeDate);

      if (!groupedTaskAlerts.containsKey(formattedDate)) {
        groupedTaskAlerts[formattedDate] = [];
      }
      groupedTaskAlerts[formattedDate]!.add(alert);
    }
    return groupedTaskAlerts;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final groupedTaskAlerts = _groupTaskAlertsByDate(task);

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Overview',
                style: textTheme.montserratTitleSmall,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              const Gap(4),
              GestureDetector(
                onTap: onDocumentsTap,
                child: Row(
                  children: [
                    Image.asset(
                      AppAssets.documentsIcon,
                      scale: 4,
                      color: Colors.black,
                    ),
                    const Gap(6),
                    Text(
                      'Documents',
                      style: textTheme.montserratTitleExtraSmall,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (task.taskalerts != null &&
                      task.taskalerts!.isNotEmpty) ...[
                    Builder(builder: (context) {
                      final latestEntry = groupedTaskAlerts.entries.first;
                      final latestAlert = latestEntry.value.first;
                      return SimpleNotificationCard(
                        type: SimpleNotificationType.urgent,
                        message: latestAlert.message ?? 'No message',
                        enableHtml: true,
                      );
                    }),
                  ] else ...[
                    const SimpleNotificationCard(
                      type: SimpleNotificationType.urgent,
                      message: 'No alerts available',
                    )
                  ],
                  const Gap(16),
                  SimpleNotificationCard(
                    type: SimpleNotificationType.manager,
                    message: task.taskNote?.isNotEmpty == true
                        ? task.taskNote!
                        : 'No notes available',
                    enableHtml: true,
                  ),
                  const Gap(16),
                  const SimpleNotificationCard(
                    type: SimpleNotificationType.info,
                    title: 'Brief summary',
                    message: 'The summary of the brief will be shown here',
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
