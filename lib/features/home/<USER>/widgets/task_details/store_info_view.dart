import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/date_converter.dart';
import 'package:storetrack_app/shared/widgets/offline_image_widget.dart';

class StoreInfoView extends StatelessWidget {
  final entities.TaskDetail task;

  const StoreInfoView({super.key, required this.task});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(0),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.location_on_outlined,
                          size: 16,
                          color: AppColors.black,
                        ),
                        const Gap(4),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${task.storeName ?? ""}, ${task.location ?? ""}',
                                style: textTheme.montserratTableSmall.copyWith(
                                  overflow: TextOverflow.ellipsis,
                                ),
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const Gap(6),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          task.client ?? "",
                          style: textTheme.titleSmall?.copyWith(
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 2,
                        ),
                        const Gap(4),
                        Text(
                          task.cycle ?? "",
                          style: textTheme.montserratTableSmall.copyWith(
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 2,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              task.clientLogoUrl?.isEmpty == true
                  ? const SizedBox()
                  : const Gap(8),
              task.clientLogoUrl?.isEmpty == true
                  ? Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: const Center(child: Text('N/A')),
                    )
                  : Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(32),
                        child: OfflineImageWidget(
                          url: task.clientLogoUrl ?? '',
                          width: 60,
                          height: 60,
                          fit: BoxFit.contain,
                          borderRadius: 0, // Already handled by ClipRRect
                        ),
                      ),
                    ),
            ],
          ),
          const Gap(2),
          const Divider(color: AppColors.blackTint2),
          const Gap(4),
          Row(
            children: [
              const Icon(
                Icons.access_time,
                size: 16,
                color: AppColors.black,
              ),
              const Gap(4),
              Text(
                task.budget != null ? '${task.budget}m' : '',
                style: textTheme.montserratTableSmall
                    .copyWith(fontWeight: FontWeight.w500, fontSize: 12),
              ),
              const Gap(16),
              Image.asset(
                AppAssets.appbarCalendar,
                scale: 6,
                color: Colors.black,
              ),
              const Gap(4),
              Text(
                task.scheduledTimeStamp != null
                    ? convertToDateFormat(task.scheduledTimeStamp.toString())
                    : '',
                style: textTheme.bodySmall,
              ),
              const Spacer(),
              Text(
                'ID: ${task.taskId?.toString() ?? ''}',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.black,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
