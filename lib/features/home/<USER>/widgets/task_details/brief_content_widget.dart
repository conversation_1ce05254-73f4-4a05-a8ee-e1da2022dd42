import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/html_text_widget.dart';

class BriefContentWidget extends StatefulWidget {
  final entities.TaskDetail task;

  const BriefContentWidget({
    super.key,
    required this.task,
  });

  @override
  State<BriefContentWidget> createState() => _BriefContentWidgetState();
}

class _BriefContentWidgetState extends State<BriefContentWidget> {
  bool isOverviewExpanded = false;
  bool isBriefContentExpanded = false;

  @override
  Widget build(BuildContext context) {
    return _buildBriefContent(context);
  }

  Widget _buildBriefContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.task.forms != null && widget.task.forms!.isNotEmpty)
          ...widget.task.forms!.asMap().entries.map((entry) {
            final index = entry.key;
            final form = entry.value;
            return _buildPriorityFormItem(
              context,
              index + 1,
              form,
              widget.task,
            );
          })
        else
          const EmptyState(message: 'No brief available'),
      ],
    );
  }

  Widget _buildPriorityFormItem(
    BuildContext context,
    int priorityNumber,
    entities.Form form,
    entities.TaskDetail task,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final visibleQuestions = form.questions
        ?.where((q) => q.questionBrief?.isNotEmpty == true)
        .toList();

    // This check is important to avoid errors if visibleQuestions is null
    final hasVisibleQuestions =
        visibleQuestions != null && visibleQuestions.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.black20),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  priorityNumber.toString(),
                  style: textTheme.bodySmall?.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            const Gap(12),
            // Use Expanded to prevent form names from overflowing if they are too long
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    form.formName ?? 'Unnamed Form',
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        // This is the corrected Row
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // The vertical line column
            Padding(
              padding: const EdgeInsets.only(
                  left: 11.0), // Centered with the 24px circle
              child: Container(
                width: 2,
                // Only show the line if there are questions
                height: hasVisibleQuestions ? 60 : 0,
                color: AppColors.black10,
              ),
            ),
            const Gap(23), // Adjusted gap
            // Use Expanded to contain the questions column
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (hasVisibleQuestions)
                    ...visibleQuestions.asMap().entries.map((entry) {
                      final question = entry.value;
                      final isLastQuestion =
                          entry.key == visibleQuestions.length - 1;
                      return _buildPriorityQuestionItem(
                        context,
                        form,
                        question,
                        isLastQuestion,
                      );
                    })
                  else
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                      child: Text(
                        'N/A',
                        style: textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriorityQuestionItem(BuildContext context, entities.Form form,
      entities.Question question, bool isLastQuestion) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isLastQuestion
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (question.questionBrief?.isNotEmpty == true)
            Padding(
              padding: const EdgeInsets.all(2.0),
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 100,
                child: HtmlTextWidget(
                  text: question.questionBrief ?? 'No question brief',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.black,
                    fontSize: 24,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

isBriefAvailable(TaskDetail task) {
  for (final form in task.forms ?? []) {
    final visibleQuestions = form.questions
        ?.where((q) => q.questionBrief?.isNotEmpty == true)
        .toList();
    if (visibleQuestions != null && visibleQuestions.isNotEmpty) {
      return true;
    }
  }
  return false;
}
