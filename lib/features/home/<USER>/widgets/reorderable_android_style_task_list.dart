import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:intl/intl.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/shared/widgets/offline_image_widget.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/accept_task_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/accept_task_request_entity.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:auto_route/auto_route.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';

class ReorderableAndroidStyleTaskList extends StatefulWidget {
  final List<TaskDetail> tasks;
  final bool isCheckboxMode;
  final Function(List<TaskDetail> allItems, List<TaskDetail> selectedItems)?
      onSelectionChanged;
  final bool selectAll;
  final void Function(TaskDetail task)? onTaskTap;
  final bool enableTimeValidation;
  final bool disableTaskNavigation;
  final Function(int oldIndex, int newIndex)? onReorder;

  const ReorderableAndroidStyleTaskList({
    super.key,
    required this.tasks,
    this.isCheckboxMode = false,
    this.onSelectionChanged,
    this.selectAll = false,
    this.onTaskTap,
    this.enableTimeValidation = false,
    this.disableTaskNavigation = false,
    this.onReorder,
  });

  @override
  State<ReorderableAndroidStyleTaskList> createState() =>
      _ReorderableAndroidStyleTaskListState();
}

class _ReorderableAndroidStyleTaskListState
    extends State<ReorderableAndroidStyleTaskList> {
  late List<TaskDetail> _tasks;
  final Map<String, bool> _expandedStates = {};
  final Map<String, bool> _selectedStates = {};
  final List<TaskDetail> _selectedItems = [];

  // API related fields
  late final AcceptTaskUseCase _acceptTaskUseCase;
  late final DataManager _dataManager;
  String _userId = '';
  String _authToken = '';

  @override
  void initState() {
    super.initState();
    _tasks = List<TaskDetail>.from(widget.tasks);
    _initializeStates();
    _initializeApiDependencies();
  }

  void _initializeApiDependencies() {
    _acceptTaskUseCase = sl<AcceptTaskUseCase>();
    _dataManager = sl<DataManager>();
    _loadUserCredentials();
  }

  Future<void> _loadUserCredentials() async {
    try {
      _userId = await _dataManager.getUserId() ?? '';
      _authToken = await _dataManager.getAuthToken() ?? '';
    } catch (e) {
      // Handle credential loading error
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to load user credentials: ${e.toString()}',
        );
      }
    }
  }

  void _initializeStates() {
    for (var task in _tasks) {
      final taskId = task.taskId.toString();
      _expandedStates[taskId] = false;
      _selectedStates[taskId] = false;
    }
  }

  @override
  void didUpdateWidget(ReorderableAndroidStyleTaskList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tasks != widget.tasks) {
      _tasks = List<TaskDetail>.from(widget.tasks);
      _initializeStates();
    }
    if (oldWidget.selectAll != widget.selectAll) {
      _handleSelectAll(widget.selectAll);
    }
  }

  void _handleSelectAll(bool selectAll) {
    setState(() {
      for (var task in _tasks) {
        final taskId = task.taskId.toString();
        _selectedStates[taskId] = selectAll;
      }
      _updateSelectedItems();
    });
  }

  void _toggleExpanded(String taskId) {
    setState(() {
      _expandedStates[taskId] = !(_expandedStates[taskId] ?? false);
    });
  }

  void _toggleSelected(String taskId) {
    setState(() {
      _selectedStates[taskId] = !(_selectedStates[taskId] ?? false);
      _updateSelectedItems();
    });
  }

  void _updateSelectedItems() {
    _selectedItems.clear();
    for (var task in _tasks) {
      final taskId = task.taskId.toString();
      if (_selectedStates[taskId] == true) {
        _selectedItems.add(task);
      }
    }
    widget.onSelectionChanged?.call(_tasks, _selectedItems);
  }

  void _handleReorder(int oldIndex, int newIndex) {
    setState(() {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final TaskDetail task = _tasks.removeAt(oldIndex);
      _tasks.insert(newIndex, task);
    });

    // Provide haptic feedback for successful reorder
    HapticFeedback.mediumImpact();

    // Call the optional onReorder callback
    widget.onReorder?.call(oldIndex, newIndex);
  }

  // Handle Full Brief button tap
  Future<void> _handleFullBriefTap(TaskDetail task) async {
    try {
      // Navigate to the new Full Brief page
      if (mounted) {
        context.router.push(FullBriefRoute(
          taskId: task.taskId.toString(),
        ));
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to open brief: ${e.toString()}',
        );
      }
    }
  }

  // Handle Accept Task button tap
  Future<void> _handleAcceptTaskTap(TaskDetail task) async {
    try {
      // Validate credentials
      if (_userId.isEmpty || _authToken.isEmpty) {
        await _loadUserCredentials();
        if (_userId.isEmpty || _authToken.isEmpty) {
          if (mounted) {
            SnackBarService.error(
              context: context,
              message: 'User credentials not available. Please login again.',
            );
          }
          return;
        }
      }

      // Create accept task request
      final request = AcceptTaskRequestEntity(
        token: _authToken,
        userId: _userId,
        taskId: task.taskId.toString(),
        dateScheduled: DateTime.now()
            .toIso8601String()
            .split('T')[0], // Today's date in YYYY-MM-DD format
      );

      // Call the API
      final result = await _acceptTaskUseCase(request);

      if (result.isSuccess && result.data != null) {
        if (mounted) {
          SnackBarService.success(
            context: context,
            message: result.data!.message.isNotEmpty
                ? result.data!.message
                : 'Task accepted successfully!',
          );
        }

        // Call the original onTaskTap callback if provided
        widget.onTaskTap?.call(task);

        // Remove the task from the list since it's now accepted
        if (mounted) {
          setState(() {
            _tasks.remove(task);
            _initializeStates(); // Reinitialize states for remaining tasks
          });
        }
      } else {
        final errorMessage =
            result.error?.toString() ?? 'Failed to accept task.';
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: errorMessage,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to accept task: ${e.toString()}',
        );
      }
    }
  }

  // Calculate distance (placeholder - you can implement actual distance calculation)
  String _calculateDistance(TaskDetail task) {
    // This is a placeholder - implement actual distance calculation based on user location
    return "2.5 KM";
  }

  // Generate color from store name
  Color _getColorFromName(String name) {
    if (name.isEmpty) return Colors.blueAccent.shade700;
    int sum = 0;
    for (int i = 0; i < name.length; i++) {
      sum += name.codeUnitAt(i);
    }
    final hue = (sum % 360).toDouble();
    return HSLColor.fromAHSL(1.0, hue, 0.6, 0.5).toColor();
  }

  @override
  Widget build(BuildContext context) {
    if (_tasks.isEmpty) {
      return const Center(
        child: Text('No tasks available'),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return ReorderableListView.builder(
          shrinkWrap: true,
          physics: const ClampingScrollPhysics(),
          buildDefaultDragHandles: false,
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          onReorderStart: (index) {
            HapticFeedback.mediumImpact();
          },
          onReorder: _handleReorder,
          proxyDecorator: (child, index, animation) {
            // Add a spring animation to the dragged item
            final curvedAnimation = CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
              reverseCurve: Curves.easeInOut,
            );

            return AnimatedBuilder(
              animation: curvedAnimation,
              builder: (context, child) {
                final scale =
                    1.0 + (curvedAnimation.value * 0.02); // Subtle scale effect

                return Material(
                  elevation: 4 * curvedAnimation.value,
                  color: Colors.transparent,
                  shadowColor: Colors.black.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(10),
                  child: Transform.scale(
                    scale: scale,
                    child: child,
                  ),
                );
              },
              child: child,
            );
          },
          itemCount: _tasks.length,
          itemBuilder: (context, index) {
            final task = _tasks[index];
            final taskId = task.taskId.toString();
            final isExpanded = _expandedStates[taskId] ?? false;
            final isSelected = _selectedStates[taskId] ?? false;

            return ReorderableDragStartListener(
              key: ValueKey(taskId),
              index: index,
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Task Header (Collapsible)
                    _buildTaskHeader(
                        task, taskId, isExpanded, isSelected, index),

                    // Task Details (Expandable Content)
                    if (isExpanded) _buildTaskDetails(task),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTaskHeader(TaskDetail task, String taskId, bool isExpanded,
      bool isSelected, int index) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          // Drag handle
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
            child: Icon(
              Icons.drag_handle,
              color: AppColors.blackTint1.withValues(alpha: 0.6),
              size: 20,
            ),
          ),

          const SizedBox(width: 8),

          // Checkbox/Tick Indicator
          if (widget.isCheckboxMode)
            GestureDetector(
              onTap: () => _toggleSelected(taskId),
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color:
                      isSelected ? AppColors.primaryBlue : Colors.transparent,
                  border: Border.all(
                    color: isSelected
                        ? AppColors.primaryBlue
                        : AppColors.blackTint1,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        size: 16,
                        color: Colors.white,
                      )
                    : null,
              ),
            ),

          if (widget.isCheckboxMode) const SizedBox(width: 8),

          // Store Avatar
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.blackTint1.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: CircleAvatar(
              radius: 20,
              backgroundColor:
                  task.groupUrl != null && task.groupUrl!.isNotEmpty
                      ? Colors.transparent
                      : _getColorFromName(task.storeName ?? ''),
              child: task.groupUrl != null && task.groupUrl!.isNotEmpty
                  ? ClipOval(
                      child: OfflineImageWidget(
                        url: task.groupUrl!,
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                        borderRadius: 0,
                      ),
                    )
                  : Text(
                      (task.storeName?.length ?? 0) >= 2
                          ? task.storeName!.substring(0, 2).toUpperCase()
                          : task.storeName ?? '',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
            ),
          ),

          const SizedBox(width: 8),

          // Store Information
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      flex: 8,
                      child: Text(
                        task.storeName ?? "Store Name",
                        style: textTheme.montserratTitleExtraSmall,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ),
                    const Spacer(),
                    const Icon(
                      Icons.access_time,
                      size: 16,
                      color: AppColors.blackTint1,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${task.budget ?? 0}m',
                      style: textTheme.montserratTitleExtraSmall.copyWith(
                        color: AppColors.blackTint1,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 4),

                // Location
                if (task.location != null)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.location_on_outlined,
                        size: 16,
                        color: AppColors.black,
                      ),
                      const SizedBox(width: 2),
                      Flexible(
                        child: Text(
                          task.location!,
                          style: textTheme.montserratTableSmall,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),

          const SizedBox(width: 8),

          // Expand/Collapse Indicator
          GestureDetector(
            onTap: () => _toggleExpanded(taskId),
            child: AnimatedRotation(
              turns: isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 200),
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 24,
                color: AppColors.blackTint1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskDetails(TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.only(
        left: 10,
        right: 10,
        bottom: 10,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Client Name
          Text(
            task.client ?? "Client Name",
            style: textTheme.montserratTitleExtraSmall.copyWith(
              color: AppColors.black,
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 8),

          // Cycle Information
          _buildDetailRow("Cycle:", task.cycle ?? "N/A"),

          // Date Range
          _buildDetailRow(
            "Range:",
            task.rangeStart != null && task.rangeEnd != null
                ? "${DateFormat('dd/MM/yyyy').format(task.rangeStart!)} - ${DateFormat('dd/MM/yyyy').format(task.rangeEnd!)}"
                : "N/A",
          ),

          // Budget
          _buildDetailRow("Budget:", "${task.budget ?? 0}m"),

          // Scheduled Date
          _buildDetailRow(
            "Scheduled:",
            task.scheduledTimeStamp != null &&
                    task.scheduledTimeStamp!.isAfter(AppConstants.minDateTime)
                ? DateFormat('dd/MM/yyyy').format(task.scheduledTimeStamp!)
                : "Not Scheduled",
            showLock: task.disallowReschedule == true,
          ),

          // Expires Date
          _buildDetailRow(
            "Expires:",
            task.expires != null
                ? DateFormat('dd/MM/yyyy').format(task.expires!)
                : "N/A",
          ),

          // Re-opened Status
          _buildDetailRow(
            "Re-opened:",
            task.reOpened == true ? (task.reOpenedReason ?? "Yes") : "No",
          ),

          // Description
          _buildDetailRow(
              "Description:", task.comment ?? task.taskNote ?? "N/A"),

          const SizedBox(height: 10),

          // Action Buttons
          Row(
            children: [
              // Full Brief Button
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _handleFullBriefTap(task),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    "Full Brief",
                    style: textTheme.montserratTableSmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // Accept Task Button
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _handleAcceptTaskTap(task),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.loginGreen,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    "Accept Task",
                    style: textTheme.montserratTableSmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // Reject Task Button (hidden for open tasks)
              if (task.taskStatus != "Tentative")
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // Handle reject task action
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.loginRed,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      "Reject",
                      style: textTheme.montserratTableSmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool showLock = false}) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: textTheme.montserratTableSmall.copyWith(
                color: AppColors.blackTint1,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (showLock) ...[
            const Icon(
              Icons.lock,
              size: 16,
              color: AppColors.blackTint1,
            ),
            const SizedBox(width: 4),
          ],
          Expanded(
            child: Text(
              value,
              style: textTheme.montserratTableSmall.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
