import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart'; // Assuming this is your custom theme extension
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/core/services/camera_service.dart';
import 'package:storetrack_app/core/services/photo_service.dart';
import 'package:storetrack_app/core/utils/image_storage_utils.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'dart:io';

/// Helper class for displaying photos in the widget
class PhotoDisplayItem {
  final String? imagePath;
  final bool isCannotUpload;
  final int? originalIndex;

  const PhotoDisplayItem({
    this.imagePath,
    required this.isCannotUpload,
    this.originalIndex,
  });
}

class PhotoUploadWidget extends StatefulWidget {
  /// A list of local image file paths to display as thumbnails.
  final List<String> selectedImages;

  /// A callback triggered when the header area (containing "Add photos") is tapped.
  /// Use this to initiate adding a new photo (e.g., by showing a modal sheet).
  final VoidCallback? onCameraTap;

  /// A callback triggered when the row of displayed images is tapped.
  /// Use this to navigate to a gallery or detail view.
  /// Receives the photoTagId as parameter.
  final Function(int?)? onImagesTap;

  /// Error message to display below the photo upload widget
  final String? errorText;

  /// PhotoTag object containing photo configuration settings
  final PhotoTagsT? photoTag;

  final bool? isComplete;

  /// Parameters for direct image capture functionality
  final String? taskId;
  final String? formId;
  final String? questionId;
  final String? questionPartId;
  final String? measurementId;
  final int? level;
  final VoidCallback? onPhotoAdded;

  const PhotoUploadWidget({
    super.key,
    this.selectedImages = const [],
    this.onCameraTap,
    this.onImagesTap,
    this.errorText,
    this.photoTag,
    this.isComplete,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.measurementId,
    this.level,
    this.onPhotoAdded,
  });

  @override
  State<PhotoUploadWidget> createState() => _PhotoUploadWidgetState();
}

class _PhotoUploadWidgetState extends State<PhotoUploadWidget> {
  late final CameraService _cameraService;
  late final PhotoService _photoService;

  /// Internal cache of photo objects by their file paths
  final Map<String, Photo> _photoObjectsByPath = {};

  @override
  void initState() {
    super.initState();
    _cameraService = sl<CameraService>();
    _photoService = sl<PhotoService>();
    _loadPhotoMetadata();
  }

  @override
  void didUpdateWidget(PhotoUploadWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Reload photo metadata if selectedImages changed
    if (oldWidget.selectedImages != widget.selectedImages ||
        oldWidget.taskId != widget.taskId) {
      _loadPhotoMetadata();
    }
  }

  /// Load photo metadata internally for cannot upload detection
  Future<void> _loadPhotoMetadata() async {
    logger(
        'Loading photo metadata - taskId: ${widget.taskId}, selectedImages: ${widget.selectedImages}');

    if (widget.taskId == null) {
      _photoObjectsByPath.clear();
      logger('Clearing photo metadata - no taskId');
      return;
    }

    try {
      final taskId = int.tryParse(widget.taskId!);
      if (taskId == null) {
        logger('Invalid taskId format: ${widget.taskId}');
        return;
      }

      final photos = await _photoService.getPhotosFromTask(taskId: taskId);
      logger('Loaded ${photos.length} photos from PhotoService');

      if (mounted) {
        setState(() {
          _photoObjectsByPath.clear();

          // Map photos by their image paths for quick lookup
          for (final photo in photos) {
            final imagePath = photo.photoUrl ?? photo.localPath;
            if (imagePath != null) {
              _photoObjectsByPath[imagePath] = photo;
              logger(
                  'Mapped photo: path=$imagePath, cannotUpload=${photo.cannotUploadMandatory}');
            } else if (photo.cannotUploadMandatory == true) {
              // For cannot upload photos without paths, use a unique key
              final uniqueKey = 'cannot_upload_${photo.photoId}';
              _photoObjectsByPath[uniqueKey] = photo;
              logger(
                  'Mapped cannot upload photo: key=$uniqueKey, photoId=${photo.photoId}');
            }
          }

          logger(
              'Photo metadata loaded: ${_photoObjectsByPath.length} photos mapped');
        });
      }
    } catch (e) {
      logger('Error loading photo metadata: $e');
    }
  }

  /// Get appropriate image provider based on whether the path is a URL or local file
  ImageProvider? _getImageProvider(String imagePath, {int? index}) {
    // Check if this is a cannot upload photo using internal cache
    if (index != null && _isCannotUploadPhoto(index)) {
      // Return null for cannot upload photos - will be handled specially in UI
      return null;
    }

    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      // Network image
      logger('Loading network image: $imagePath');
      return NetworkImage(imagePath);
    } else {
      // Local file
      logger('Loading local file: $imagePath');
      return FileImage(File(imagePath));
    }
  }

  /// Get all photo display items including cannot upload photos
  List<PhotoDisplayItem> _getAllPhotoDisplayItems() {
    final items = <PhotoDisplayItem>[];

    // Add regular photos from selectedImages
    for (int i = 0; i < widget.selectedImages.length; i++) {
      final imagePath = widget.selectedImages[i];
      final photo = _photoObjectsByPath[imagePath];

      items.add(PhotoDisplayItem(
        imagePath: imagePath,
        isCannotUpload: photo?.cannotUploadMandatory == true,
        originalIndex: i,
      ));
    }

    // Add cannot upload photos that don't have file paths, but only for this photo tag
    final photoTagId = widget.photoTag?.photoTagId?.toInt();
    final questionId =
        widget.questionId != null ? int.tryParse(widget.questionId!) : null;
    final measurementId = widget.measurementId != null
        ? int.tryParse(widget.measurementId!)
        : null;
    final questionpartId = widget.questionPartId != null
        ? int.tryParse(widget.questionPartId!)
        : null;

    for (final entry in _photoObjectsByPath.entries) {
      final key = entry.key;
      final photo = entry.value;

      logger('Checking photo: key=$key, cannotUpload=${photo.cannotUploadMandatory}');

      if (photo.cannotUploadMandatory == true &&
          key.startsWith('cannot_upload_')) {
        // Check if this cannot upload photo belongs to the current photo tag
        bool matches = true;

        if (photoTagId != null && photo.photoTagId != photoTagId) {
          matches = false;
        }

        if (questionId != null && photo.questionId != questionId) {
          matches = false;
        }

        if (measurementId != null && photo.measurementId != measurementId) {
          matches = false;
        }

        if (questionpartId != null && photo.questionpartId != questionpartId) {
          matches = false;
        }

        if (matches) {
          // This is a cannot upload photo for this specific photo tag
          items.add(const PhotoDisplayItem(
            imagePath: null,
            isCannotUpload: true,
            originalIndex: null,
          ));
          logger(
              'Added cannot upload photo display item for key: $key, photoTagId: $photoTagId');
        }
      }
    }

    logger('Generated ${items.length} photo display items');
    return items;
  }

  /// Check if photo at given index is a cannot upload photo
  bool _isCannotUploadPhoto(int index) {
    final displayItems = _getAllPhotoDisplayItems();
    if (index >= displayItems.length) {
      return false;
    }

    final item = displayItems[index];

    // Debug logging
    logger('Checking cannot upload for index $index, path: ${item.imagePath}');
    logger('IsCannotUpload: ${item.isCannotUpload}');

    return item.isCannotUpload;
  }

  /// Check if a cannot upload photo already exists for the current photo tag
  bool _hasExistingCannotUploadPhoto() {
    final photoTagId = widget.photoTag?.photoTagId?.toInt();
    final questionId =
        widget.questionId != null ? int.tryParse(widget.questionId!) : null;
    final measurementId = widget.measurementId != null
        ? int.tryParse(widget.measurementId!)
        : null;
    final questionpartId = widget.questionPartId != null
        ? int.tryParse(widget.questionPartId!)
        : null;

    // Search through cached photos for existing cannot upload photo
    for (final entry in _photoObjectsByPath.entries) {
      final photo = entry.value;

      // Check if this is a cannot upload photo
      if (photo.cannotUploadMandatory == true) {
        // Match by photoTagId, questionId, measurementId, and questionpartId
        bool matches = true;

        if (photoTagId != null && photo.photoTagId != photoTagId) {
          matches = false;
        }

        if (questionId != null && photo.questionId != questionId) {
          matches = false;
        }

        if (measurementId != null && photo.measurementId != measurementId) {
          matches = false;
        }

        if (questionpartId != null && photo.questionpartId != questionpartId) {
          matches = false;
        }

        if (matches) {
          logger(
              'Found existing cannot upload photo: photoId=${photo.photoId}');
          return true;
        }
      }
    }

    logger('No existing cannot upload photo found');
    return false;
  }

  /// Check if normal (non-cannot-upload) photos already exist for the current photo tag
  bool _hasExistingNormalPhotos() {
    final photoTagId = widget.photoTag?.photoTagId?.toInt();
    final questionId =
        widget.questionId != null ? int.tryParse(widget.questionId!) : null;
    final measurementId = widget.measurementId != null
        ? int.tryParse(widget.measurementId!)
        : null;
    final questionpartId = widget.questionPartId != null
        ? int.tryParse(widget.questionPartId!)
        : null;

    // Search through cached photos for existing normal photos
    for (final entry in _photoObjectsByPath.entries) {
      final photo = entry.value;

      // Check if this is a normal photo (not a cannot upload photo)
      if (photo.cannotUploadMandatory != true) {
        // Match by photoTagId, questionId, measurementId, and questionpartId
        bool matches = true;

        if (photoTagId != null && photo.photoTagId != photoTagId) {
          matches = false;
        }

        if (questionId != null && photo.questionId != questionId) {
          matches = false;
        }

        if (measurementId != null && photo.measurementId != measurementId) {
          matches = false;
        }

        if (questionpartId != null && photo.questionpartId != questionpartId) {
          matches = false;
        }

        if (matches) {
          logger('Found existing normal photo: photoId=${photo.photoId}');
          return true;
        }
      }
    }

    logger('No existing normal photos found');
    return false;
  }

  /// Remove existing cannot upload photos for the current photo tag
  Future<void> _removeExistingCannotUploadPhotos() async {
    if (widget.taskId == null || widget.questionId == null) {
      logger(
          'TaskId or QuestionId is null, cannot remove cannot upload photos');
      return;
    }

    final taskId = int.tryParse(widget.taskId!);
    if (taskId == null) {
      logger('Invalid taskId format');
      return;
    }

    final photoTagId = widget.photoTag?.photoTagId?.toInt();
    final questionId =
        widget.questionId != null ? int.tryParse(widget.questionId!) : null;
    final measurementId = widget.measurementId != null
        ? int.tryParse(widget.measurementId!)
        : null;
    final questionpartId = widget.questionPartId != null
        ? int.tryParse(widget.questionPartId!)
        : null;

    final photosToRemove = <String>[];

    // Find cannot upload photos that match current criteria
    for (final entry in _photoObjectsByPath.entries) {
      final key = entry.key;
      final photo = entry.value;

      if (photo.cannotUploadMandatory == true) {
        // Match by photoTagId, questionId, measurementId, and questionpartId
        bool matches = true;

        if (photoTagId != null && photo.photoTagId != photoTagId) {
          matches = false;
        }

        if (questionId != null && photo.questionId != questionId) {
          matches = false;
        }

        if (measurementId != null && photo.measurementId != measurementId) {
          matches = false;
        }

        if (questionpartId != null && photo.questionpartId != questionpartId) {
          matches = false;
        }

        if (matches && photo.photoId != null) {
          logger('Removing cannot upload photo: photoId=${photo.photoId}');

          // Delete from database
          final deleteSuccess = await _photoService.deletePhotoFromTask(
            photoId: photo.photoId!.toInt(),
            taskId: taskId,
            folderId: photoTagId,
          );

          if (deleteSuccess) {
            photosToRemove.add(key);
          }
        }
      }
    }

    // Remove from internal cache
    if (photosToRemove.isNotEmpty) {
      setState(() {
        for (final key in photosToRemove) {
          _photoObjectsByPath.remove(key);
        }
      });
      logger(
          'Removed ${photosToRemove.length} cannot upload photos from cache');
    }
  }

  /// Get camera icon color based on is_mandatory property
  Color _getCameraIconColor() {
    if (widget.photoTag?.isMandatory == true) {
      return AppColors.darkYellow15; // Yellow color for mandatory
    } else {
      return AppColors.loginGreen; // Green color for optional
    }
  }

  /// Determine if HI-RES text should be shown based on image_rec property
  bool _shouldShowHiResText() {
    return widget.photoTag?.imageRec == true;
  }

  /// Get image quality for compression based on PhotoResPerc
  /// Returns null if compression should be disabled (when image_rec is true)
  int? _getImageQuality() {
    if (widget.photoTag?.imageRec == true) {
      // Disable compression for high-resolution images
      return null;
    }

    // Use PhotoResPerc as compression percentage, default to 85 if not specified
    final photoResPerc = widget.photoTag?.photoResPerc?.toInt();
    return photoResPerc ?? 85;
  }

  /// Check if only live images (camera) are allowed
  bool _isLiveImagesOnly() {
    return widget.photoTag?.liveImagesOnly == true;
  }

  /// Handle direct image capture when onCameraPressed is null
  void _handleDirectImageCapture() async {
    // If onCameraPressed is provided, use it instead of direct capture
    if (widget.onCameraTap != null) {
      widget.onCameraTap!();
      return;
    }

    // Check if we have the required parameters for direct capture
    if (widget.taskId == null || widget.questionId == null) {
      logger('TaskId or QuestionId is null, cannot capture image directly');
      return;
    }

    _showImageSourceDialog();
  }

  /// Show image source selection dialog (camera or gallery)
  void _showImageSourceDialog() {
    final liveImagesOnly = _isLiveImagesOnly();

    // If only live images are allowed, directly open camera
    // if (liveImagesOnly) {
    //   _captureImage(ImageSource.camera);
    //   return;
    // }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      useRootNavigator: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.blackTint2,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Gap(16),
                Text(
                  'Add Photo',
                  style: Theme.of(context).textTheme.montserratTitleExtraSmall,
                ),
                const Gap(16),
                // Camera option
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.camera_alt,
                        color: Colors.blue, size: 20),
                  ),
                  title: Text('Take Photo',
                      style: Theme.of(context)
                          .textTheme
                          .montserratTitleExtraSmall),
                  subtitle: Text(
                    'Use camera to take a new photo',
                    style: Theme.of(context).textTheme.montserratTableSmall,
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _captureImage(ImageSource.camera);
                  },
                ),
                // Gallery option (only if live images only is false)
                if (!liveImagesOnly)
                  ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.photo_library,
                          color: Colors.green, size: 20),
                    ),
                    title: Text('Choose from Gallery',
                        style: Theme.of(context)
                            .textTheme
                            .montserratTitleExtraSmall),
                    subtitle: Text(
                      'Select multiple photos from gallery',
                      style: Theme.of(context).textTheme.montserratTableSmall,
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _captureMultipleImagesFromGallery();
                    },
                  ),
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.image_not_supported_rounded,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ),
                  title: Text('Cannot Upload',
                      style: Theme.of(context)
                          .textTheme
                          .montserratTitleExtraSmall),
                  subtitle: Text(
                    'I cannot upload this photo',
                    style: Theme.of(context).textTheme.montserratTableSmall,
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _handleCannotUpload();
                  },
                ),
                const Gap(16),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Handle cannot upload photo option
  Future<void> _handleCannotUpload() async {
    try {
      // Check if normal photos already exist - if so, cannot add cannot upload
      if (_hasExistingNormalPhotos()) {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Photos already exist',
          );
        }
        return;
      }

      // Check if a cannot upload photo already exists
      if (_hasExistingCannotUploadPhoto()) {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'You have already marked this as cannot upload',
          );
        }
        return;
      }

      await _saveCannotUploadPhoto();
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to save cannot upload photo: ${e.toString()}',
        );
      }
    }
  }

  /// Save cannot upload photo to database
  Future<void> _saveCannotUploadPhoto() async {
    try {
      if (widget.taskId == null || widget.questionId == null) {
        logger('TaskId or QuestionId is null, cannot save cannot upload photo');
        return;
      }

      final taskId = int.tryParse(widget.taskId!);
      final questionId = int.tryParse(widget.questionId!);
      final measurementId = widget.measurementId != null
          ? int.tryParse(widget.measurementId!)
          : null;
      final questionpartId = widget.questionPartId != null
          ? int.tryParse(widget.questionPartId!)
          : null;

      if (taskId == null || questionId == null) {
        logger('Invalid taskId or questionId format');
        return;
      }

      final photoId = await _photoService.getNextPhotoId(taskId: taskId);

      final photo = Photo(
        photoId: photoId,
        formId: widget.formId != null ? int.tryParse(widget.formId!) : null,
        questionId: questionId,
        measurementId: measurementId,
        measurementPhototypeId:
            widget.photoTag?.measurementPhototypeId?.toInt(),
        questionpartId: questionpartId,
        combineTypeId: widget.level,
        caption: '',
        localPath: null, // No actual image file for cannot upload
        modifiedTimeStampPhoto: DateTime.now(),
        userDeletedPhoto: false,
        cannotUploadMandatory: true, // Set to true for cannot upload
        imageRec: false,
        photoTagId: widget.photoTag?.photoTagId?.toInt(),
        photoCombinetypeId: widget.level,
        isEdited: false,
      );

      final success = await _photoService.savePhotoToTask(
        photo: photo,
        taskId: taskId,
        folderId: widget.photoTag?.photoTagId?.toInt(),
      );

      if (success) {
        // if (mounted) {
        //   SnackBarService.success(
        //     context: context,
        //     message: 'Cannot upload photo saved successfully!',
        //   );
        // }
        // Notify parent widget that a photo was added
        if (widget.onPhotoAdded != null) {
          widget.onPhotoAdded!();
        }
      } else {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Failed to save cannot upload photo to database.',
          );
        }
      }
    } catch (e) {
      logger('Error saving cannot upload photo to database: $e');
      rethrow;
    }
  }

  /// Capture multiple images from gallery
  Future<void> _captureMultipleImagesFromGallery() async {
    try {
      // Determine image quality based on photoTag settings
      int? imageQuality = _getImageQuality();

      final List<File> imageFiles =
          await _cameraService.pickMultipleImagesFromGallery(
        imageQuality: imageQuality,
      );

      if (imageFiles.isEmpty) return;

      await _saveMultipleImagesWithCaptions(imageFiles, '');
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to select photos: ${e.toString()}',
        );
      }
    }
  }

  /// Capture image from camera or gallery
  Future<void> _captureImage(ImageSource source) async {
    try {
      // Determine image quality based on photoTag settings
      int? imageQuality = _getImageQuality();

      // Check if only live images (camera) are allowed
      final liveImagesOnly = _isLiveImagesOnly();

      final File? imageFile = await _cameraService.pickImage(
        source: source,
        imageQuality: imageQuality,
        liveImagesOnly: liveImagesOnly,
      );
      if (imageFile == null) return;
      await _saveImageWithCaption(imageFile, '');
      // Show fullscreen image viewer
      // if (mounted) {
      //   showDialog(
      //     context: context,
      //     barrierColor: Colors.black,
      //     builder: (context) => FullscreenImageViewer(
      //       imagePath: imageFile.path,
      //       initialCaption: 'Photo',
      //       onRetake: () {
      //         // Retake photo - trigger the picker again
      //         _captureImage(source);
      //       },
      //       onUsePhoto: (caption) async {
      //         // Save the photo with the entered caption
      //         await _saveImageWithCaption(imageFile, caption);
      //       },
      //     ),
      //   );
      // }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to capture photo: ${e.toString()}',
        );
      }
    }
  }

  /// Save multiple images with captions to database
  Future<void> _saveMultipleImagesWithCaptions(
      List<File> imageFiles, String caption) async {
    if (imageFiles.isEmpty) return;

    try {
      // Show initial progress
      if (mounted) {
        SnackBarService.info(
          context: context,
          message: 'Saving ${imageFiles.length} photos...',
        );
      }

      // Remove any existing cannot upload photos before saving batch
      await _removeExistingCannotUploadPhotos();

      int savedCount = 0;
      int failedCount = 0;

      for (int i = 0; i < imageFiles.length; i++) {
        final imageFile = imageFiles[i];

        try {
          final String? savedImagePath =
              await ImageStorageUtils.saveImageToAppStorage(imageFile);
          if (savedImagePath == null) {
            failedCount++;
            continue;
          }

          final Photo? photo =
              await _savePhotoToDatabase(savedImagePath, caption);
          if (photo != null) {
            savedCount++;

            // Show progress every 2 photos or on last photo
            if (mounted && (i % 2 == 1 || i == imageFiles.length - 1)) {
              SnackBarService.info(
                context: context,
                message: 'Saved $savedCount of ${imageFiles.length} photos...',
              );
            }
          } else {
            failedCount++;
          }
        } catch (e) {
          logger('Error saving individual photo ${i + 1}: $e');
          failedCount++;
        }
      }

      // Show final result
      if (mounted) {
        if (savedCount > 0) {
          if (failedCount == 0) {
            SnackBarService.success(
              context: context,
              message: 'All $savedCount photos saved successfully!',
            );
          } else {
            SnackBarService.success(
              context: context,
              message:
                  '$savedCount photos saved successfully! $failedCount failed.',
            );
          }

          // Notify parent widget that photos were added
          if (widget.onPhotoAdded != null) {
            widget.onPhotoAdded!();
          }
        } else {
          SnackBarService.error(
            context: context,
            message: 'Failed to save photos to database.',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to save photos: ${e.toString()}',
        );
      }
    }
  }

  /// Save image with caption to database
  Future<void> _saveImageWithCaption(File imageFile, String caption) async {
    try {
      final String? savedImagePath =
          await ImageStorageUtils.saveImageToAppStorage(imageFile);
      if (savedImagePath == null) {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Failed to save photo.',
          );
        }
        return;
      }

      // Remove any existing cannot upload photos before saving the actual photo
      await _removeExistingCannotUploadPhotos();

      final photoSaved = await _savePhotoToDatabase(savedImagePath, caption);

      if (photoSaved != null) {
        if (mounted) {
          SnackBarService.success(
            context: context,
            message: 'Photo added successfully!',
          );
        }
        // Notify parent widget that a photo was added
        if (widget.onPhotoAdded != null) {
          widget.onPhotoAdded!();
        }
      } else {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Failed to save photo to database.',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to save photo: ${e.toString()}',
        );
      }
    }
  }

  /// Save photo to database
  Future<Photo?> _savePhotoToDatabase(String photoPath,
      [String? caption]) async {
    try {
      if (widget.taskId == null || widget.questionId == null) {
        logger('TaskId or QuestionId is null, cannot save photo');
        return null;
      }

      final taskId = int.tryParse(widget.taskId!);
      final questionId = int.tryParse(widget.questionId!);
      final measurementId = widget.measurementId != null
          ? int.tryParse(widget.measurementId!)
          : null;
      final questionpartId = widget.questionPartId != null
          ? int.tryParse(widget.questionPartId!)
          : null;

      if (taskId == null || questionId == null) {
        logger('Invalid taskId or questionId format');
        return null;
      }

      final photoId = await _photoService.getNextPhotoId(taskId: taskId);

      final photo = Photo(
        photoId: photoId,
        formId: widget.formId != null ? int.tryParse(widget.formId!) : null,
        questionId: questionId,
        measurementId: measurementId,
        measurementPhototypeId:
            widget.photoTag?.measurementPhototypeId?.toInt(),
        questionpartId: questionpartId,
        combineTypeId: widget.level,
        caption: caption ?? '',
        localPath: photoPath,
        modifiedTimeStampPhoto: DateTime.now(),
        userDeletedPhoto: false,
        cannotUploadMandatory: false,
        imageRec: false,
        photoTagId: widget.photoTag?.photoTagId?.toInt(),
        photoCombinetypeId: widget.level,
        isEdited: false,
      );

      final success = await _photoService.savePhotoToTask(
        photo: photo,
        taskId: taskId,
        folderId: widget.photoTag?.photoTagId?.toInt(),
      );

      return success ? photo : null;
    } catch (e) {
      logger('Error saving photo to database: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayItems = _getAllPhotoDisplayItems();
    final bool hasImages = displayItems.isNotEmpty;
    // Assuming your theme extension is available on the BuildContext
    final textTheme = Theme.of(context).textTheme;

    // Determine camera icon color based on is_mandatory
    final Color cameraIconColor = _getCameraIconColor();

    // Determine if HI-RES text should be shown
    final bool showHiResText = _shouldShowHiResText();

    return Container(
      margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: widget.isComplete == true
            ? AppColors.completedGreenLight
            : Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppColors.borderBlack,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 12, 20, 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: _handleDirectImageCapture,
              borderRadius: BorderRadius.circular(10),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Add photos',
                            style: textTheme.montserratTitleExtraSmall,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.photoTag?.photoTag ?? 'Tap to add photos',
                            style: textTheme.montserratTableSmall,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    // This Row is part of the tappable area
                    Row(
                      children: [
                        // Show HI-RES text only if image_rec is true
                        if (showHiResText) ...[
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade300,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 10,
                              vertical: 6,
                            ),
                            child: Text(
                              'HI-RES',
                              style: textTheme.montserratTitleSmall.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 10,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                        ],
                        Icon(
                          Icons.camera_alt_outlined,
                          color: cameraIconColor,
                          size: 24,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            if (hasImages) ...[
              const SizedBox(height: 16),
              Container(height: 1, color: Colors.grey.shade300),
              const SizedBox(height: 16),
              InkWell(
                onTap: () => widget.onImagesTap
                    ?.call(widget.photoTag?.photoTagId?.toInt()),
                borderRadius: BorderRadius.circular(8),
                child: Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 60,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: displayItems.length,
                          itemBuilder: (context, index) {
                            final item = displayItems[index];
                            return Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.grey.shade200,
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: item.isCannotUpload
                                      ? Container(
                                          width: 60,
                                          height: 60,
                                          color: Colors.black
                                              .withValues(alpha: 0.1),
                                          child: Icon(
                                            Icons.image_not_supported_outlined,
                                            color: Colors.black
                                                .withValues(alpha: 0.5),
                                            size: 30,
                                          ),
                                        )
                                      : item.imagePath != null
                                          ? Image(
                                              image: _getImageProvider(
                                                  item.imagePath!,
                                                  index: index)!,
                                              fit: BoxFit.cover,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Container(
                                                  width: 60,
                                                  height: 60,
                                                  color: Colors.grey.shade300,
                                                  child: Icon(
                                                    Icons.broken_image,
                                                    color: Colors.grey.shade600,
                                                    size: 24,
                                                  ),
                                                );
                                              },
                                              loadingBuilder: (context, child,
                                                  loadingProgress) {
                                                if (loadingProgress == null) {
                                                  return child;
                                                }
                                                return Container(
                                                  width: 60,
                                                  height: 60,
                                                  color: Colors.grey.shade200,
                                                  child: Center(
                                                    child: SizedBox(
                                                      width: 20,
                                                      height: 20,
                                                      child:
                                                          CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                        value: loadingProgress
                                                                    .expectedTotalBytes !=
                                                                null
                                                            ? loadingProgress
                                                                    .cumulativeBytesLoaded /
                                                                loadingProgress
                                                                    .expectedTotalBytes!
                                                            : null,
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              },
                                            )
                                          : Container(
                                              width: 60,
                                              height: 60,
                                              color: Colors.grey.shade300,
                                              child: Icon(
                                                Icons.broken_image,
                                                color: Colors.grey.shade600,
                                                size: 24,
                                              ),
                                            ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      Icons.chevron_right,
                      color: Colors.grey.shade600,
                      size: 24,
                    ),
                  ],
                ),
              ),
              const Gap(6),
            ],
            // Error text display
            if (widget.errorText != null) ...[
              const SizedBox(height: 8),
              Text(
                widget.errorText!,
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
