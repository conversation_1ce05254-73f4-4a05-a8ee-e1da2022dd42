import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

class FormItemCard extends StatelessWidget {
  final dynamic form;
  final VoidCallback? onTap;

  const FormItemCard({
    super.key,
    required this.form,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        title: Text(
          form?.formName ?? 'Unknown Form',
          style: textTheme.montserratSemiBold.copyWith(
            fontSize: 16,
            color: AppColors.black,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppColors.blackTint1,
        ),
        onTap: onTap,
      ),
    );
  }
}
