import 'package:storetrack_app/features/home/<USER>/models/calendar_info_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_model.dart';

import '../../../../core/database/realm_database.dart';
import '../../../../core/utils/sync_utils.dart';
import '../../domain/entities/tasks_response_entity.dart';
import '../../domain/entities/calendar_response_entity.dart';
import '../../domain/entities/profile_response_entity.dart';
import '../models/task_detail_model.dart';
import '../mappers/task_detail_mapper.dart';
import '../mappers/calendar_info_mapper.dart';
import '../mappers/profile_mapper.dart';
import '../../domain/entities/misc_setting_response_entity.dart';
import '../models/misc_setting_model.dart';
import '../mappers/misc_setting_mapper.dart';
import '../models/history_model.dart';
import '../models/alert_model.dart';
import '../models/availability_model.dart';
import '../models/leave_model.dart';
import '../models/skills_model.dart';
import '../models/induction_model.dart';
import '../models/history_response.dart';
import '../models/notification_response.dart';
import '../models/availability_response.dart';
import '../models/leave_response.dart';
import '../models/skills_response.dart';
import '../models/induction_response.dart';
import '../mappers/history_mapper.dart';
import '../mappers/alert_mapper.dart';
import '../mappers/availability_mapper.dart';
import '../mappers/leave_mapper.dart';
import '../mappers/skills_mapper.dart';
import '../mappers/induction_mapper.dart';
import '../models/country_response.dart';
import '../models/state_response.dart';
import '../models/country_model.dart' as country_realm;
import '../models/state_model.dart' as state_realm;
import '../mappers/country_mapper.dart';
import '../mappers/state_mapper.dart';

abstract class HomeLocalDataSource {
  Future<void> saveTasks(TasksResponseEntity response);
  Future<TasksResponseEntity?> getTasks();
  Future<TaskDetail?> getTaskDetail(int taskId);
  Future<void> saveCalendarInfo(CalendarResponseEntity response);
  Future<CalendarResponseEntity?> getCalendarInfo();
  Future<void> saveProfile(ProfileResponseEntity response);
  Future<ProfileResponseEntity?> getProfile();
  Future<void> saveMiscSetting(MiscSettingResponseEntity response);
  Future<MiscSettingResponseEntity?> getMiscSetting();

  // New cache methods for emulation
  Future<void> saveHistory(HistoryResponse response);
  Future<HistoryResponse?> getCachedHistory();
  Future<void> saveAlerts(NotificationResponse response);
  Future<NotificationResponse?> getCachedAlerts();
  Future<void> saveAvailability(AvailabilityResponse response);
  Future<AvailabilityResponse?> getCachedAvailability();
  Future<void> saveLeave(LeaveResponse response);
  Future<LeaveResponse?> getCachedLeave();
  Future<void> saveSkills(SkillsResponse response);
  Future<SkillsResponse?> getCachedSkills();
  Future<void> saveInduction(InductionResponse response);
  Future<InductionResponse?> getCachedInduction();
  Future<void> saveCountry(CountryResponse response);
  Future<CountryResponse?> getCachedCountry();
  Future<void> saveState(StateResponse response);
  Future<StateResponse?> getCachedState();
}

class HomeLocalDataSourceImpl implements HomeLocalDataSource {
  final RealmDatabase realmDatabase;

  HomeLocalDataSourceImpl({required this.realmDatabase});

  @override
  Future<void> saveTasks(TasksResponseEntity response) async {
    // Use the comprehensive processing logic from SyncUtils
    await SyncUtils.processTasksResponse(response);
  }

  @override
  Future<TasksResponseEntity?> getTasks() async {
    final realm = realmDatabase.realm;

    final taskModels = realm.all<TaskDetailModel>();

    if (taskModels.isEmpty) {
      return null;
    }

    // Convert models back to entities
    final taskEntities =
        taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

    return TasksResponseEntity(addTasks: taskEntities);
  }

  @override
  Future<TaskDetail?> getTaskDetail(int taskId) async {
    final realm = realmDatabase.realm;

    // Find the task with the matching taskId
    final taskModel =
        realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

    if (taskModel == null) {
      return null;
    }

    // Convert model back to entity
    return TaskDetailMapper.toEntity(taskModel);
  }

  @override
  Future<void> saveCalendarInfo(CalendarResponseEntity response) async {
    final realm = realmDatabase.realm;

    // Clear existing calendar info
    realm.write(() {
      realm.deleteAll<CalendarInfoModel>();
    });

    // Save new calendar info
    if (response.data?.calendarInfo != null) {
      realm.write(() {
        int id = 1;
        for (var calendarInfo in response.data!.calendarInfo!) {
          final calendarInfoModel =
              CalendarInfoMapper.toModel(calendarInfo, id++);
          realm.add(calendarInfoModel);
        }
      });
    }
  }

  @override
  Future<CalendarResponseEntity?> getCalendarInfo() async {
    final realm = realmDatabase.realm;

    final calendarInfoModels = realm.all<CalendarInfoModel>();

    if (calendarInfoModels.isEmpty) {
      return null;
    }

    // Convert models back to entities
    final calendarInfoEntities = calendarInfoModels
        .map((model) => CalendarInfoMapper.toEntity(model))
        .toList();

    final data = Data(calendarInfo: calendarInfoEntities);

    return CalendarResponseEntity(data: data);
  }

  @override
  Future<void> saveProfile(ProfileResponseEntity response) async {
    final realm = realmDatabase.realm;

    // Clear existing profile
    realm.write(() {
      realm.deleteAll<ProfileModel>();
    });

    // Save new profile
    if (response.data != null) {
      realm.write(() {
        final profileModel = ProfileMapper.toModel(response);
        realm.add(profileModel);
      });
    }
  }

  @override
  Future<ProfileResponseEntity?> getProfile() async {
    final realm = realmDatabase.realm;

    final profileModel = realm.query<ProfileModel>('id == 0').firstOrNull;

    if (profileModel == null) {
      return null;
    }

    // Convert model back to entity
    return ProfileMapper.toEntity(profileModel);
  }

  @override
  Future<void> saveMiscSetting(MiscSettingResponseEntity response) async {
    final realm = realmDatabase.realm;

    // Clear existing misc setting
    realm.write(() {
      realm.deleteAll<MiscSettingModel>();
    });

    // Save new misc setting
    realm.write(() {
      final miscSettingModel = MiscSettingMapper.toModel(response);
      realm.add(miscSettingModel);
    });
  }

  @override
  Future<MiscSettingResponseEntity?> getMiscSetting() async {
    final realm = realmDatabase.realm;

    final miscSettingModel = realm.find<MiscSettingModel>(0);

    if (miscSettingModel == null) {
      return null;
    }

    return MiscSettingMapper.toEntity(miscSettingModel);
  }

  // History cache implementation
  @override
  Future<void> saveHistory(HistoryResponse response) async {
    final realm = realmDatabase.realm;
    final existModel = realm.find<HistoryModel>("history");

    realm.write(() {
      if (existModel != null) {
        HistoryMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = HistoryMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<HistoryResponse?> getCachedHistory() async {
    final realm = realmDatabase.realm;
    final model = realm.find<HistoryModel>("history");

    if (model != null) {
      return HistoryMapper.toEntity(model);
    }
    return null;
  }

  // Alert cache implementation
  @override
  Future<void> saveAlerts(NotificationResponse response) async {
    final realm = realmDatabase.realm;
    final existModel = realm.find<AlertModel>("alerts");

    realm.write(() {
      if (existModel != null) {
        AlertMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = AlertMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<NotificationResponse?> getCachedAlerts() async {
    final realm = realmDatabase.realm;
    final model = realm.find<AlertModel>("alerts");

    if (model != null) {
      return AlertMapper.toEntity(model);
    }
    return null;
  }

  // Availability cache implementation
  @override
  Future<void> saveAvailability(AvailabilityResponse response) async {
    final realm = realmDatabase.realm;
    final existModel = realm.find<AvailabilityModel>("availability");

    realm.write(() {
      if (existModel != null) {
        AvailabilityMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = AvailabilityMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<AvailabilityResponse?> getCachedAvailability() async {
    final realm = realmDatabase.realm;
    final model = realm.find<AvailabilityModel>("availability");

    if (model != null) {
      return AvailabilityMapper.toEntity(model);
    }
    return null;
  }

  // Leave cache implementation
  @override
  Future<void> saveLeave(LeaveResponse response) async {
    final realm = realmDatabase.realm;
    final existModel = realm.find<LeaveModel>("leave");

    realm.write(() {
      if (existModel != null) {
        LeaveMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = LeaveMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<LeaveResponse?> getCachedLeave() async {
    final realm = realmDatabase.realm;
    final model = realm.find<LeaveModel>("leave");

    if (model != null) {
      return LeaveMapper.toEntity(model);
    }
    return null;
  }

  // Skills cache implementation
  @override
  Future<void> saveSkills(SkillsResponse response) async {
    final realm = realmDatabase.realm;
    final existModel = realm.find<SkillsModel>("skills");

    realm.write(() {
      if (existModel != null) {
        SkillsMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = SkillsMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<SkillsResponse?> getCachedSkills() async {
    final realm = realmDatabase.realm;
    final model = realm.find<SkillsModel>("skills");

    if (model != null) {
      return SkillsMapper.toEntity(model);
    }
    return null;
  }

  // Induction cache implementation
  @override
  Future<void> saveInduction(InductionResponse response) async {
    final realm = realmDatabase.realm;
    final existModel = realm.find<InductionModel>("induction");

    realm.write(() {
      if (existModel != null) {
        InductionMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = InductionMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<InductionResponse?> getCachedInduction() async {
    final realm = realmDatabase.realm;
    final model = realm.find<InductionModel>("induction");

    if (model != null) {
      return InductionMapper.toEntity(model);
    }
    return null;
  }

  @override
  Future<void> saveCountry(CountryResponse response) async {
    final realm = realmDatabase.realm;
    final existModel = realm.find<country_realm.CountryModel>("country");

    realm.write(() {
      if (existModel != null) {
        CountryMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = CountryMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<CountryResponse?> getCachedCountry() async {
    final realm = realmDatabase.realm;
    final model = realm.find<country_realm.CountryModel>("country");

    if (model != null) {
      return CountryMapper.toEntity(model);
    }
    return null;
  }

  @override
  Future<void> saveState(StateResponse response) async {
    final realm = realmDatabase.realm;
    final existModel = realm.find<state_realm.StateModel>("state");

    realm.write(() {
      if (existModel != null) {
        StateMapper.updateModel(
          existingModel: existModel,
          response: response,
        );
      } else {
        final newModel = StateMapper.toModel(response);
        realm.add(newModel);
      }
    });
  }

  @override
  Future<StateResponse?> getCachedState() async {
    final realm = realmDatabase.realm;
    final model = realm.find<state_realm.StateModel>("state");

    if (model != null) {
      return StateMapper.toEntity(model);
    }
    return null;
  }
}
