import '../../../../shared/models/result.dart';
import '../../data/models/notification_req.dart';
import '../../data/models/notification_response.dart';
import '../../data/repositories/home_repository.dart';

/// UseCase for getting notification
class GetNotificationUseCase {
  /// Constructor
  GetNotificationUseCase(this._repository);

  final HomeRepository _repository;

  Future<Result<NotificationResponse>> call(
    NotificationReqParams request, {
    bool isSync = false,
  }) async {
    return _repository.getAlerts(request, isSync: isSync);
  }
}
