import '../../../../shared/models/result.dart';
import '../../data/repositories/home_repository.dart';

class DeleteLeaveUseCase {
  final HomeRepository _repository;

  DeleteLeaveUseCase(this._repository);

  Future<Result<bool>> call({
    required String token,
    required String userId,
    required List<int> leaveIds,
  }) async {
    // Loop through each leave ID and make individual API calls
    for (final leaveId in leaveIds) {
      final result = await _repository.deleteLeave(
        token: token,
        userId: userId,
        leaveId: leaveId.toString(),
      );

      // If any deletion fails, return the failure result
      if (!result.isSuccess) {
        return result;
      }
    }

    // If all deletions succeeded, return success
    return Result.success(true);
  }
}
