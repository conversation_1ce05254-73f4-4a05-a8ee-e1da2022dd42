import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/open_available_request_entity.dart';
import '../entities/open_available_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetOpenAvailableUseCase
    implements
        UseCase<Result<OpenAvailableResponseEntity>,
            OpenAvailableRequestEntity> {
  final HomeRepository _repository;

  GetOpenAvailableUseCase(this._repository);

  @override
  Future<Result<OpenAvailableResponseEntity>> call(
      OpenAvailableRequestEntity request) async {
    return await _repository.getOpenAvailable(request);
  }
}
