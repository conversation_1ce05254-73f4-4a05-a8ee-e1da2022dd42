import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/opened_doc_entity.dart';
import '../../data/repositories/home_repository.dart';

class SendOpenedDocsUseCase
    implements
        UseCase<Result<OpenedDocResponseEntity>, OpenedDocRequestEntity> {
  final HomeRepository _repository;

  SendOpenedDocsUseCase(this._repository);

  @override
  Future<Result<OpenedDocResponseEntity>> call(
    OpenedDocRequestEntity request,
  ) async {
    return _repository.sendOpenedDocs(request);
  }
}
