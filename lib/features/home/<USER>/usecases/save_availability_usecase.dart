import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../entities/availability_request_entity.dart';

class SaveAvailabilityUseCase {
  final HomeRepository repository;

  SaveAvailabilityUseCase(this.repository);

  Future<Result<bool>> call(AvailabilityRequestEntity request) async {
    return await repository.updateAvailability(request);
  }
}
