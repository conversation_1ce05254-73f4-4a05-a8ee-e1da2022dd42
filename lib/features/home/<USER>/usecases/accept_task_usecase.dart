import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/accept_task_request_entity.dart';
import '../entities/accept_task_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class AcceptTaskUseCase
    implements
        UseCase<Result<AcceptTaskResponseEntity>, AcceptTaskRequestEntity> {
  final HomeRepository _repository;

  AcceptTaskUseCase(this._repository);

  @override
  Future<Result<AcceptTaskResponseEntity>> call(
      AcceptTaskRequestEntity request) async {
    return await _repository.acceptTask(request);
  }
}
