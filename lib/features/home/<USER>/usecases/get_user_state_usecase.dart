import '../../../../shared/models/result.dart';
import '../entities/user_state_request_entity.dart';
import '../entities/user_state_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetUserStateUseCase {
  final HomeRepository _repository;

  GetUserStateUseCase(this._repository);

  Future<Result<UserStateResponseEntity>> call({
    required UserStateRequestEntity request,
  }) async {
    return await _repository.getUserState(request: request);
  }
}
