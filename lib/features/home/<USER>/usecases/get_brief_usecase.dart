import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/brief_request_entity.dart';
import '../entities/brief_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetBriefUseCase
    implements UseCase<Result<BriefResponseEntity>, BriefRequestEntity> {
  final HomeRepository _repository;

  GetBriefUseCase(this._repository);

  @override
  Future<Result<BriefResponseEntity>> call(BriefRequestEntity request) async {
    return await _repository.getBrief(request);
  }
}
