import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/open_accepted_request_entity.dart';
import '../entities/open_accepted_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class GetOpenAcceptedUseCase
    implements
        UseCase<Result<OpenAcceptedResponseEntity>, OpenAcceptedRequestEntity> {
  final HomeRepository _repository;

  GetOpenAcceptedUseCase(this._repository);

  @override
  Future<Result<OpenAcceptedResponseEntity>> call(
      OpenAcceptedRequestEntity request) async {
    return await _repository.getOpenAccepted(request);
  }
}
