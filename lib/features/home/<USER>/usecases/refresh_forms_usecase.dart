import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../entities/refresh_forms_entity.dart';
import '../entities/tasks_response_entity.dart';
import '../../data/repositories/home_repository.dart';

class RefreshFormsUseCase
    implements UseCase<Result<TasksResponseEntity>, RefreshFormsEntity> {
  final HomeRepository _repository;
  RefreshFormsUseCase(this._repository);

  @override
  Future<Result<TasksResponseEntity>> call(RefreshFormsEntity request) async {
    return _repository.refreshTasks(request);
  }
}
