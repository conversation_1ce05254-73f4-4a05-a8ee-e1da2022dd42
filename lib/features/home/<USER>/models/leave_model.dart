import 'package:realm/realm.dart';

part 'leave_model.realm.dart';

// Leave Model (structured fields)
@RealmModel()
abstract class _LeaveModel {
  @PrimaryKey()
  late String id; // "leave"

  // List of leaves
  late List<_LeaveItemModel> leaves;
}

// Individual leave item model
@RealmModel()
abstract class _LeaveItemModel {
  late int leaveId;
  late DateTime leaveDate;
  String? leaveTitle;
  String? leaveDescription;
  late bool isSelected;
}
