// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'address_skill_validation_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class AddressSkillValidationModel extends _AddressSkillValidationModel
    with RealmEntity, RealmObjectBase, RealmObject {
  AddressSkillValidationModel(
    bool isAddressValidated,
    bool isSkillValidated,
    bool isAvailabilityValidated,
  ) {
    RealmObjectBase.set(this, 'isAddressValidated', isAddressValidated);
    RealmObjectBase.set(this, 'isSkillValidated', isSkillValidated);
    RealmObjectBase.set(
        this, 'isAvailabilityValidated', isAvailabilityValidated);
  }

  AddressSkillValidationModel._();

  @override
  bool get isAddressValidated =>
      RealmObjectBase.get<bool>(this, 'isAddressValidated') as bool;
  @override
  set isAddressValidated(bool value) =>
      RealmObjectBase.set(this, 'isAddressValidated', value);

  @override
  bool get isSkillValidated =>
      RealmObjectBase.get<bool>(this, 'isSkillValidated') as bool;
  @override
  set isSkillValidated(bool value) =>
      RealmObjectBase.set(this, 'isSkillValidated', value);

  @override
  bool get isAvailabilityValidated =>
      RealmObjectBase.get<bool>(this, 'isAvailabilityValidated') as bool;
  @override
  set isAvailabilityValidated(bool value) =>
      RealmObjectBase.set(this, 'isAvailabilityValidated', value);

  @override
  Stream<RealmObjectChanges<AddressSkillValidationModel>> get changes =>
      RealmObjectBase.getChanges<AddressSkillValidationModel>(this);

  @override
  Stream<RealmObjectChanges<AddressSkillValidationModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<AddressSkillValidationModel>(
          this, keyPaths);

  @override
  AddressSkillValidationModel freeze() =>
      RealmObjectBase.freezeObject<AddressSkillValidationModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'isAddressValidated': isAddressValidated.toEJson(),
      'isSkillValidated': isSkillValidated.toEJson(),
      'isAvailabilityValidated': isAvailabilityValidated.toEJson(),
    };
  }

  static EJsonValue _toEJson(AddressSkillValidationModel value) =>
      value.toEJson();
  static AddressSkillValidationModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'isAddressValidated': EJsonValue isAddressValidated,
        'isSkillValidated': EJsonValue isSkillValidated,
        'isAvailabilityValidated': EJsonValue isAvailabilityValidated,
      } =>
        AddressSkillValidationModel(
          fromEJson(isAddressValidated),
          fromEJson(isSkillValidated),
          fromEJson(isAvailabilityValidated),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(AddressSkillValidationModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject,
        AddressSkillValidationModel, 'AddressSkillValidationModel', [
      SchemaProperty('isAddressValidated', RealmPropertyType.bool),
      SchemaProperty('isSkillValidated', RealmPropertyType.bool),
      SchemaProperty('isAvailabilityValidated', RealmPropertyType.bool),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
