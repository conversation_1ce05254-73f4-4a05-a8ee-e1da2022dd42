// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'state_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class StateModel extends _StateModel
    with RealmEntity, RealmObjectBase, RealmObject {
  StateModel(
    String id,
    String jsonData,
    DateTime lastUpdated,
  ) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'jsonData', jsonData);
    RealmObjectBase.set(this, 'lastUpdated', lastUpdated);
  }

  StateModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get jsonData =>
      RealmObjectBase.get<String>(this, 'jsonData') as String;
  @override
  set jsonData(String value) => RealmObjectBase.set(this, 'jsonData', value);

  @override
  DateTime get lastUpdated =>
      RealmObjectBase.get<DateTime>(this, 'lastUpdated') as DateTime;
  @override
  set lastUpdated(DateTime value) =>
      RealmObjectBase.set(this, 'lastUpdated', value);

  @override
  Stream<RealmObjectChanges<StateModel>> get changes =>
      RealmObjectBase.getChanges<StateModel>(this);

  @override
  Stream<RealmObjectChanges<StateModel>> changesFor([List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<StateModel>(this, keyPaths);

  @override
  StateModel freeze() => RealmObjectBase.freezeObject<StateModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'jsonData': jsonData.toEJson(),
      'lastUpdated': lastUpdated.toEJson(),
    };
  }

  static EJsonValue _toEJson(StateModel value) => value.toEJson();
  static StateModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'jsonData': EJsonValue jsonData,
        'lastUpdated': EJsonValue lastUpdated,
      } =>
        StateModel(
          fromEJson(id),
          fromEJson(jsonData),
          fromEJson(lastUpdated),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(StateModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, StateModel, 'StateModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('jsonData', RealmPropertyType.string),
      SchemaProperty('lastUpdated', RealmPropertyType.timestamp),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
