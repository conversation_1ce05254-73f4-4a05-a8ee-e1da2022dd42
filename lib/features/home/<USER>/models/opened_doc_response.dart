import 'dart:convert';

class OpenedDocResponse {
  final bool success;
  final String message;

  OpenedDocResponse({
    required this.success,
    required this.message,
  });

  factory OpenedDocResponse.fromRawJson(String str) =>
      OpenedDocResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OpenedDocResponse.fromJson(Map<String, dynamic> json) =>
      OpenedDocResponse(
        success: json["success"] ?? false,
        message: json["message"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
      };
}
