// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leave_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class LeaveModel extends _LeaveModel
    with RealmEntity, RealmObjectBase, RealmObject {
  LeaveModel(
    String id, {
    Iterable<LeaveItemModel> leaves = const [],
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set<RealmList<LeaveItemModel>>(
        this, 'leaves', RealmList<LeaveItemModel>(leaves));
  }

  LeaveModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  RealmList<LeaveItemModel> get leaves =>
      RealmObjectBase.get<LeaveItemModel>(this, 'leaves')
          as RealmList<LeaveItemModel>;
  @override
  set leaves(covariant RealmList<LeaveItemModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<LeaveModel>> get changes =>
      RealmObjectBase.getChanges<LeaveModel>(this);

  @override
  Stream<RealmObjectChanges<LeaveModel>> changesFor([List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<LeaveModel>(this, keyPaths);

  @override
  LeaveModel freeze() => RealmObjectBase.freezeObject<LeaveModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'leaves': leaves.toEJson(),
    };
  }

  static EJsonValue _toEJson(LeaveModel value) => value.toEJson();
  static LeaveModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        LeaveModel(
          fromEJson(id),
          leaves: fromEJson(ejson['leaves']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(LeaveModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, LeaveModel, 'LeaveModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('leaves', RealmPropertyType.object,
          linkTarget: 'LeaveItemModel',
          collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class LeaveItemModel extends _LeaveItemModel
    with RealmEntity, RealmObjectBase, RealmObject {
  LeaveItemModel(
    int leaveId,
    DateTime leaveDate,
    bool isSelected, {
    String? leaveTitle,
    String? leaveDescription,
  }) {
    RealmObjectBase.set(this, 'leaveId', leaveId);
    RealmObjectBase.set(this, 'leaveDate', leaveDate);
    RealmObjectBase.set(this, 'leaveTitle', leaveTitle);
    RealmObjectBase.set(this, 'leaveDescription', leaveDescription);
    RealmObjectBase.set(this, 'isSelected', isSelected);
  }

  LeaveItemModel._();

  @override
  int get leaveId => RealmObjectBase.get<int>(this, 'leaveId') as int;
  @override
  set leaveId(int value) => RealmObjectBase.set(this, 'leaveId', value);

  @override
  DateTime get leaveDate =>
      RealmObjectBase.get<DateTime>(this, 'leaveDate') as DateTime;
  @override
  set leaveDate(DateTime value) =>
      RealmObjectBase.set(this, 'leaveDate', value);

  @override
  String? get leaveTitle =>
      RealmObjectBase.get<String>(this, 'leaveTitle') as String?;
  @override
  set leaveTitle(String? value) =>
      RealmObjectBase.set(this, 'leaveTitle', value);

  @override
  String? get leaveDescription =>
      RealmObjectBase.get<String>(this, 'leaveDescription') as String?;
  @override
  set leaveDescription(String? value) =>
      RealmObjectBase.set(this, 'leaveDescription', value);

  @override
  bool get isSelected => RealmObjectBase.get<bool>(this, 'isSelected') as bool;
  @override
  set isSelected(bool value) => RealmObjectBase.set(this, 'isSelected', value);

  @override
  Stream<RealmObjectChanges<LeaveItemModel>> get changes =>
      RealmObjectBase.getChanges<LeaveItemModel>(this);

  @override
  Stream<RealmObjectChanges<LeaveItemModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<LeaveItemModel>(this, keyPaths);

  @override
  LeaveItemModel freeze() => RealmObjectBase.freezeObject<LeaveItemModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'leaveId': leaveId.toEJson(),
      'leaveDate': leaveDate.toEJson(),
      'leaveTitle': leaveTitle.toEJson(),
      'leaveDescription': leaveDescription.toEJson(),
      'isSelected': isSelected.toEJson(),
    };
  }

  static EJsonValue _toEJson(LeaveItemModel value) => value.toEJson();
  static LeaveItemModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'leaveId': EJsonValue leaveId,
        'leaveDate': EJsonValue leaveDate,
        'isSelected': EJsonValue isSelected,
      } =>
        LeaveItemModel(
          fromEJson(leaveId),
          fromEJson(leaveDate),
          fromEJson(isSelected),
          leaveTitle: fromEJson(ejson['leaveTitle']),
          leaveDescription: fromEJson(ejson['leaveDescription']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(LeaveItemModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, LeaveItemModel, 'LeaveItemModel', [
      SchemaProperty('leaveId', RealmPropertyType.int),
      SchemaProperty('leaveDate', RealmPropertyType.timestamp),
      SchemaProperty('leaveTitle', RealmPropertyType.string, optional: true),
      SchemaProperty('leaveDescription', RealmPropertyType.string,
          optional: true),
      SchemaProperty('isSelected', RealmPropertyType.bool),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
