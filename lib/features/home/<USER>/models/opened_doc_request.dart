import 'dart:convert';

class OpenedDocRequest {
  final String userId;
  final String deviceToken;
  final List<OpenedDocItem> messageRead;

  OpenedDocRequest({
    required this.userId,
    required this.deviceToken,
    required this.messageRead,
  });

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "device_token": deviceToken,
        "MessageRead": messageRead.map((item) => item.toJson()).toList(),
      };

  String toRawJson() => json.encode(toJson());

  factory OpenedDocRequest.fromJson(Map<String, dynamic> json) =>
      OpenedDocRequest(
        userId: json["user_id"],
        deviceToken: json["device_token"],
        messageRead: List<OpenedDocItem>.from(
          json["MessageRead"].map((x) => OpenedDocItem.fromJson(x)),
        ),
      );
}

class OpenedDocItem {
  final String docId;
  final String timeStamp;

  OpenedDocItem({
    required this.docId,
    required this.timeStamp,
  });

  Map<String, dynamic> toJson() => {
        "doc_id": docId,
        "time_stamp": timeStamp,
      };

  factory OpenedDocItem.fromJson(Map<String, dynamic> json) => OpenedDocItem(
        docId: json["doc_id"],
        timeStamp: json["time_stamp"],
      );
}
