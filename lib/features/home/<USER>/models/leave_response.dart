import 'dart:convert';

class LeaveResponse {
  final LeaveData data; // Changed to final for immutability

  LeaveResponse({
    required this.data,
  });

  factory LeaveResponse.fromRawJson(String str) =>
      LeaveResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LeaveResponse.fromJson(Map<String, dynamic> json) => LeaveResponse(
        data: LeaveData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data.toJson(),
      };

  // copyWith method for LeaveResponse
  LeaveResponse copyWith({
    LeaveData? data,
  }) {
    return LeaveResponse(
      data: data ?? this.data,
    );
  }
}

class LeaveData {
  final List<Leaf> leaves; // Changed to final for immutability

  LeaveData({
    required this.leaves,
  });

  factory LeaveData.fromRawJson(String str) =>
      LeaveData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LeaveData.fromJson(Map<String, dynamic> json) => LeaveData(
        leaves: List<Leaf>.from(json["leaves"].map((x) => Leaf.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "leaves": List<dynamic>.from(leaves.map((x) => x.toJson())),
      };

  // copyWith method for LeaveData
  LeaveData copyWith({
    List<Leaf>? leaves,
  }) {
    return LeaveData(
      leaves: leaves ?? this.leaves,
    );
  }
}

class Leaf {
  final int leaveId;
  final DateTime leaveDate;
  final String? leaveTitle;
  final String? leaveDescription;
  final bool isSelected; // Changed to final for immutability

  Leaf({
    required this.leaveId,
    required this.leaveDate,
    this.leaveTitle,
    this.leaveDescription,
    this.isSelected = false,
  });

  factory Leaf.fromRawJson(String str) => Leaf.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Leaf.fromJson(Map<String, dynamic> json) => Leaf(
        leaveId: json["leave_id"],
        leaveDate: DateTime.parse(json["leave_date"]),
        leaveTitle: json["leave_title"],
        leaveDescription: json["leave_description"],
        isSelected: json["is_selected"] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "leave_id": leaveId,
        "leave_date": leaveDate.toIso8601String(),
        "leave_title": leaveTitle,
        "leave_description": leaveDescription,
        "is_selected": isSelected,
      };

  // copyWith method for Leaf
  Leaf copyWith({
    int? leaveId,
    DateTime? leaveDate,
    String? leaveTitle,
    String? leaveDescription,
    bool? isSelected,
  }) {
    return Leaf(
      leaveId: leaveId ?? this.leaveId,
      leaveDate: leaveDate ?? this.leaveDate,
      leaveTitle: leaveTitle ?? this.leaveTitle,
      leaveDescription: leaveDescription ?? this.leaveDescription,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
