import 'package:realm/realm.dart';

part 'alert_model.realm.dart';

// Alert Model (structured fields)
@RealmModel()
abstract class _AlertModel {
  @PrimaryKey()
  late String id; // "alerts"

  // List of alerts
  late List<_AlertItemModel> alerts;
}

// Individual alert item
@RealmModel()
abstract class _AlertItemModel {
  late int alertId;
  late int clientId;
  late int userId;
  late String title;
  late String shortDescription;
  late String comment;
  late String sender;
  late DateTime date;
  late String clientLogoUrl;
  late bool isRead;
}
