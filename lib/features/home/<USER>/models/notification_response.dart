class NotificationResponse {
  final NotificationData data;

  NotificationResponse({required this.data});

  factory NotificationResponse.fromJson(Map<String, dynamic> json) {
    return NotificationResponse(
      data: NotificationData.fromJson(json['data']),
    );
  }
}

class NotificationData {
  final List<Alert> alerts;

  NotificationData({required this.alerts});

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    var alertsList =
        (json['alerts'] as List).map((alert) => Alert.fromJson(alert)).toList();
    return NotificationData(alerts: alertsList);
  }
}

class Alert {
  final num alertId;
  final num clientId;
  final num userId;
  final String title;
  final String shortDescription;
  final String comment;
  final String sender;
  final DateTime date;
  final String clientLogoUrl;
  final bool isRead;

  Alert({
    required this.alertId,
    required this.clientId,
    required this.userId,
    required this.title,
    required this.shortDescription,
    required this.comment,
    required this.sender,
    required this.date,
    required this.clientLogoUrl,
    required this.isRead,
  });

  factory Alert.fromJson(Map<String, dynamic> json) {
    return Alert(
      alertId: json['alert_id'],
      clientId: json['client_id'],
      userId: json['user_id'],
      title: json['title'],
      shortDescription: json['short_description'],
      comment: json['comment'],
      sender: json['sender'],
      date: DateTime.parse(json['date']),
      clientLogoUrl: json['client_logo_url'],
      isRead: json['is_read'],
    );
  }
}
