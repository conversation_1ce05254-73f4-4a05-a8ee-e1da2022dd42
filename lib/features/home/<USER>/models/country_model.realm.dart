// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'country_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// ignore_for_file: type=lint
class CountryModel extends _CountryModel
    with RealmEntity, RealmObjectBase, RealmObject {
  CountryModel(
    String id,
    String jsonData,
    DateTime lastUpdated,
  ) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'jsonData', jsonData);
    RealmObjectBase.set(this, 'lastUpdated', lastUpdated);
  }

  CountryModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get jsonData =>
      RealmObjectBase.get<String>(this, 'jsonData') as String;
  @override
  set jsonData(String value) => RealmObjectBase.set(this, 'jsonData', value);

  @override
  DateTime get lastUpdated =>
      RealmObjectBase.get<DateTime>(this, 'lastUpdated') as DateTime;
  @override
  set lastUpdated(DateTime value) =>
      RealmObjectBase.set(this, 'lastUpdated', value);

  @override
  Stream<RealmObjectChanges<CountryModel>> get changes =>
      RealmObjectBase.getChanges<CountryModel>(this);

  @override
  Stream<RealmObjectChanges<CountryModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<CountryModel>(this, keyPaths);

  @override
  CountryModel freeze() => RealmObjectBase.freezeObject<CountryModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'jsonData': jsonData.toEJson(),
      'lastUpdated': lastUpdated.toEJson(),
    };
  }

  static EJsonValue _toEJson(CountryModel value) => value.toEJson();
  static CountryModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'jsonData': EJsonValue jsonData,
        'lastUpdated': EJsonValue lastUpdated,
      } =>
        CountryModel(
          fromEJson(id),
          fromEJson(jsonData),
          fromEJson(lastUpdated),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(CountryModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, CountryModel, 'CountryModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('jsonData', RealmPropertyType.string),
      SchemaProperty('lastUpdated', RealmPropertyType.timestamp),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
