import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/skill/skills_cubit.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import '../../data/models/skills_response.dart';

@RoutePage()
class SkillsPage extends StatelessWidget {
  const SkillsPage({super.key});

  // Count how many skills are selected
  int _howManySelected(List<Skill> skills) {
    int count = 0;
    for (Skill skill in skills) {
      if (skill.has) {
        count++;
      }
    }
    return count;
  }

  // Show validation error dialog
  void _showSkillsRequiredDialog(BuildContext context) {
    ConfirmDialog.show(
      context: context,
      title: 'Skills required',
      message: 'At least one skill needs to be selected',
      confirmText: 'OK',
      onConfirm: () {
        context.router.maybePop();
      },
    );
  }

  Widget _buildSkillItem(Skill skill, VoidCallback onTap, TextTheme texttheme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        title: Text(
          skill.skillName,
          style: texttheme.montserratSemiBold.copyWith(
            fontSize: 16,
            color: AppColors.black,
          ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 5),
          child: Text(
            skill.skillDescription,
            style: const TextStyle(
              fontFamily: AppFonts.montserrat,
              color: AppColors.blackTint1,
            ),
          ),
        ),
        trailing: Icon(
          Icons.check_circle,
          color: skill.has ? AppColors.primaryBlue : AppColors.black20,
        ),
        onTap: onTap,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return BlocProvider(
      create: (_) => sl<SkillsCubit>()..fetchSkills(),
      child: BlocConsumer<SkillsCubit, SkillsState>(
        listener: (context, state) {
          if (state is SkillsError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          } else if (state is SkillsSaved) {
            SnackBarService.success(
              context: context,
              message: 'Skills changes saved successfully',
            );
            context.read<SkillsCubit>().fetchSkills(isSync: true);
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: AppColors.lightGrey2,
            appBar: CustomAppBar(
              title: 'Skills',
              onBackPressed: () => context.router.maybePop(),
              actions: state is SkillsLoaded
                  ? [
                      IconButton(
                        onPressed: state is SkillsSaving
                            ? null
                            : () {
                                // Validation: At least one skill must be selected
                                final skills =
                                    state.response.data?.skills ?? [];
                                int selectedCount = _howManySelected(skills);

                                if (selectedCount <= 0) {
                                  // Show error dialog
                                  _showSkillsRequiredDialog(context);
                                } else {
                                  // Proceed with API call
                                  context
                                      .read<SkillsCubit>()
                                      .saveSkillChanges();
                                }
                              },
                        icon: state is SkillsSaving
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Icon(Icons.check),
                      ),
                    ]
                  : null,
            ),
            body: _buildBody(state, context, textTheme),
          );
        },
      ),
    );
  }

  Widget _buildBody(
      SkillsState state, BuildContext context, TextTheme textTheme) {
    if (state is SkillsLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    } else if (state is SkillsLoaded) {
      final skills = state.response.data?.skills ?? [];
      return ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: skills.length,
        itemBuilder: (context, index) {
          final skill = skills[index];
          return _buildSkillItem(skill, () {
            context.read<SkillsCubit>().toggleSkillSelection(skill.skillId);
          }, textTheme);
        },
      );
    } else if (state is SkillsSaving) {
      // Show the previous loaded state with skills while saving
      final skills = state.response.data?.skills ?? [];
      return ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: skills.length,
        itemBuilder: (context, index) {
          final skill = skills[index];
          return _buildSkillItem(skill, () {
            // Disable interaction while saving
          }, textTheme);
        },
      );
    } else if (state is SkillsError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.blackTint1,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontFamily: AppFonts.montserrat,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                state.message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontFamily: AppFonts.montserrat,
                  color: AppColors.blackTint1,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context.read<SkillsCubit>().fetchSkills();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
