import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/shared/theme/app_date_picker_theme.dart';
import '../blocs/add_leave/add_leave_cubit.dart';

@RoutePage()
class AddLeavePage extends StatefulWidget {
  const AddLeavePage({super.key});

  @override
  State<AddLeavePage> createState() => _AddLeavePageState();
}

class _AddLeavePageState extends State<AddLeavePage> {
  DateTime? startDate;
  DateTime? endDate;
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  int get dayCount {
    if (startDate == null || endDate == null) return 0;
    return endDate!.difference(startDate!).inDays + 1;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => sl<AddLeaveCubit>(),
      child: BlocConsumer<AddLeaveCubit, AddLeaveState>(
        listener: (context, state) {
          if (state is AddLeaveError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          } else if (state is AddLeaveSuccess) {
            SnackBarService.success(
              context: context,
              message: 'Leave request submitted successfully',
            );
            context.router.maybePop(true); // Pass true to indicate success
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(0xFFF5F5F5),
            appBar: CustomAppBar(
              title: 'Add Leave',
              onBackPressed: () => context.router.maybePop(),
            ),
            body: _buildBody(state, context),
          );
        },
      ),
    );
  }

  Widget _buildBody(AddLeaveState state, BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Start Date Section
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        color: AppColors.primaryBlue,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Start Date',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: AppFonts.montserrat,
                          color: AppColors.black,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  GestureDetector(
                    onTap: () => _selectDate(context, true),
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          vertical: 16, horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: startDate != null
                              ? AppColors.primaryBlue.withValues(alpha: 0.4)
                              : AppColors.black20,
                          width: startDate != null ? 1.5 : 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        color: startDate != null
                            ? AppColors.primaryBlue.withValues(alpha: 0.05)
                            : Colors.transparent,
                      ),
                      child: Text(
                        startDate != null
                            ? _dateFormat.format(startDate!)
                            : 'Select start date',
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: AppFonts.montserrat,
                          fontWeight: startDate != null
                              ? FontWeight.w500
                              : FontWeight.w400,
                          color: startDate != null
                              ? AppColors.black
                              : AppColors.blackTint1,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // End Date Section
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.event_outlined,
                        color: AppColors.primaryBlue,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'End Date',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: AppFonts.montserrat,
                          color: AppColors.black,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  GestureDetector(
                    onTap: startDate != null
                        ? () => _selectDate(context, false)
                        : null,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          vertical: 16, horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: startDate == null
                              ? AppColors.black20.withValues(alpha: 0.5)
                              : endDate != null
                                  ? AppColors.primaryBlue.withValues(alpha: 0.4)
                                  : AppColors.black20,
                          width: endDate != null ? 1.5 : 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                        color: startDate == null
                            ? AppColors.black20.withValues(alpha: 0.1)
                            : endDate != null
                                ? AppColors.primaryBlue.withValues(alpha: 0.05)
                                : Colors.transparent,
                      ),
                      child: Text(
                        endDate != null
                            ? _dateFormat.format(endDate!)
                            : startDate != null
                                ? 'Select end date'
                                : 'Select start date first',
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: AppFonts.montserrat,
                          fontWeight: endDate != null
                              ? FontWeight.w500
                              : FontWeight.w400,
                          color: startDate == null
                              ? AppColors.blackTint1.withValues(alpha: 0.6)
                              : endDate != null
                                  ? AppColors.black
                                  : AppColors.blackTint1,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Days Count Display
          if (startDate != null && endDate != null) ...[
            const SizedBox(height: 20),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: AppColors.primaryBlue.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    color: AppColors.primaryBlue,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '$dayCount ${dayCount == 1 ? 'day' : 'days'} selected',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: AppFonts.montserrat,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 32),

          // Submit Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: (startDate != null &&
                      endDate != null &&
                      state is! AddLeaveLoading)
                  ? () => _submitLeaveRequest(context)
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: state is AddLeaveLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          dayCount > 1
                              ? 'Submitting $dayCount days...'
                              : 'Submitting...',
                          style: const TextStyle(
                            fontFamily: AppFonts.montserrat,
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    )
                  : Text(
                      dayCount > 1
                          ? 'Submit $dayCount Days Leave'
                          : 'Submit Leave Request',
                      style: const TextStyle(
                        fontFamily: AppFonts.montserrat,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? DateTime.now() : (startDate ?? DateTime.now()),
      firstDate: isStartDate ? DateTime.now() : (startDate ?? DateTime.now()),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: AppDatePickerTheme.getTheme(context),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          startDate = picked;
          // If end date is before start date, reset end date
          if (endDate != null && endDate!.isBefore(picked)) {
            endDate = null;
          }
        } else {
          endDate = picked;
        }
      });
    }
  }

  void _submitLeaveRequest(BuildContext context) {
    if (startDate == null || endDate == null) {
      SnackBarService.error(
        context: context,
        message: 'Please select both start and end dates',
      );
      return;
    }

    if (endDate!.isBefore(startDate!)) {
      SnackBarService.error(
        context: context,
        message: 'End date cannot be before start date',
      );
      return;
    }

    context.read<AddLeaveCubit>().submitLeaveRequest(
          startDate: startDate!,
          endDate: endDate!,
        );
  }
}
