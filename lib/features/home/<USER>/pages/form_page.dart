import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/form_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_refresh/form_refresh_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_page/form_page_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/form_page/form_page_state.dart';
import 'package:storetrack_app/di/service_locator.dart';

/// A page that displays a list of forms for a specific task
///
/// This page provides:
/// - Dynamic progress tracking for each form
/// - Navigation to QuestionPage or WebBrowser for vision forms
/// - Real-time progress updates after returning from form pages
/// - Comprehensive error handling and retry functionality
/// - Proper state management with caching for performance
@RoutePage()
class FormPage extends StatefulWidget {
  final int taskId;

  const FormPage({
    super.key,
    required this.taskId,
  });

  @override
  State<FormPage> createState() => _FormPageState();
}

class _FormPageState extends State<FormPage> {
  @override
  Widget build(BuildContext context) {
    // Provide the FormPageCubit here.
    // The actual data loading is moved to `_FormPageContent.initState`
    // to prevent blocking the initial build of this page.
    return BlocProvider<FormPageCubit>(
      create: (context) => sl<FormPageCubit>(),
      child: _FormPageContent(taskId: widget.taskId),
    );
  }
}

class _FormPageContent extends StatefulWidget {
  final int taskId;

  const _FormPageContent({
    required this.taskId,
  });

  @override
  State<_FormPageContent> createState() => _FormPageContentState();
}

class _FormPageContentState extends State<_FormPageContent>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // FIX: Load data after the first frame to prevent jank on page open.
    // By using `addPostFrameCallback`, we ensure the initial page transition
    // is smooth (showing a loading indicator), and data fetching starts immediately after.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final cubit = context.read<FormPageCubit>();
        cubit.loadTask(widget.taskId);
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Reload task data when app becomes active (user returns to app)
    if (state == AppLifecycleState.resumed && mounted) {
      context.read<FormPageCubit>().loadTask(widget.taskId);
    }
  }

  // FIX: Removed `didChangeDependencies`. Its logic was complex and a likely
  // source of jank from unnecessary rebuilds when popping back to this page.
  // The refresh-on-return is now handled cleanly by the `await` in `_navigateToQuestion`.

  /// Handle form navigation and refresh progress after returning
  ///
  /// Supports both regular forms (QuestionPage) and vision forms (WebBrowser)
  /// Automatically refreshes progress data when returning from any form
  Future<void> _navigateToQuestion(entities.Form form) async {
    if (form.formId == null) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Invalid form data. Cannot open form.',
        );
      }
      return;
    }

    try {
      if (form.isVisionForm == true) {
        final visionUrl = form.visionFormUrl;
        if (visionUrl == null || visionUrl.isEmpty) {
          if (mounted) {
            SnackBarService.error(
              context: context,
              message: 'Vision form URL not available.',
            );
          }
          return;
        }

        await context.router
            .push(WebBrowserRoute(url: visionUrl, title: form.formName));
      } else {
        await context.router.push(
          QuestionRoute(
            formId: form.formId!,
            taskId: widget.taskId,
          ),
        );
      }

      // Reload task data after returning from form
      if (mounted) {
        context.read<FormPageCubit>().loadTask(widget.taskId);
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to open form: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // FIX: The FormRefreshCubit instance is captured here to be used safely
    // in the PopScope callback, avoiding context issues after the widget is popped.
    final formRefreshCubit = context.read<FormRefreshCubit>();

    // FIX: Removed the BlocListener<FormRefreshCubit, ...>. This page should
    // manage its own state updates (on-load, on-return) rather than listening for
    // a broad refresh event, which can cause redundant and janky rebuilds.
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          // FIX: Notify the previous page to refresh after this page has been popped.
          // Using `addPostFrameCallback` ensures the pop animation completes smoothly
          // before the previous page's state is updated, preventing jank.
          // The previous implementation was likely broken due to an incorrect `mounted` check.
          WidgetsBinding.instance.addPostFrameCallback((_) {
            formRefreshCubit.refresh();
          });
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.lightGrey2,
        body: BlocBuilder<FormPageCubit, FormPageState>(
          builder: (context, state) {
            return Scaffold(
              backgroundColor: AppColors.lightGrey2,
              appBar: _buildAppBar(context, state),
              body: _buildBody(context, textTheme, state),
            );
          },
        ),
      ),
    );
  }

  /// Check if task is completed (Successful or Unsuccessful)
  bool _isTaskCompleted(String? taskStatus) {
    return taskStatus == 'Successful' || taskStatus == 'Unsuccessful';
  }

  /// Build the app bar with conditional refresh button
  PreferredSizeWidget _buildAppBar(BuildContext context, FormPageState state) {
    entities.TaskDetail? task;

    if (state is FormPageLoaded) {
      task = state.task;
    } else if (state is FormPageProgressLoading) {
      task = state.task;
    } else if (state is FormPageProgressLoaded) {
      task = state.task;
    }

    final shouldShowRefresh = _isTaskCompleted(task?.taskStatus);

    return CustomAppBar(
      title: 'Forms',
      actions: shouldShowRefresh
          ? [
              SizedBox(
                height: 32,
                width: 120,
                child: AppButton(
                  radius: 6,
                  text: 'Refresh forms',
                  color: AppColors.primaryBlue,
                  textColor: Colors.white,
                  height: 32,
                  onPressed: () {
                    context.read<FormPageCubit>().refreshForms(widget.taskId);
                  },
                ),
              ),
              const Gap(16),
            ]
          : null,
    );
  }

  Widget _buildBody(
      BuildContext context, TextTheme textTheme, FormPageState state) {
    if (state is FormPageLoading || state is FormPageRefreshing) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    }

    if (state is FormPageError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const Gap(16),
            Text(
              'Error loading forms',
              style: textTheme.titleMedium,
            ),
            const Gap(8),
            Text(
              state.message,
              style: textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const Gap(16),
            ElevatedButton(
              onPressed: () =>
                  context.read<FormPageCubit>().loadTask(widget.taskId),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    entities.TaskDetail? task;
    Map<int, Map<String, dynamic>>? progressData;

    if (state is FormPageLoaded) {
      task = state.task;
    } else if (state is FormPageProgressLoading) {
      task = state.task;
    } else if (state is FormPageProgressLoaded) {
      task = state.task;
      progressData = state.progressData;
    }

    if (task == null) {
      return const Center(
        child: EmptyState(message: 'No task data available'),
      );
    }

    final formItems = task.forms;
    final isCompleted = _isTaskCompleted(task.taskStatus);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(8),
          formItems == null || formItems.isEmpty
              ? Center(
                  child: EmptyState(
                    message: isCompleted
                        ? 'Completed task forms may not show or show old information only. Please \'Refresh Forms\' to get the latest information.'
                        : 'No forms available for this task',
                  ),
                )
              : _buildFormsList(formItems, state, progressData),
          const Gap(8),
        ],
      ),
    );
  }

  Widget _buildFormsList(
    List<entities.Form> formItems,
    FormPageState state,
    Map<int, Map<String, dynamic>>? progressData,
  ) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      itemCount: formItems.length,
      itemBuilder: (context, index) {
        final form = formItems[index];
        return _buildFormItem(form, state, progressData);
      },
      separatorBuilder: (BuildContext context, int index) {
        return const Gap(8);
      },
    );
  }

  Widget _buildFormItem(
    entities.Form form,
    FormPageState state,
    Map<int, Map<String, dynamic>>? progressData,
  ) {
    final formId = form.formId?.toInt();
    final isMandatory = form.isMandatory ?? false;

    Map<String, dynamic>? cachedProgress;
    if (formId != null && progressData != null) {
      cachedProgress = progressData[formId];
    }

    double progress = 0.0;
    String progressText = 'Loading...';
    bool isCompleted = false;

    if (cachedProgress != null) {
      progress = cachedProgress['progress'] as double;
      progressText = cachedProgress['progressText'] as String;
      isCompleted = cachedProgress['isCompleted'] as bool? ?? false;
    } else if (state is FormPageProgressLoading) {
      progressText = 'Updating...';
    }

    return GestureDetector(
      onTap: () => _navigateToQuestion(form),
      child: FormCard(
        title: form.formName ?? 'Unnamed Form',
        progress: progress,
        progressText: progressText,
        width: 1.0,
        isMandatory: isMandatory,
        isVisionForm: form.isVisionForm ?? false,
        isCompleted: isCompleted,
      ),
    );
  }
}
