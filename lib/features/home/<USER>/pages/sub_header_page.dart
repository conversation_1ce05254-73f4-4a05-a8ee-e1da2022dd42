import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/blocs/task_details/task_details_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/task_details/task_details_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/sub_header_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/search_bar_with_scanner.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';

/// A page that displays a list of question parts with progress indicators
///
/// This page shows question parts in a clean, organized list format with:
/// - Progress bars for each item showing completion status
/// - Mandatory indicators for required items
/// - Consistent styling following app design patterns
/// - Optimized performance with separated widget components
@RoutePage()
class SubHeaderPage extends StatefulWidget {
  final String title;
  final num? questionId;
  final num? taskId;
  final num? formId;

  const SubHeaderPage({
    super.key,
    required this.title,
    this.questionId,
    this.taskId,
    this.formId,
  });

  @override
  State<SubHeaderPage> createState() => _SubHeaderPageState();
}

class _SubHeaderPageState extends State<SubHeaderPage> {
  late TaskDetailsCubit _taskDetailsCubit;
  entities.Question? _question;
  List<entities.QuestionPart> _questionParts = [];
  List<entities.QuestionPart> _filteredQuestionParts = [];
  String? _errorMessage;
  Map<num, FormProgress> _questionPartProgress = {};
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _taskDetailsCubit = sl<TaskDetailsCubit>();
    _searchController.addListener(_onSearchChanged);
    _loadTaskData();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _taskDetailsCubit.close();
    super.dispose();
  }

  /// Load task data and extract question and question parts
  Future<void> _loadTaskData() async {
    if (widget.taskId == null) {
      setState(() {
        _errorMessage = 'Task ID is required';
      });
      return;
    }

    _taskDetailsCubit.getTaskDetail(widget.taskId!.toInt());
  }

  /// Extract question and question parts from task detail
  void _extractQuestionData(entities.TaskDetail taskDetail) {
    if (widget.formId == null || widget.questionId == null) {
      setState(() {
        _errorMessage = 'Form ID and Question ID are required';
      });
      return;
    }

    // Find the form
    final form = taskDetail.forms?.firstWhere(
      (f) => f.formId == widget.formId,
      orElse: () => throw StateError('Form not found'),
    );

    if (form == null) {
      setState(() {
        _errorMessage = 'Form not found';
      });
      return;
    }

    // Find the question
    final question = form.questions?.firstWhere(
      (q) => q.questionId == widget.questionId,
      orElse: () => throw StateError('Question not found'),
    );

    if (question == null) {
      setState(() {
        _errorMessage = 'Question not found';
      });
      return;
    }

    setState(() {
      _question = question;
      _questionParts = question.questionParts ?? [];
      _filteredQuestionParts = _questionParts;
      _errorMessage = null;
    });

    // Load progress for each question part
    _loadQuestionPartProgress();
  }

  /// Load progress for each question part using FormUtils
  Future<void> _loadQuestionPartProgress() async {
    if (_question == null ||
        widget.taskId == null ||
        widget.formId == null ||
        widget.questionId == null) {
      return;
    }

    final progressMap = <num, FormProgress>{};

    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId != null) {
        try {
          final progress = await FormUtils.getQPMDPageProgress(
            taskId: widget.taskId!,
            formId: widget.formId!,
            questionId: widget.questionId!,
            questionpartId: questionPart.questionpartId!,
          );
          progressMap[questionPart.questionpartId!] = progress;
        } catch (e) {
          // Log error and use default progress
          progressMap[questionPart.questionpartId!] = FormProgress();
        }
      }
    }

    setState(() {
      _questionPartProgress = progressMap;
    });
  }

  /// Check if a question is mandatory based on the complex logic provided
  bool _isQuestionMandatory(entities.Question question) {
    // Check for non-multi and non-comment questions
    if (question.isMulti != true && question.isComment != true) {
      // Loop through measurements to check if any validation is required
      if (question.measurements != null) {
        for (final measurement in question.measurements!) {
          if (measurement.measurementValidations != null) {
            for (final validation in measurement.measurementValidations!) {
              if (validation.required == true) {
                return true;
              }
            }
          }
        }
      }
    } else if (question.isMulti != true) {
      // Check if isSupplementaryQuestion is true and isOneOption is true
      final isSupplementaryQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId.toString() != "0" &&
          question.multiMeasurementId.toString().isNotEmpty;

      if (isSupplementaryQuestion) {
        // Check if isOneOption is true (any questionpart_id contains "-")
        if (question.questionParts != null) {
          for (final questionPart in question.questionParts!) {
            if (questionPart.questionpartId != null &&
                questionPart.questionpartId.toString().contains("-")) {
              return true;
            }
          }
        }
      }
    }

    return false;
  }

  /// Handle navigation to QPMD page and refresh state when returning
  Future<void> _handleQuestionPartTap(
      entities.QuestionPart questionPart) async {
    if (_question == null) return;

    await context.router.push(QPMDRoute(
      questionId: _question!.questionId,
      questionpartId: questionPart.questionpartId,
      taskId: widget.taskId,
      formId: widget.formId,
      itemTitle: questionPart.questionpartDescription,
    ));

    // Refresh the progress when returning from QPMD page
    if (mounted) {
      await _loadQuestionPartProgress();
    }
  }

  /// Handle search query changes
  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterQuestionParts();
    });
  }

  /// Filter question parts based on search query
  void _filterQuestionParts() {
    if (_searchQuery.isEmpty) {
      _filteredQuestionParts = _questionParts;
    } else {
      _filteredQuestionParts = _questionParts.where((part) {
        final description = part.questionpartDescription?.toLowerCase() ?? '';
        return description.contains(_searchQuery);
      }).toList();
    }
  }

  /// Handle barcode scan result
  void _handleScanResult(String scannedCode) {
    if (_questionParts.isEmpty) return;

    // Search for matching question parts
    final matchingParts = _questionParts.where((part) {
      final description = part.questionpartDescription?.toLowerCase() ?? '';
      final scannedCodeLower = scannedCode.toLowerCase();
      return description.contains(scannedCodeLower);
    }).toList();

    if (matchingParts.isNotEmpty && mounted) {
      // Show success message with count
      SnackBarService.success(
        context: context,
        message: matchingParts.length == 1
            ? 'Found and filtered: ${matchingParts.first.questionpartDescription}'
            : 'Found ${matchingParts.length} matching items',
      );
    } else if (mounted) {
      SnackBarService.info(
        context: context,
        message: 'No matching items found for: $scannedCode',
      );
    }
  }

  /// Check if all question parts are complete
  bool _areAllQuestionPartsComplete() {
    if (_question == null || _questionParts.isEmpty) return false;

    final isMandatory = _isQuestionMandatory(_question!);

    // If this is not a mandatory question, no need to check completion
    if (!isMandatory) return true;

    // Check each question part's progress
    for (final questionPart in _questionParts) {
      if (questionPart.questionpartId == null) continue;

      final progress = _questionPartProgress[questionPart.questionpartId!];
      if (progress == null) return false;

      // If there are visible items but not all are completed, return false
      if (progress.totalVisible > 0 &&
          progress.totalCompleted < progress.totalVisible) {
        return false;
      }
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey1,
      appBar: CustomAppBar(
        title: widget.title,
      ),
      body: BlocProvider(
        create: (context) => _taskDetailsCubit,
        child: BlocListener<TaskDetailsCubit, TaskDetailsState>(
          listener: (context, state) {
            if (state is TaskDetailsSuccess) {
              try {
                _extractQuestionData(state.taskDetail);
              } catch (e) {
                setState(() {
                  _errorMessage =
                      'Error extracting question data: ${e.toString()}';
                });
              }
            } else if (state is TaskDetailsError) {
              setState(() {
                _errorMessage = state.message;
              });
            }
          },
          child: BlocBuilder<TaskDetailsCubit, TaskDetailsState>(
            builder: (context, state) {
              if (state is TaskDetailsLoading) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (_errorMessage != null) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red.shade400,
                        ),
                        const Gap(16),
                        Text(
                          'Error',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const Gap(8),
                        Text(
                          _errorMessage!,
                          style: Theme.of(context).textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                        const Gap(16),
                        ElevatedButton(
                          onPressed: _loadTaskData,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                );
              }

              if (_question == null) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              final textTheme = Theme.of(context).textTheme;
              final isMandatory = _isQuestionMandatory(_question!);

              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 90),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Search bar with barcode scanner
                      SearchBarWithScanner(
                        controller: _searchController,
                        hintText: 'Search items...',
                        onScanResult: _handleScanResult,
                      ),
                      // const Gap(8),
                      _filteredQuestionParts.isEmpty
                          ? Center(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Text(
                                  _searchQuery.isNotEmpty
                                      ? 'No items match your search'
                                      : 'No items available for this section',
                                  style: textTheme.bodyLarge,
                                ),
                              ),
                            )
                          : ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              itemCount: _filteredQuestionParts.length,
                              itemBuilder: (context, index) {
                                final part = _filteredQuestionParts[index];
                                final progress = _questionPartProgress[
                                        part.questionpartId] ??
                                    FormProgress();

                                return SubHeaderCard(
                                  questionPart: part,
                                  question: _question!,
                                  isMandatory: isMandatory,
                                  taskId: widget.taskId,
                                  formId: widget.formId,
                                  totalVisible: progress.totalVisible,
                                  totalCompleted: progress.totalCompleted,
                                  onTap: () => _handleQuestionPartTap(part),
                                );
                              },
                              separatorBuilder: (_, __) => const Gap(8),
                            ),
                      const Gap(8),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: _question?.hasSignature == true
          ? Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                width: double.infinity,
                child: AppButton(
                  text: 'Add signature',
                  color: AppColors.primaryBlue,
                  onPressed: () {
                    if (_areAllQuestionPartsComplete()) {
                      context.router.push(SignatureRoute(
                        questionId: widget.questionId,
                        taskId: widget.taskId,
                        formId: widget.formId,
                        title: '${widget.title} - signature',
                      ));
                    } else {
                      SnackBarService.error(
                        context: context,
                        message:
                            'Please complete all required items before adding signature',
                      );
                    }
                  },
                ),
              ),
            )
          : null,
    );
  }
}
