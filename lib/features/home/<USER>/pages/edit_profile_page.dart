import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/services/camera_service.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/image_storage_utils.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/offline_image_widget.dart';
import '../blocs/edit_profile/edit_profile_cubit.dart';
import '../blocs/profile/profile_cubit.dart';
import '../../domain/entities/profile_response_entity.dart';
import '../../domain/entities/country_response_entity.dart' as country_entities;
import '../../domain/entities/state_response_entity.dart' as state_entities;
import '../../domain/usecases/get_country_usecase.dart';
import '../../domain/usecases/get_state_usecase.dart';
import '../../data/models/update_profile_request.dart';

@RoutePage()
class EditProfilePage extends StatelessWidget {
  final ProfileResponseEntity? profileData;

  const EditProfilePage({
    super.key,
    this.profileData,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<EditProfileCubit>(),
      child: _EditProfileView(profileData: profileData),
    );
  }
}

class _EditProfileView extends StatefulWidget {
  final ProfileResponseEntity? profileData;

  const _EditProfileView({this.profileData});

  @override
  State<_EditProfileView> createState() => _EditProfileViewState();
}

class _EditProfileViewState extends State<_EditProfileView> {
  // Form controllers
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _suburbController = TextEditingController();
  final TextEditingController _postcodeController = TextEditingController();
  final TextEditingController _deliveryController = TextEditingController();
  final TextEditingController _postalAddressController =
      TextEditingController();
  final TextEditingController _postalSuburbController = TextEditingController();
  final TextEditingController _postalPostcodeController =
      TextEditingController();

  // Dropdown values
  String? _selectedCountry;
  String? _selectedState;
  String? _selectedPostalCountry;
  String? _selectedPostalState;

  // Checkbox state
  bool _postalSameAsHome = false;

  // Profile data
  ProfileResponseEntity? _profileData;

  // Cached country and state data
  List<country_entities.Country> _countries = [];
  List<state_entities.State> _states = [];
  bool _isLoadingData = false;

  // Photo handling
  File? _selectedImageFile;
  bool _hasImageChanged = false;
  bool _isProcessingImage = false;
  late final CameraService _cameraService;

  // Form key
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _cameraService = sl<CameraService>();

    // Load cached country and state data
    _loadCachedData();

    // If profile data is passed, use it directly, otherwise load from API
    if (widget.profileData != null) {
      _prefillFormData(widget.profileData!);
    } else {
      _loadProfileData();
    }
  }

  /// Loads cached country and state data from database
  Future<void> _loadCachedData() async {
    setState(() {
      _isLoadingData = true;
    });

    try {
      // Load cached countries from database (no API call needed)
      final countryResult = await sl<GetCountryUseCase>().call(
        token: "", // Not needed for cached data
        userId: "", // Not needed for cached data
        isSync: false, // Get from cache only
      );

      // Load cached states from database (no API call needed)
      final stateResult = await sl<GetStateUseCase>().call(
        token: "", // Not needed for cached data
        userId: "", // Not needed for cached data
        isSync: false, // Get from cache only
      );

      if (countryResult.isSuccess && stateResult.isSuccess) {
        setState(() {
          _countries = countryResult.data?.toEntity().data.countries ?? [];
          _states = stateResult.data?.toEntity().data.states ?? [];
          _isLoadingData = false;
        });
      } else {
        setState(() {
          _countries = [];
          _states = [];
          _isLoadingData = false;
        });
      }
    } catch (e) {
      setState(() {
        _countries = [];
        _states = [];
        _isLoadingData = false;
      });
    }
  }

  /// Loads the current user's profile data from storage and triggers profile loading
  Future<void> _loadProfileData() async {
    final dataManager = sl<DataManager>();
    final token = await dataManager.getAuthToken();
    final userId = await dataManager.getUserId();

    if (token != null && userId != null) {
      if (mounted) {
        context.read<EditProfileCubit>().loadProfile(
              token: token,
              userId: userId,
            );
      }
    }
  }

  /// Reloads profile data through ProfileCubit with sync enabled after successful update
  Future<void> _reloadProfileData() async {
    final dataManager = sl<DataManager>();
    final token = await dataManager.getAuthToken();
    final userId = await dataManager.getUserId();

    if (token != null && userId != null && mounted) {
      // Trigger ProfileCubit to reload data with isSync: true to refresh cached data
      context.read<ProfileCubit>().loadProfile(
            token: token,
            userId: userId,
            isSync: true,
          );
      // Navigate back to profile page after triggering reload
      if (mounted) {
        context.router.maybePop();
      }
    }
  }

  /// Pre-fills form fields with existing profile data
  void _prefillFormData(ProfileResponseEntity profileResponse) {
    final data = profileResponse.data;
    if (data != null) {
      setState(() {
        _profileData = profileResponse;

        // Prefill basic information
        _mobileController.text = data.mobile ?? '';
        _addressController.text = data.address ?? '';
        _suburbController.text = data.suburb ?? '';
        _postcodeController.text = data.postcode ?? '';
        _deliveryController.text = data.pDeliveryComment ?? '';

        // Prefill postal address
        _postalAddressController.text = data.pAddress ?? '';
        _postalSuburbController.text = data.pSuburb ?? '';
        _postalPostcodeController.text = data.pPostcode ?? '';

        // Set dropdown selections - handle empty/null values
        _selectedCountry =
            _isValidDropdownValue(data.country) ? data.country : null;
        _selectedState = _isValidDropdownValue(data.state) ? data.state : null;
        _selectedPostalCountry =
            _isValidDropdownValue(data.pCountry) ? data.pCountry : null;
        _selectedPostalState =
            _isValidDropdownValue(data.pRegion) ? data.pRegion : null;
      });
    }
  }

  /// Helper method to check if a dropdown value is valid (not empty, null, or "0")
  bool _isValidDropdownValue(String? value) {
    return value != null && value.isNotEmpty && value != "0";
  }

  /// Handles the "postal same as home" checkbox toggle
  void _handlePostalSameAsHomeToggle(bool? value) {
    setState(() {
      _postalSameAsHome = value ?? false;

      if (_postalSameAsHome) {
        // Copy home address to postal address
        _postalAddressController.text = _addressController.text;
        _postalSuburbController.text = _suburbController.text;
        _postalPostcodeController.text = _postcodeController.text;
        _selectedPostalCountry = _selectedCountry;
        _selectedPostalState = _selectedState;
      } else {
        // Clear postal address fields
        _postalAddressController.clear();
        _postalSuburbController.clear();
        _postalPostcodeController.clear();
        _selectedPostalCountry = null;
        _selectedPostalState = null;
      }
    });
  }

  /// Shows image source selection dialog (camera or gallery)
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      useRootNavigator: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.blackTint2,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Gap(16),
                Text('Change profile photo',
                    style: Theme.of(context)
                        .textTheme
                        .montserratNavigationPrimaryMedium),
                const Gap(24),
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.camera_alt,
                        color: AppColors.primaryBlue, size: 20),
                  ),
                  title: Text('Take Photo',
                      style: Theme.of(context)
                          .textTheme
                          .montserratTitleExtraSmall),
                  subtitle: Text(
                    'Use camera to capture a new photo',
                    style: Theme.of(context).textTheme.montserratTableSmall,
                  ),
                  onTap: () {
                    context.router.maybePop();
                    _selectImage(ImageSource.camera);
                  },
                ),
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.photo_library,
                        color: Colors.green, size: 20),
                  ),
                  title: Text('Choose from Gallery',
                      style: Theme.of(context)
                          .textTheme
                          .montserratTitleExtraSmall),
                  subtitle: Text(
                    'Select an existing photo from gallery',
                    style: Theme.of(context).textTheme.montserratTableSmall,
                  ),
                  onTap: () {
                    context.router.maybePop();
                    _selectImage(ImageSource.gallery);
                  },
                ),
                const Gap(16),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Handles image selection from camera or gallery
  Future<void> _selectImage(ImageSource source) async {
    try {
      setState(() {
        _isProcessingImage = true;
      });

      final File? imageFile = await _cameraService.pickImage(
        source: source,
        imageQuality: 85,
      );

      if (imageFile != null) {
        setState(() {
          _selectedImageFile = imageFile;
          _hasImageChanged = true;
          _isProcessingImage = false;
        });

        if (mounted) {
          SnackBarService.success(
            context: context,
            message: 'Photo selected successfully!',
          );
        }
      } else {
        setState(() {
          _isProcessingImage = false;
        });
      }
    } catch (e) {
      setState(() {
        _isProcessingImage = false;
      });
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to select photo: ${e.toString()}',
        );
      }
    }
  }

  /// Saves the profile changes
  Future<void> _saveProfileChanges() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        final dataManager = sl<DataManager>();
        final token = await dataManager.getAuthToken();
        final userId = await dataManager.getUserId();

        if (token == null || userId == null) {
          SnackBarService.error(
            context: context,
            message: 'Authentication error. Please login again.',
          );
          return;
        }

        // Helper function to get country ID from country name
        String getCountryId(String? country) {
          if (!_isValidDropdownValue(country)) {
            return '0'; // Return 0 for null/empty values
          }

          final matchingCountry = _countries.firstWhere(
            (c) => c.country == country,
            orElse: () =>
                country_entities.Country(country: 'Australia', countryId: 14),
          );
          return matchingCountry.countryId.toString();
        }

        // Helper function to get state ID from state name
        String getStateId(String? state) {
          if (!_isValidDropdownValue(state)) {
            return '0'; // Return 0 for null/empty values
          }

          final matchingState = _states.firstWhere(
            (s) => s.state == state,
            orElse: () => state_entities.State(
                state: 'NSW', stateId: 4, country: 'Australia', countryId: 14),
          );
          return matchingState.stateId.toString();
        }

        // Handle image encoding if photo was changed
        String? pictureBlob;
        if (_hasImageChanged && _selectedImageFile != null) {
          try {
            pictureBlob = await ImageStorageUtils.encodeFileToBase64(
                _selectedImageFile!.path);
          } catch (e) {
            if (mounted) {
              SnackBarService.error(
                context: context,
                message: 'Error processing image: ${e.toString()}',
              );
            }
            return;
          }
        }

        final request = UpdateProfileRequest(
          userId: userId,
          token: token,
          address: _addressController.text.trim(),
          countryId: getCountryId(_selectedCountry),
          country:
              _isValidDropdownValue(_selectedCountry) ? _selectedCountry! : '',
          stateId: getStateId(_selectedState),
          state: _isValidDropdownValue(_selectedState) ? _selectedState! : '',
          suburb: _suburbController.text.trim(),
          postcode: _postcodeController.text.trim(),
          pAddress: _postalAddressController.text.trim(),
          pCountryId: getCountryId(_selectedPostalCountry),
          pCountry: _isValidDropdownValue(_selectedPostalCountry)
              ? _selectedPostalCountry!
              : '',
          pSuburb: _postalSuburbController.text.trim(),
          pPostcode: _postalPostcodeController.text.trim(),
          pRegion: _isValidDropdownValue(_selectedPostalState)
              ? _selectedPostalState!
              : '',
          pDeliveryComment: _deliveryController.text.trim(),
          mobile: _mobileController.text.trim(),
          deviceLatitude: 1.0, // Default values as per the example
          deviceLongitude: 1.0,
          pictureBlob: pictureBlob,
          pictureUpdate: _hasImageChanged,
        );

        if (mounted) {
          context.read<EditProfileCubit>().updateProfile(request: request);
        }
      } catch (e) {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Error preparing profile update: ${e.toString()}',
          );
        }
      }
    }
  }

  @override
  void dispose() {
    // Dispose all controllers to prevent memory leaks
    _mobileController.dispose();
    _addressController.dispose();
    _suburbController.dispose();
    _postcodeController.dispose();
    _deliveryController.dispose();
    _postalAddressController.dispose();
    _postalSuburbController.dispose();
    _postalPostcodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<EditProfileCubit, EditProfileState>(
      listener: (context, state) {
        if (state is EditProfileLoaded) {
          _prefillFormData(state.profileResponse);
        } else if (state is EditProfileUpdateSuccess) {
          SnackBarService.success(
            context: context,
            message: 'Profile updated successfully!',
          );
          // Reload profile data with sync to refresh cached data
          _reloadProfileData();
        } else if (state is EditProfileError) {
          ConfirmDialog.show(
            context: context,
            title: 'Profile Update Error',
            message: state.message,
            confirmText: 'OK',
            onConfirm: () {},
          );
        }
      },
      child: BlocBuilder<EditProfileCubit, EditProfileState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: AppColors.lightGrey2,
            appBar: CustomAppBar(
              title: 'Edit Profile',
              actions: state is! EditProfileLoading
                  ? [
                      IconButton(
                        onPressed: state is EditProfileUpdating
                            ? null
                            : () => _saveProfileChanges(),
                        icon: state is EditProfileUpdating
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: AppColors.primaryBlue,
                                ),
                              )
                            : const Icon(
                                Icons.check,
                                color: AppColors.primaryBlue,
                              ),
                      ),
                    ]
                  : null,
            ),
            body: _buildBody(state),
          );
        },
      ),
    );
  }

  Widget _buildBody(EditProfileState state) {
    if (state is EditProfileLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryBlue,
        ),
      );
    }

    return Stack(
      children: [
        Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Header Card
                _buildHeaderCard(),
                const Gap(16),

                // Personal Information Card
                _buildSectionCard(
                  title: 'Personal Information',
                  icon: Icons.person_outline,
                  children: [
                    _buildPersonalInfoFields(_profileData?.data),
                  ],
                ),
                const Gap(16),

                // Address Information Card
                _buildSectionCard(
                  title: 'Address Information',
                  icon: Icons.location_on_outlined,
                  children: [
                    _buildAddressInfoFields(),
                  ],
                ),
                const Gap(16),

                // Delivery Instructions Card
                _buildSectionCard(
                  title: 'Delivery Instructions',
                  icon: Icons.comment_outlined,
                  children: [
                    _buildDeliveryInstructionsField(),
                  ],
                ),
                const Gap(16),

                // Postal Address Card
                _buildSectionCard(
                  title: 'Postal Address',
                  icon: Icons.markunread_mailbox_outlined,
                  children: [
                    _buildPostalAddressFields(),
                  ],
                ),
                const Gap(16), // Reduced from 24 to 16 since no save button
              ],
            ),
          ),
        ),

        // Loading overlay when updating
        if (state is EditProfileUpdating)
          Container(
            color: AppColors.black.withValues(alpha: 0.3),
            child: const Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryBlue,
              ),
            ),
          ),
      ],
    );
  }

  /// Builds the header card with profile information
  Widget _buildHeaderCard() {
    final data = _profileData?.data;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Square profile image with edit functionality
          GestureDetector(
            onTap: _isProcessingImage ? null : _showImageSourceDialog,
            child: Stack(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.borderColor,
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(7),
                    child: _buildProfileImage(data),
                  ),
                ),
                // Edit icon overlay
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      _isProcessingImage
                          ? Icons.hourglass_empty
                          : Icons.camera_alt,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Gap(16),
          Text(
            '${data?.firstName ?? 'John'} ${data?.lastName ?? 'Doe'}',
            style: AppTypography.montserratSemiBold.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: AppColors.black,
            ),
          ),
          const Gap(4),
          Text(
            data?.email ?? '<EMAIL>',
            style: AppTypography.montserratParagraphSmall.copyWith(
              color: AppColors.blackTint1,
              fontSize: 14,
            ),
          ),
          const Gap(12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.2)),
            ),
            child: Text(
              'Employee ID: ${data?.contractorId?.toString() ?? '123456789'}',
              style: AppTypography.montserratTitleXxsmall.copyWith(
                color: AppColors.primaryBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const Gap(4),
        ],
      ),
    );
  }

  /// Builds a section card with title and content
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.primaryBlue,
                size: 20,
              ),
              const Gap(12),
              Text(
                title,
                style: AppTypography.montserratHeadingMedium.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          const Gap(20),
          ...children,
        ],
      ),
    );
  }

  /// Builds the profile image widget
  Widget _buildProfileImage(dynamic data) {
    // Show selected image if user has changed it
    if (_hasImageChanged && _selectedImageFile != null) {
      return Image.file(
        _selectedImageFile!,
        width: 80,
        height: 80,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultSquareAvatar();
        },
      );
    }

    // Show existing profile image or default
    if (data?.profileImageUrl?.isNotEmpty == true) {
      return OfflineImageWidget(
        url: _addCacheBuster(data!.profileImageUrl!),
        width: 80,
        height: 80,
        fit: BoxFit.cover,
      );
    }

    return _buildDefaultSquareAvatar();
  }

  /// Builds the default square avatar
  Widget _buildDefaultSquareAvatar() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: AppColors.lightGrey2,
        borderRadius: BorderRadius.circular(7),
      ),
      child: const Icon(
        Icons.person_rounded,
        size: 40,
        color: AppColors.blackTint1,
      ),
    );
  }

  String _addCacheBuster(String url) {
    if (url.isEmpty) return url;
    final separator = url.contains('?') ? '&' : '?';
    return '$url${separator}v=${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Builds personal information fields
  Widget _buildPersonalInfoFields(dynamic data) {
    return Column(
      children: [
        _buildModernTextField(
          label: 'Mobile Number',
          controller: _mobileController,
          keyboardType: TextInputType.phone,
          hintText: 'Enter your mobile number',
          prefixIcon: Icons.phone_outlined,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Mobile number is required';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Builds address information fields
  Widget _buildAddressInfoFields() {
    return Column(
      children: [
        _buildModernTextField(
          label: 'Address',
          controller: _addressController,
          hintText: 'Enter your street address',
          prefixIcon: Icons.location_on_outlined,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Address is required';
            }
            return null;
          },
        ),
        const Gap(20),

        // Country and State row
        Row(
          children: [
            Expanded(
              child: _buildModernDropdownField(
                label: 'Country',
                value: _selectedCountry,
                items: _isLoadingData
                    ? []
                    : _countries.map((c) => c.country).toList(),
                onChanged: (value) => setState(() {
                  _selectedCountry = value;
                  // Clear state if it's not valid for the selected country
                  if (_selectedState != null &&
                      !_states.any((s) =>
                          s.country == value && s.state == _selectedState)) {
                    _selectedState = null;
                  }
                }),
                prefixIcon: Icons.public_outlined,
                validator: (value) {
                  if (value == null) {
                    return 'Country is required';
                  }
                  return null;
                },
              ),
            ),
            const Gap(16),
            Expanded(
              child: _buildModernDropdownField(
                label: 'State',
                value: _selectedState,
                items: _isLoadingData
                    ? []
                    : _states
                        .where((s) => s.country == _selectedCountry)
                        .map((s) => s.state)
                        .toList(),
                onChanged: (value) => setState(() => _selectedState = value),
                prefixIcon: Icons.map_outlined,
                validator: (value) {
                  if (value == null) {
                    return 'State is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const Gap(20),

        // Suburb and Postcode row
        Row(
          children: [
            Expanded(
              child: _buildModernTextField(
                label: 'Suburb',
                controller: _suburbController,
                hintText: 'Enter suburb',
                prefixIcon: Icons.home_work_outlined,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Suburb is required';
                  }
                  return null;
                },
              ),
            ),
            const Gap(16),
            Expanded(
              child: _buildModernTextField(
                label: 'Postcode',
                controller: _postcodeController,
                keyboardType: TextInputType.number,
                hintText: 'Enter postcode',
                prefixIcon: Icons.local_post_office_outlined,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'Postcode is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds delivery instructions field
  Widget _buildDeliveryInstructionsField() {
    return _buildModernTextField(
      label: 'Special Instructions',
      controller: _deliveryController,
      hintText: 'Enter any special delivery instructions',
      prefixIcon: Icons.note_add_outlined,
      maxLines: 3,
    );
  }

  /// Builds postal address fields
  Widget _buildPostalAddressFields() {
    return Column(
      children: [
        // Modern checkbox
        _buildModernCheckbox(
          title: 'Postal Address Same As Home Address',
          value: _postalSameAsHome,
          onChanged: _handlePostalSameAsHomeToggle,
        ),

        // Postal address fields (show only if not same as home)
        if (!_postalSameAsHome) ...[
          const Gap(24),
          _buildModernTextField(
            label: 'Postal Address',
            controller: _postalAddressController,
            hintText: 'Enter postal address',
            prefixIcon: Icons.markunread_mailbox_outlined,
          ),
          const Gap(20),

          // Postal Country and State row
          Row(
            children: [
              Expanded(
                child: _buildModernDropdownField(
                  label: 'Postal Country',
                  value: _selectedPostalCountry,
                  items: _isLoadingData
                      ? []
                      : _countries.map((c) => c.country).toList(),
                  onChanged: (value) => setState(() {
                    _selectedPostalCountry = value;
                    // Clear postal state if it's not valid for the selected postal country
                    if (_selectedPostalState != null &&
                        !_states.any((s) =>
                            s.country == value &&
                            s.state == _selectedPostalState)) {
                      _selectedPostalState = null;
                    }
                  }),
                  prefixIcon: Icons.public_outlined,
                ),
              ),
              const Gap(16),
              Expanded(
                child: _buildModernDropdownField(
                  label: 'Postal State',
                  value: _selectedPostalState,
                  items: _isLoadingData
                      ? []
                      : _states
                          .where((s) => s.country == _selectedPostalCountry)
                          .map((s) => s.state)
                          .toList(),
                  onChanged: (value) =>
                      setState(() => _selectedPostalState = value),
                  prefixIcon: Icons.map_outlined,
                ),
              ),
            ],
          ),
          const Gap(20),

          // Postal Suburb and Postcode row
          Row(
            children: [
              Expanded(
                child: _buildModernTextField(
                  label: 'Postal Suburb',
                  controller: _postalSuburbController,
                  hintText: 'Enter postal suburb',
                  prefixIcon: Icons.home_work_outlined,
                ),
              ),
              const Gap(16),
              Expanded(
                child: _buildModernTextField(
                  label: 'Postal Postcode',
                  controller: _postalPostcodeController,
                  keyboardType: TextInputType.number,
                  hintText: 'Enter postal postcode',
                  prefixIcon: Icons.local_post_office_outlined,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Builds a text field with traditional styling
  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    String? hintText,
    IconData? prefixIcon,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.montserratTitleSmall,
        ),
        const Gap(8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.blackTint1,
                    size: 20,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.primaryBlue,
                width: 1.5,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1.5),
            ),
            filled: true,
            fillColor: AppColors.lightGrey1,
            contentPadding: EdgeInsets.symmetric(
              horizontal: prefixIcon != null ? 12.0 : 16.0,
              vertical: maxLines > 1 ? 16.0 : 14.0,
            ),
            hintStyle: AppTypography.montserratParagraphSmall.copyWith(
              color: AppColors.blackTint1,
            ),
          ),
          style: AppTypography.montserratFormsfield,
        ),
      ],
    );
  }

  /// Builds a dropdown field with traditional styling
  Widget _buildModernDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required void Function(String?) onChanged,
    IconData? prefixIcon,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.montserratTitleSmall,
        ),
        const Gap(8),
        DropdownButtonFormField<String>(
          value: items.contains(value)
              ? value
              : null, // Only set value if it exists in items
          validator: validator,
          decoration: InputDecoration(
            hintText: 'Select...',
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.blackTint1,
                    size: 20,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.primaryBlue,
                width: 1.5,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1.5),
            ),
            filled: true,
            fillColor: AppColors.lightGrey1,
            contentPadding: EdgeInsets.symmetric(
              horizontal: prefixIcon != null ? 12.0 : 16.0,
              vertical: 14.0,
            ),
          ),
          isExpanded: true,
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(
                item,
                style: AppTypography.montserratFormsfield,
              ),
            );
          }).toList(),
          onChanged: onChanged,
          icon: const Icon(
            Icons.keyboard_arrow_down_rounded,
            color: AppColors.blackTint1,
          ),
        ),
      ],
    );
  }

  /// Builds a checkbox widget with traditional styling
  Widget _buildModernCheckbox({
    required String title,
    required bool value,
    required void Function(bool?) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: value ? AppColors.lightGrey1 : AppColors.lightGrey1,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: value ? AppColors.primaryBlue : AppColors.borderColor,
          width: 1,
        ),
      ),
      child: GestureDetector(
        onTap: () => onChanged(!value),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: value ? AppColors.primaryBlue : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: value ? AppColors.primaryBlue : AppColors.blackTint2,
                  width: 2,
                ),
              ),
              child: value
                  ? const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    )
                  : null,
            ),
            const Gap(12),
            Expanded(
              child: Text(
                title,
                style: AppTypography.montserratTitleSmall,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
