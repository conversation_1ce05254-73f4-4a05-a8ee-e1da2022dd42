import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart' hide Form;
import 'package:storetrack_app/core/services/app_info_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/core/utils/time_access_validator.dart';
import 'package:storetrack_app/core/utils/task_schedule_validator.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/blocs/schedule/schedule_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/schedule/schedule_state.dart';

import '../../../../core/storage/data_manager.dart';
import '../../domain/entities/tasks_response_entity.dart';
import '../../../home/<USER>/widgets/calendar_bottom_sheet.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'package:gap/gap.dart';
import '../widgets/all_tasks_list.dart';
import '../widgets/week_tasks_list.dart';
import '../widgets/overdue_tasks_list.dart';
import '../widgets/reschedule_floating_buttons.dart';
import '../widgets/view_mode_toggle.dart';
import '../widgets/list_toggle_button.dart';
import '../widgets/calendar_view_toggle.dart';
import '../widgets/week_calendar_view.dart';
import '../widgets/month_calendar_view.dart';
import '../../../../config/routes/app_router.gr.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';

@RoutePage()
class SchedulePage extends StatefulWidget {
  const SchedulePage({super.key});

  @override
  State<SchedulePage> createState() => _SchedulePageState();
}

class _SchedulePageState extends State<SchedulePage>
    with SingleTickerProviderStateMixin {
  _SchedulePageState(); // Add an unnamed constructor
  late TabController _tabController;
  bool isWeekView = true;
  late DateTime selectedDate; // Using late to initialize in initState
  DateTime?
      selectedCalendarDate; // This will track which date is selected in the calendar
  List<DateTime> weekDays = [];
  final DateTime _currentMonth = DateTime.now();
  List<DateTime> monthDays = [];

  // Track which button is currently selected
  String _selectedButton =
      'all'; // Options: 'all', 'this_week', 'next_week', 'overdue'

  // Track view mode (List vs Calendar)
  ViewMode _currentViewMode = ViewMode.list;

  // Track calendar view mode (Week vs Month) when in calendar mode
  CalendarViewMode _calendarViewMode = CalendarViewMode.week;

  // Task data
  String actualDeviceUid = '';
  late String actualUserId;
  late String actualAppVersion;
  final List<String> actualTasksToSchedule = [];
  late String actualUserToken;

  List<TaskDetail> scheduledTasks = [];
  bool _isCheckboxMode = false;
  List<TaskDetail> selectedItems = [];
  bool _areAllItemsSelected = false;

  // Store all tasks from the API response
  List<schedule.TaskDetail> allApiTasks = [];

  // Map to track which dates have tasks
  Map<String, bool> datesWithTasks = {};

  // Convert schedule.Datum to unscheduled Datum
  TaskDetail convertScheduleToDatum(schedule.TaskDetail scheduleTask) {
    return scheduleTask;
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this, initialIndex: 0);
    _tabController.addListener(_handleTabChange);

    // Set initial selected button based on tab
    _selectedButton = _tabController.index == 0
        ? 'this_week'
        : _tabController.index == 1
            ? 'next_week'
            : _tabController.index == 2
                ? 'overdue'
                : 'all';

    // Initialize selectedDate but don't select current date
    selectedDate = DateTime
        .now(); // We need to initialize it, but won't mark it as selected in the calendar

    // Initialize the week and month days
    _generateWeekDays();
    _generateMonthDays();

    // Initialize data
    _initializeData();
  }

  void _generateWeekDays() {
    // Get the start of the week (Monday)
    final DateTime now = selectedDate;
    final int day = now.weekday;
    final DateTime firstDayOfWeek = now.subtract(Duration(days: day - 1));

    weekDays = List.generate(7, (index) {
      return firstDayOfWeek.add(Duration(days: index));
    });

    // Check if today is in this week, if so select it
    final today = DateTime.now();
    final bool isTodayInWeek = weekDays.any((day) =>
        day.day == today.day &&
        day.month == today.month &&
        day.year == today.year);

    if (isTodayInWeek) {
      // Select today
      selectedDate = today;
    }
  }

  void _generateMonthDays() {
    // Get the first day of the month
    final DateTime firstDayOfMonth =
        DateTime(_currentMonth.year, _currentMonth.month, 1);

    // Get the weekday of the first day (0 = Monday, 6 = Sunday)
    final int firstWeekday = firstDayOfMonth.weekday - 1;

    // Calculate days from previous month to show
    final DateTime firstDayToShow =
        firstDayOfMonth.subtract(Duration(days: firstWeekday));

    // Calculate total days to show (6 weeks = 42 days)
    monthDays = List.generate(42, (index) {
      return firstDayToShow.add(Duration(days: index));
    });
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Update the selected button based on tab index
        if (_tabController.index == 0) {
          _selectedButton = 'this_week';
          isWeekView = true;

          // When switching to this week view, use today's date for filtering
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 1) {
          _selectedButton = 'next_week';
          isWeekView = true;

          // When switching to next week view, use next week's date for filtering
          final DateTime nextWeekDate =
              DateTime.now().add(const Duration(days: 7));
          selectedDate = nextWeekDate;
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 2) {
          _selectedButton = 'overdue';
          isWeekView = false;
        } else if (_tabController.index == 3) {
          _selectedButton = 'all';
          isWeekView = true;

          // When switching to all view, we don't need to filter by date
          // as we'll show all scheduled tasks
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
        }

        // For tabs other than "All", filter tasks for the selected date
        // For "All" tab, we'll show all scheduled tasks in _buildAllTasksListContent
        _updateScheduledTasksForView();
      });
    }
  }

  void _updateScheduledTasksForView() {
    List<schedule.TaskDetail> tasksToDisplay = [];

    final DateTime today =
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);

    if (_selectedButton == 'all') {
      tasksToDisplay = allApiTasks
          .where((task) =>
              task.taskStatus == "Confirmed" &&
              task.isOpen == false &&
              task.scheduledTimeStamp != null)
          .toList();
    } else if (_selectedButton == 'this_week') {
      final weekDates =
          weekDays.map((d) => DateTime(d.year, d.month, d.day)).toSet();
      tasksToDisplay = allApiTasks.where((task) {
        if (task.taskStatus != "Confirmed" ||
            task.isOpen == true ||
            task.scheduledTimeStamp == null) {
          return false;
        }
        final taskDate = DateTime(task.scheduledTimeStamp!.year,
            task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);
        return weekDates.contains(taskDate);
      }).toList();
    } else if (_selectedButton == 'next_week') {
      final weekDates =
          weekDays.map((d) => DateTime(d.year, d.month, d.day)).toSet();
      tasksToDisplay = allApiTasks.where((task) {
        if (task.taskStatus != "Confirmed" ||
            task.isOpen == true ||
            task.scheduledTimeStamp == null) {
          return false;
        }
        final taskDate = DateTime(task.scheduledTimeStamp!.year,
            task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);
        return weekDates.contains(taskDate);
      }).toList();
    } else if (_selectedButton == 'overdue') {
      tasksToDisplay = allApiTasks
          .where((task) =>
              task.taskStatus == "Confirmed" &&
              task.isOpen == false &&
              task.scheduledTimeStamp != null &&
              task.scheduledTimeStamp!.isBefore(today))
          .toList();
    }
    setState(() {
      scheduledTasks =
          tasksToDisplay.map((task) => convertScheduleToDatum(task)).toList();
    });
  }

  Future<void> _initializeData() async {
    _refreshData();
  }

  Future<void> _refreshData() async {
    try {
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();
      actualAppVersion = await sl<AppInfoService>().getVersion();

      if (mounted) {
        context.read<ScheduleTaskCubit>().getData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  // Open route map using StoreTrack web service
  Future<void> _openRouteMap() async {
    try {
      // Get current location
      final locationService = sl<LocationService>();
      final currentLocation = await locationService.getCurrentPosition();

      if (!mounted) return;

      if (currentLocation == null) {
        SnackBarService.warning(
          context: context,
          message:
              'Unable to get current location. Please enable location services.',
        );
        return;
      }

      // Get user ID
      final userID = await sl<DataManager>().getUserId() ?? "0";

      // Construct current location string
      final currentLatLng =
          "${currentLocation.latitude},${currentLocation.longitude}";

      // Construct the StoreTrack route URL
      final routeUrl =
          "https://webservice2.storetrack.com.au/standalone/route.aspx?pid=$userID&latlong=$currentLatLng";

      // Navigate to web browser with the route URL
      context.router.push(WebBrowserRoute(url: routeUrl, title: 'Route Map'));
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error opening route map: ${e.toString()}',
        );
      }
    }
  }

  PreferredSizeWidget _buildViewToggleButtons() {
    return PreferredSize(
      preferredSize:
          Size.fromHeight(_currentViewMode == ViewMode.list ? 108 : 48),
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            // Top row: List button on left, Week/Month toggle on right
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // List toggle button
                  ListToggleButton(
                    isSelected: _currentViewMode == ViewMode.list,
                    onPressed: () {
                      setState(() {
                        _currentViewMode = ViewMode.list;
                      });
                    },
                  ),

                  const Spacer(),

                  // Calendar view toggle (Week/Month)
                  CalendarViewToggle(
                    currentMode: _currentViewMode == ViewMode.list
                        ? null
                        : _calendarViewMode,
                    onModeChanged: (CalendarViewMode mode) {
                      setState(() {
                        _currentViewMode = ViewMode.calendar;
                        _calendarViewMode = mode;
                      });
                    },
                  ),
                ],
              ),
            ),

            // Tab buttons (only shown in List mode)
            if (_currentViewMode == ViewMode.list)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    // Tab bar with All, This Week, Next Week, and Overdue buttons
                    Expanded(
                      child: Container(
                        height: 44,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(8),
                          // border: Border.all(
                          //   color: const Color(0xFFE0E0E0),
                          //   width: 1,
                          // ),
                        ),
                        child: Row(
                          children: [
                            // This Week button
                            _buildToggleButton(
                              'This weeks',
                              'this_week',
                              const BorderRadius.only(
                                topLeft: Radius.circular(8),
                                bottomLeft: Radius.circular(8),
                              ),
                            ),
                            // Next Week button
                            _buildToggleButton(
                              'Next weeks',
                              'next_week',
                              BorderRadius.zero,
                            ),
                            // Overdue button
                            _buildToggleButton(
                              'Overdue',
                              'overdue',
                              BorderRadius.zero,
                            ),
                            // All button
                            _buildToggleButton(
                              'All',
                              'all',
                              const BorderRadius.only(
                                topRight: Radius.circular(8),
                                bottomRight: Radius.circular(8),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleButton(
    String text,
    String buttonKey,
    BorderRadius borderRadius,
  ) {
    final bool isSelected = _selectedButton == buttonKey;

    return Expanded(
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedButton = buttonKey;
            if (buttonKey == 'this_week') {
              _tabController.animateTo(0);
            } else if (buttonKey == 'next_week') {
              _tabController.animateTo(1);
            } else if (buttonKey == 'overdue') {
              _tabController.animateTo(2);
            } else if (buttonKey == 'all') {
              _tabController.animateTo(3);
            }
          });
        },
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isSelected ? AppColors.primaryBlue : const Color(0xFFF5F5F5),
          foregroundColor: isSelected ? Colors.white : AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
            side: const BorderSide(
              color: Colors.grey,
              width: 0,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          text,
          style: Theme.of(context)
              .textTheme
              .montserratNavigationPrimaryMedium
              .copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
        ),
      ),
    );
  }

  // Helper method to check if a task is scheduled for a specific date
  bool _isTaskScheduledForDate(schedule.TaskDetail task, DateTime date) {
    // Check if the task has a scheduled timestamp
    if (task.scheduledTimeStamp == null) return false;

    // Check if the task is confirmed and not open
    if (task.taskStatus != "Confirmed" || task.isOpen == true) return false;

    // Compare only the date part (year, month, day) ignoring time
    final taskDate = task.scheduledTimeStamp!;
    final taskDateOnly = DateTime(taskDate.year, taskDate.month, taskDate.day);
    final selectedDateOnly = DateTime(date.year, date.month, date.day);

    // Check if the dates match
    return taskDateOnly.isAtSameMomentAs(selectedDateOnly);
  }

  // Update the map of dates that have tasks
  void _updateDatesWithTasks(List<schedule.TaskDetail> tasks) {
    datesWithTasks.clear();

    for (var task in tasks) {
      if (task.scheduledTimeStamp != null &&
          task.taskStatus == "Confirmed" &&
          task.isOpen == false) {
        final taskDate = task.scheduledTimeStamp!;
        final dateKey = "${taskDate.year}-${taskDate.month}-${taskDate.day}";

        datesWithTasks[dateKey] = true;
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ScheduleTaskCubit, ScheduleTaskState>(
      listener: (context, state) {
        if (state is ScheduleTaskSuccess) {
          var response = state.response;

          setState(() {
            // Store all tasks from the API response
            allApiTasks = response.addTasks ?? [];

            // Update the map of dates with tasks
            _updateDatesWithTasks(allApiTasks);
          });

          _updateScheduledTasksForView();
        } else if (state is ScheduleTaskError) {
          // Show error message
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey2,
          appBar: CustomAppBar(
            title: 'Scheduled',
            bottom: _buildViewToggleButtons(),
            actions: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isCheckboxMode = false;
                  });
                },
                child: Image.asset(
                  AppAssets.appbarCalendar,
                  scale: 4,
                  color: !_isCheckboxMode
                      ? AppColors.primaryBlue
                      : AppColors.black,
                ),
              ),
              const Gap(8),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isCheckboxMode = !_isCheckboxMode;
                    if (!_isCheckboxMode) {
                      // Clear selection when exiting checkbox mode
                      selectedItems.clear();
                      _areAllItemsSelected = false;
                    }
                  });
                },
                child: Image.asset(
                  AppAssets.appbarCalendarEdit,
                  color:
                      _isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
                  scale: 4,
                ),
              ),
              const Gap(16)
            ],
          ),
          body: BlocListener<SyncCubit, SyncState>(
            listener: (context, state) {
              if (state is SyncSuccess) {
                _refreshData();
              }
            },
            child: RefreshIndicator(
              onRefresh: _initializeData,
              child: _buildScrollableContent(context),
            ),
          ),
          floatingActionButton: _isCheckboxMode
              ? RescheduleFloatingButtons(
                  isCheckboxMode: _isCheckboxMode,
                  selectedItems: selectedItems,
                  areAllItemsSelected: _areAllItemsSelected,
                  onClose: () {
                    setState(() {
                      _isCheckboxMode = false;
                      selectedItems.clear();
                      _areAllItemsSelected = false;
                    });
                  },
                  onSelectAll: _selectAllItems,
                  onReschedule: () {
                    // Show calendar bottom sheet
                    _showCalendarBottomSheet(context);
                  },
                )
              : null,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
        );
      },
    );
  }

  // Implementation of select all that works directly with the data
  void _selectAllItems() {
    setState(() {
      _areAllItemsSelected = !_areAllItemsSelected;

      if (_areAllItemsSelected) {
        // We're selecting all items - create a fresh list with all scheduled tasks
        selectedItems = List.from(scheduledTasks);
      } else {
        // We're deselecting all - clear the list
        selectedItems.clear();
      }
    });
  }

  void _handleSelectionChanged(
      List<TaskDetail> itemsInList, List<TaskDetail> selectedItemsInList) {
    setState(() {
      // Get a set of task IDs for the items managed by the component that fired the event
      final itemsInListIds = itemsInList.map((t) => t.taskId).toSet();

      // Remove all tasks from the global selection that were managed by this component
      selectedItems.removeWhere((task) => itemsInListIds.contains(task.taskId));

      // Add the newly selected items from the component
      selectedItems.addAll(selectedItemsInList);

      // Update _areAllItemsSelected based on whether all items are selected
      _areAllItemsSelected = selectedItems.length == scheduledTasks.length &&
          scheduledTasks.isNotEmpty;
    });
  }

  // Build the scrollable content that includes task list
  Widget _buildScrollableContent(BuildContext context) {
    // Calendar views (Week/Month) don't need ScrollView wrapper
    if (_currentViewMode == ViewMode.calendar) {
      if (_calendarViewMode == CalendarViewMode.week) {
        return WeekCalendarView(
          allApiTasks: allApiTasks,
          isCheckboxMode: _isCheckboxMode,
          areAllItemsSelected: _areAllItemsSelected,
          onSelectionChanged: _handleSelectionChanged,
          convertScheduleToDatum: convertScheduleToDatum,
          calculateTotalHours: _calculateTotalHours,
          onTaskTap: _handleTaskTap,
        );
      } else {
        return MonthCalendarView(
          allApiTasks: allApiTasks,
          isCheckboxMode: _isCheckboxMode,
          areAllItemsSelected: _areAllItemsSelected,
          onSelectionChanged: _handleSelectionChanged,
          convertScheduleToDatum: convertScheduleToDatum,
          calculateTotalHours: _calculateTotalHours,
          onTaskTap: _handleTaskTap,
        );
      }
    }

    // List view mode - existing tab functionality
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content section based on selected button
          if (_selectedButton == 'all')
            // For "All" tab, show all scheduled tasks
            AllTasksList(
              allApiTasks: allApiTasks,
              isCheckboxMode: _isCheckboxMode,
              areAllItemsSelected: _areAllItemsSelected,
              // onParentActionTap: _handleParentActionTap,
              // onSubtaskActionTap: _handleSubtaskActionTap,
              onSelectionChanged: _handleSelectionChanged,
              convertScheduleToDatum: convertScheduleToDatum,
              onTaskTap: _handleTaskTap,
            )
          else if (_selectedButton == 'this_week' ||
              _selectedButton == 'next_week')
            // For week tabs, show tasks for the selected week
            WeekTasksList(
              allApiTasks: allApiTasks,
              weekDays: weekDays,
              isCheckboxMode: _isCheckboxMode,
              areAllItemsSelected: _areAllItemsSelected,
              // onParentActionTap: _handleParentActionTap,
              // onSubtaskActionTap: _handleSubtaskActionTap,
              onSelectionChanged: _handleSelectionChanged,
              convertScheduleToDatum: convertScheduleToDatum,
              calculateTotalHours: _calculateTotalHours,
              onTaskTap: _handleTaskTap,
            )
          else if (_selectedButton == 'overdue')
            // For overdue tab, show overdue tasks
            OverdueTasksList(
              allApiTasks: allApiTasks,
              isCheckboxMode: _isCheckboxMode,
              areAllItemsSelected: _areAllItemsSelected,
              // onParentActionTap: _handleParentActionTap,
              // onSubtaskActionTap: _handleSubtaskActionTap,
              onSelectionChanged: _handleSelectionChanged,
              convertScheduleToDatum: convertScheduleToDatum,
              onTaskTap: _handleTaskTap,
            )
          else
            // Fallback for any other state
            Container(
              color: AppColors.lightGrey2,
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Select a view option',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),

          // Add bottom padding to ensure content is visible above floating action button
          if (_isCheckboxMode)
            const SizedBox(height: 120), // Space for floating action button
        ],
      ),
    );
  }

  // Calculate total hours for a list of tasks
  String _calculateTotalHours(List<TaskDetail> tasks) {
    logger('tasks: ${tasks.length}');
    if (tasks.isEmpty) {
      return "0 hrs";
    }

    double totalMinutes = 0;
    for (var task in tasks) {
      if (task.minutes != null) {
        // Ensure we're properly converting the minutes value to a double
        // Handle different numeric types (int, double, num)
        try {
          logger(
              'Task minutes: ${task.budget}, type: ${task.budget.runtimeType}');
          totalMinutes += task.budget!.toDouble();
        } catch (e) {
          logger("Error converting minutes to double: ${e.toString()}");
          // If conversion fails, try parsing as string
          try {
            totalMinutes += double.parse(task.budget.toString());
          } catch (e) {
            logger("Error parsing minutes as string: ${e.toString()}");
          }
        }
      }
    }

    // Convert minutes to hours with proper decimal representation
    double hours = totalMinutes / 60;

    // Format hours to show proper fractions (e.g., 0.75 instead of 0.8 for 45 minutes)
    // For 45 minutes, we want to show 0.75 hrs instead of 0.8 hrs
    String formattedHours;
    if (totalMinutes % 60 == 45) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.75"; // Use exact fraction for 45 minutes
    } else if (totalMinutes % 60 == 30) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.5"; // Use exact fraction for 30 minutes
    } else if (totalMinutes % 60 == 15) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.25"; // Use exact fraction for 15 minutes
    } else {
      formattedHours = hours.toStringAsFixed(2);
      // Remove trailing zeros
      if (formattedHours.endsWith('0')) {
        formattedHours = formattedHours.substring(0, formattedHours.length - 1);
      }
    }

    logger(
        'Total minutes: $totalMinutes, hours: $hours, formatted: $formattedHours');
    return "$formattedHours hrs";
  }

  // Add calendar bottom sheet functionality
  void _showCalendarBottomSheet(BuildContext context) {
    // Create a local reference to the cubit to avoid context usage in async gap
    final scheduleTaskCubit = context.read<ScheduleTaskCubit>();

    // Create a list of dates that have tasks
    List<DateTime> taskDates = [];
    for (var task in allApiTasks) {
      if (task.scheduledTimeStamp != null &&
          task.taskStatus == "Confirmed" &&
          task.isOpen != true) {
        taskDates.add(task.scheduledTimeStamp!);
      }
    }

    // Show calendar bottom sheet
    CalendarBottomSheet.show(
      context: context,
      calendarResponse: scheduleTaskCubit
          .calendarResponse, // Pass the calendar response from the cubit
      taskDates: taskDates, // Pass the task dates to show grey circles
    ).then((selectedDate) async {
      // Check if the widget is still mounted and a date was selected
      if (selectedDate != null && mounted && selectedItems.isNotEmpty) {
        // Store context before async operations
        final currentContext = context;
        final itemsCount = selectedItems.length;

        // Validate selected date against tasks
        final invalidTasks = TaskScheduleValidator.getInvalidTasksForDate(
            selectedDate, selectedItems);

        if (invalidTasks.isNotEmpty) {
          // Create detailed message about invalid tasks
          final invalidTaskNames = invalidTasks
              .map((task) => task.storeName ?? 'Unknown Store')
              .take(3)
              .join(', ');
          final moreTasksCount = invalidTasks.length > 3
              ? ' and ${invalidTasks.length - 3} more'
              : '';

          if (mounted) {
            await showDialog<void>(
              context: currentContext,
              barrierDismissible: false,
              builder: (BuildContext dialogContext) {
                return ConfirmDialog(
                  title: 'Cannot Reschedule Tasks',
                  message:
                      'The following tasks cannot be rescheduled to ${DateFormat('MMM dd, yyyy').format(selectedDate)}:\n\n'
                      '$invalidTaskNames$moreTasksCount\n\n'
                      'These tasks are outside their valid scheduling range or past their expiry date.',
                  confirmText: 'OK',
                  onConfirm: () {
                    // Just close the dialog
                  },
                );
              },
            );
          }

          return; // Don't proceed with rescheduling
        }

        // Check if widget is still mounted after validation
        if (!mounted) return;

        // Show rescheduling message immediately
        SnackBarService.info(
          context: currentContext,
          message:
              'Rescheduling $itemsCount tasks for ${DateFormat('MMM dd, yyyy').format(selectedDate)}',
        );

        // Perform async operations
        _performRescheduling(selectedDate);
      }
    });
  }

  /// Performs the actual rescheduling operations asynchronously
  Future<void> _performRescheduling(DateTime selectedDate) async {
    try {
      // submission timestamp is current time in timezone "Australia/NSW", locale "en", "AU"
      final submissionTimeStamp = DateTime.now();

      // Update scheduling data for each selected task in the database
      for (var task in selectedItems) {
        await TaskUtils.updateTaskSchedulingData(
          taskId: task.taskId.toString(),
          scheduledTimeStamp: selectedDate,
          submissionTimeStamp: submissionTimeStamp,
          taskStatus: "Confirmed",
          submissionState: 1,
        );
      }

      // Sync the changes to the server
      if (mounted) {
        await SyncService().sync(context: context);
      }

      // Clear selection after rescheduling
      if (mounted) {
        setState(() {
          _isCheckboxMode = false;
          selectedItems.clear();
          _areAllItemsSelected = false;
        });
      }
    } catch (e) {
      logger('Error during rescheduling: $e');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to reschedule tasks. Please try again.',
        );
      }
    }
  }

  /// Handle task tap with time validation
  Future<void> _handleTaskTap(TaskDetail task) async {
    if ((await TimeAccessValidator.canAccessTask(context, task,
            skipDialog: task.reOpened == true)) ||
        task.reOpened == true) {
      if (mounted) {
        context.router.push(TaskDetailsRoute(
          storeId: task.storeId ?? 0,
          taskId: task.taskId!.toInt(),
        ));
      }
    }
  }
}
