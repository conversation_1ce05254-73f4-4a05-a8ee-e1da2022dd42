import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/vacancies/vacancies_cubit.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class ReferralFormPage extends StatefulWidget {
  final int vacancyId;

  const ReferralFormPage({
    super.key,
    required this.vacancyId,
  });

  @override
  State<ReferralFormPage> createState() => _ReferralFormPageState();
}

class _ReferralFormPageState extends State<ReferralFormPage> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _suburbController = TextEditingController();
  final TextEditingController _commentController = TextEditingController();

  // Preferred work checkboxes
  bool _retail = false;
  bool _hardware = false;
  bool _demonstrations = false;
  bool _any = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _stateController.dispose();
    _suburbController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  void _submitReferral() {
    print('DEBUG: _submitReferral called');

    // Debug: Check individual field values
    print('DEBUG: Name: "${_nameController.text.trim()}"');
    print('DEBUG: Email: "${_emailController.text.trim()}"');
    print('DEBUG: Phone: "${_phoneController.text.trim()}"');
    print('DEBUG: State: "${_stateController.text.trim()}"');
    print('DEBUG: Suburb: "${_suburbController.text.trim()}"');
    print(
        'DEBUG: Preferred work - Retail: $_retail, Hardware: $_hardware, Demonstrations: $_demonstrations, Any: $_any');

    // Debug: Check validation manually
    final nameValid = _nameController.text.trim().isNotEmpty;
    final emailValid = _emailController.text.trim().isNotEmpty &&
        RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
            .hasMatch(_emailController.text.trim());
    final phoneValid = _phoneController.text.trim().isNotEmpty;
    final stateValid = _stateController.text.trim().isNotEmpty;
    final suburbValid = _suburbController.text.trim().isNotEmpty;
    final preferenceValid = _retail || _hardware || _demonstrations || _any;

    print(
        'DEBUG: Validation results - Name: $nameValid, Email: $emailValid, Phone: $phoneValid, State: $stateValid, Suburb: $suburbValid, Preference: $preferenceValid');

    print(
        'DEBUG: Form validation result: ${_formKey.currentState?.validate() ?? false}');

    if (_formKey.currentState?.validate() ?? false) {
      print('DEBUG: Form validation passed');

      // Collect preferred work preferences
      final List<String> preferredWork = [];
      if (_retail) preferredWork.add('Retail');
      if (_hardware) preferredWork.add('Hardware');
      if (_demonstrations) preferredWork.add('Demonstrations');
      if (_any) preferredWork.add('Any');

      print('DEBUG: Preferred work selections: $preferredWork');

      // Validate that at least one preference is selected
      if (preferredWork.isEmpty) {
        print('DEBUG: No preferred work selected, showing error');
        _showErrorSnackBar('Please select at least one preferred work type');
        return;
      }

      // Join preferences with comma
      final preference = preferredWork.join(',');

      // Get form data
      final name = _nameController.text.trim();
      final email = _emailController.text.trim();
      final phone = _phoneController.text.trim();
      final state = _stateController.text.trim();
      final suburb = _suburbController.text.trim();
      final comment = _commentController.text.trim();

      print(
          'DEBUG: Form data collected - name: $name, email: $email, phone: $phone, state: $state, suburb: $suburb, preference: $preference');
      print(
          'DEBUG: About to call referVacancyWithDetails for vacancy ID: ${widget.vacancyId}');

      // Call the enhanced referVacancy method
      try {
        final cubit = context.read<VacanciesCubit>();
        print('DEBUG: Successfully got VacanciesCubit: ${cubit.runtimeType}');
        cubit.referVacancyWithDetails(
          widget.vacancyId,
          name: name,
          email: email,
          phone: phone,
          stateName: state,
          suburb: suburb,
          comment: comment,
          preference: preference,
        );
        print('DEBUG: referVacancyWithDetails called successfully');
      } catch (e) {
        print('DEBUG: Error calling referVacancyWithDetails: $e');
      }
    } else {
      print('DEBUG: Form validation failed');

      // Show specific validation errors
      final errors = <String>[];
      if (_nameController.text.trim().isEmpty) errors.add('Name is required');
      if (_emailController.text.trim().isEmpty) {
        errors.add('Email is required');
      } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
          .hasMatch(_emailController.text.trim())) {
        errors.add('Email format is invalid');
      }
      if (_phoneController.text.trim().isEmpty) {
        errors.add('Phone number is required');
      }
      if (_stateController.text.trim().isEmpty) errors.add('State is required');
      if (_suburbController.text.trim().isEmpty) {
        errors.add('Suburb is required');
      }
      if (!_retail && !_hardware && !_demonstrations && !_any) {
        errors.add('At least one preferred work type is required');
      }

      print('DEBUG: Validation errors: ${errors.join(', ')}');

      // Show error message to user
      _showErrorSnackBar(
          'Please fix the following errors: ${errors.join(', ')}');
    }
  }

  void _showErrorSnackBar(String message) {
    SnackBarService.error(
      context: context,
      message: message,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Refer a Friend',
        showBackButton: true,
      ),
      body: BlocListener<VacanciesCubit, VacanciesState>(
        listener: (context, state) {
          if (state is VacancyActionSuccess) {
            SnackBarService.success(
              context: context,
              message: state.message,
            );
            context.router.maybePop(); // Pop back to vacancies page
          } else if (state is VacancyActionError) {
            _showErrorSnackBar(state.message);
          } else if (state is VacancyActionInProgress) {
            logger('VacancyActionInProgress: ${state.action}');
          }
        },
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Card
                _buildHeaderCard(),
                const Gap(16),

                // Personal Information Card
                _buildSectionCard(
                  title: 'Personal Information',
                  icon: Icons.person_outline,
                  children: [
                    _buildModernTextField(
                      label: 'Full Name',
                      controller: _nameController,
                      hintText: 'Enter full name',
                      prefixIcon: Icons.person,
                      validator: (value) {
                        if (value?.trim().isEmpty ?? true) {
                          return 'Name is required';
                        }
                        return null;
                      },
                    ),
                    const Gap(16),
                    _buildModernTextField(
                      label: 'Email Address',
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      hintText: 'Enter email address',
                      prefixIcon: Icons.email_outlined,
                      validator: (value) {
                        if (value?.trim().isEmpty ?? true) {
                          return 'Email is required';
                        }
                        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                            .hasMatch(value!)) {
                          return 'Please enter a valid email address';
                        }
                        return null;
                      },
                    ),
                    const Gap(16),
                    _buildModernTextField(
                      label: 'Phone Number',
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      hintText: 'Enter phone number',
                      prefixIcon: Icons.phone_outlined,
                      validator: (value) {
                        if (value?.trim().isEmpty ?? true) {
                          return 'Phone number is required';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
                const Gap(16),

                // Location Card
                _buildSectionCard(
                  title: 'Location Details',
                  icon: Icons.location_on_outlined,
                  children: [
                    _buildModernTextField(
                      label: 'State',
                      controller: _stateController,
                      hintText: 'e.g., NSW, VIC, QLD',
                      prefixIcon: Icons.map_outlined,
                      validator: (value) {
                        if (value?.trim().isEmpty ?? true) {
                          return 'State is required';
                        }
                        return null;
                      },
                    ),
                    const Gap(16),
                    _buildModernTextField(
                      label: 'Suburb',
                      controller: _suburbController,
                      hintText: 'Enter suburb',
                      prefixIcon: Icons.home_outlined,
                      validator: (value) {
                        if (value?.trim().isEmpty ?? true) {
                          return 'Suburb is required';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
                const Gap(16),

                // Preferred Work Card
                _buildSectionCard(
                  title: 'Preferred Work Type',
                  icon: Icons.work_outline,
                  children: [
                    Text(
                      'Select at least one work type preference:',
                      style: AppTypography.montserratParagraphSmall.copyWith(
                        color: AppColors.blackTint1,
                      ),
                    ),
                    const Gap(12),
                    _buildModernCheckboxGrid(),
                  ],
                ),
                const Gap(16),

                // Comments Card
                _buildSectionCard(
                  title: 'Additional Comments',
                  icon: Icons.comment_outlined,
                  children: [
                    _buildModernTextField(
                      label: 'Comments (optional)',
                      controller: _commentController,
                      hintText:
                          'Any additional comments or notes about the referral...',
                      prefixIcon: Icons.notes,
                      maxLines: 3,
                    ),
                  ],
                ),
                const Gap(24),

                // Save button
                _buildSaveButton(),
                const Gap(24),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the save button using AppButton
  Widget _buildSaveButton() {
    return AppButton(
      text: 'Submit Referral',
      color: AppColors.primaryBlue,
      onPressed: () {
        logger('DEBUG: Submit button pressed');
        _submitReferral();
      },
      height: 50,
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.person_add_outlined,
              color: AppColors.primaryBlue,
              size: 24,
            ),
          ),
          const Gap(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Referral Form',
                  style: AppTypography.montserratHeadingMedium.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: AppColors.black,
                  ),
                ),
                const Gap(4),
                Text(
                  'Help us find great talent by referring someone you know',
                  style: AppTypography.montserratParagraphSmall.copyWith(
                    color: AppColors.blackTint1,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.primaryBlue,
                size: 20,
              ),
              const Gap(12),
              Text(
                title,
                style: AppTypography.montserratHeadingMedium.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          const Gap(20),
          ...children,
        ],
      ),
    );
  }

  /// Builds a text field with edit profile styling
  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    String? hintText,
    IconData? prefixIcon,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.montserratTitleSmall,
        ),
        const Gap(8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hintText,
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppColors.blackTint1,
                    size: 20,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.borderColor,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.primaryBlue,
                width: 1.5,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1.5),
            ),
            filled: true,
            fillColor: AppColors.lightGrey1,
            contentPadding: EdgeInsets.symmetric(
              horizontal: prefixIcon != null ? 12.0 : 16.0,
              vertical: maxLines > 1 ? 16.0 : 14.0,
            ),
            hintStyle: AppTypography.montserratParagraphSmall.copyWith(
              color: AppColors.blackTint1,
            ),
          ),
          style: AppTypography.montserratFormsfield,
        ),
      ],
    );
  }

  Widget _buildModernCheckboxGrid() {
    return Column(
      children: [
        _buildModernCheckbox(
          title: 'Retail',
          value: _retail,
          onChanged: (value) => setState(() => _retail = value ?? false),
        ),
        const Gap(12),
        _buildModernCheckbox(
          title: 'Hardware',
          value: _hardware,
          onChanged: (value) => setState(() => _hardware = value ?? false),
        ),
        const Gap(12),
        _buildModernCheckbox(
          title: 'Demonstrations',
          value: _demonstrations,
          onChanged: (value) =>
              setState(() => _demonstrations = value ?? false),
        ),
        const Gap(12),
        _buildModernCheckbox(
          title: 'Any',
          value: _any,
          onChanged: (value) => setState(() => _any = value ?? false),
        ),
      ],
    );
  }

  /// Builds a checkbox widget with edit profile styling
  Widget _buildModernCheckbox({
    required String title,
    required bool value,
    required void Function(bool?) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: value ? AppColors.lightGrey1 : AppColors.lightGrey1,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: value ? AppColors.primaryBlue : AppColors.borderColor,
          width: 1,
        ),
      ),
      child: GestureDetector(
        onTap: () => onChanged(!value),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: value ? AppColors.primaryBlue : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: value ? AppColors.primaryBlue : AppColors.blackTint2,
                  width: 2,
                ),
              ),
              child: value
                  ? const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    )
                  : null,
            ),
            const Gap(12),
            Expanded(
              child: Text(
                title,
                style: AppTypography.montserratTitleSmall,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
