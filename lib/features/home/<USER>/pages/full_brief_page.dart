import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/brief_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/full_brief/full_brief_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/full_brief/full_brief_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/offline_document_widget.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

@RoutePage()
class FullBriefPage extends StatefulWidget {
  final String taskId;

  const FullBriefPage({
    super.key,
    required this.taskId,
  });

  @override
  State<FullBriefPage> createState() => _FullBriefPageState();
}

class _FullBriefPageState extends State<FullBriefPage> {
  late final FullBriefCubit _fullBriefCubit;
  String _authToken = '';
  Set<String> expandedDocuments = {};

  @override
  void initState() {
    super.initState();
    _fullBriefCubit = sl<FullBriefCubit>();
    _loadCredentialsAndFetchBrief();
  }

  Future<void> _loadCredentialsAndFetchBrief() async {
    try {
      final dataManager = sl<DataManager>();
      _authToken = await dataManager.getAuthToken() ?? '';

      logger(
          "FullBriefPage: Loaded auth token: ${_authToken.isNotEmpty ? 'Present' : 'Missing'}");
      logger("FullBriefPage: Task ID: ${widget.taskId}");

      if (_authToken.isNotEmpty) {
        logger("FullBriefPage: Starting to fetch brief data...");
        _fullBriefCubit.getBrief(widget.taskId, _authToken);
      } else {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Authentication token not available. Please login again.',
          );
        }
      }
    } catch (e) {
      logger("FullBriefPage: Error in _loadCredentialsAndFetchBrief: $e");
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to load credentials: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _testApiCall() async {
    try {
      logger("FullBriefPage: Testing API call directly...");

      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken() ?? '';

      if (token.isEmpty) {
        logger("FullBriefPage: No auth token available for test");
        return;
      }

      logger("FullBriefPage: Test - Token: ${token.substring(0, 10)}...");
      logger("FullBriefPage: Test - Task ID: ${widget.taskId}");

      // Test the API call directly
      _fullBriefCubit.getBrief(widget.taskId, token);
    } catch (e) {
      logger("FullBriefPage: Error in test API call: $e");
    }
  }

  @override
  void dispose() {
    _fullBriefCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _fullBriefCubit,
      child: Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: const CustomAppBar(
          title: 'Full Brief',
          showBackButton: true,
        ),
        body: BlocConsumer<FullBriefCubit, FullBriefState>(
          listener: (context, state) {
            logger("FullBriefPage: State changed to: ${state.runtimeType}");
            if (state is FullBriefError) {
              logger("FullBriefPage: Error state received: ${state.message}");
              SnackBarService.error(
                context: context,
                message: state.message,
              );
            } else if (state is FullBriefSuccess) {
              logger(
                  "FullBriefPage: Success state received with ${state.response.documents.length} documents");
            }
          },
          builder: (context, state) {
            logger(
                "FullBriefPage: Building UI for state: ${state.runtimeType}");
            if (state is FullBriefLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else if (state is FullBriefSuccess) {
              return _buildBriefContent(state.response);
            } else if (state is FullBriefError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const Gap(16),
                    Text(
                      'Failed to load brief',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const Gap(8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Text(
                        state.message,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const Gap(16),
                    ElevatedButton(
                      onPressed: _loadCredentialsAndFetchBrief,
                      child: const Text('Retry'),
                    ),
                    const Gap(8),
                    TextButton(
                      onPressed: () {
                        logger("FullBriefPage: Manual retry triggered");
                        _loadCredentialsAndFetchBrief();
                      },
                      child: const Text('Debug: Retry with Logging'),
                    ),
                    const Gap(16),
                    // Debug information
                    Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Debug Info:',
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const Gap(4),
                          Text('Task ID: ${widget.taskId}'),
                          Text(
                              'Auth Token: ${_authToken.isNotEmpty ? 'Present' : 'Missing'}'),
                          Text('Error: ${state.message}'),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            } else {
              logger("FullBriefPage: Initial state - showing empty state");
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const EmptyState(message: 'No brief data available'),
                    const Gap(16),
                    ElevatedButton(
                      onPressed: () {
                        logger(
                            "FullBriefPage: Debug - Testing API call directly");
                        _testApiCall();
                      },
                      child: const Text('Debug: Test API Call'),
                    ),
                    const Gap(8),
                    Text('Task ID: ${widget.taskId}'),
                    const Gap(8),
                    Text(
                        'Auth Token: ${_authToken.isNotEmpty ? 'Present' : 'Missing'}'),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildBriefContent(BriefResponseEntity response) {
    logger(
        "FullBriefPage: Building brief content with ${response.documents.length} documents");

    if (response.documents.isEmpty) {
      logger("FullBriefPage: No documents available, showing empty state");
      return const Center(
        child: EmptyState(message: 'No documents available for this brief'),
      );
    }

    logger(
        "FullBriefPage: Building ListView with ${response.documents.length} documents");
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.only(
                  top: 20, left: 16, right: 16, bottom: 16),
              itemCount: response.documents.length,
              itemBuilder: (context, index) {
                final document = response.documents[index];
                logger(
                    "FullBriefPage: Building document card for: ${document.documentName} with ${document.files.length} files");
                return _buildDocumentCard(document);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentCard(BriefDocument document) {
    logger(
        "FullBriefPage: Building document card for: ${document.documentName}");
    logger("FullBriefPage: Document has ${document.files.length} files");

    final textTheme = Theme.of(context).textTheme;
    final isExpanded = expandedDocuments.contains(document.documentName);

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              width: 40,
              height: 40,
              alignment: Alignment.centerLeft,
              child: document.documentIconLink != null &&
                      document.documentIconLink!.isNotEmpty
                  ? Image.network(
                      document.documentIconLink!,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      color: Colors.black,
                      errorBuilder: (context, error, stackTrace) {
                        return Image.asset(
                          AppAssets.supportDocument,
                          width: 40,
                          height: 40,
                          scale: 3,
                          color: Colors.black,
                        );
                      },
                    )
                  : Image.asset(
                      AppAssets.supportDocument,
                      width: 40,
                      height: 40,
                      scale: 3,
                      color: Colors.black,
                    ),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
            title: Text(
              document.documentName,
              style: textTheme.montserratTitleExtraSmall.copyWith(
                color: Colors.black,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.documentType,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
                if (document.modifiedTimeStampDocument != null)
                  Text(
                    'Modified: ${document.modifiedTimeStampDocument}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[500],
                          fontSize: 11,
                        ),
                  ),
              ],
            ),
            trailing: Icon(
              isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              color: AppColors.black,
              size: 20,
            ),
            onTap: () {
              setState(() {
                if (isExpanded) {
                  expandedDocuments.remove(document.documentName);
                } else {
                  expandedDocuments.add(document.documentName);
                }
              });
            },
          ),
          if (isExpanded) ...[
            if (document.files.isEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No files available for this document',
                  style: textTheme.bodySmall?.copyWith(
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
              )
            else
              ...document.files.map((file) {
                logger(
                    "FullBriefPage: Building file item for: ${file.fileName} with URL: ${file.fileUrl}");
                return _buildFileItem(file, document);
              }),
            const Gap(12),
          ],
        ],
      ),
    );
  }

  Widget _buildFileItem(BriefFile file, BriefDocument document) {
    logger("FullBriefPage: Building file item widget for: ${file.fileName}");
    logger("FullBriefPage: File URL: ${file.fileUrl}");

    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: OfflineDocumentWidget(
        url: file.fileUrl,
        title: file.fileName,
        child: ListTile(
          dense: true,
          leading: Icon(
            _getFileTypeIcon(file.fileUrl),
            color: AppColors.black,
            size: 20,
          ),
          title: Text(
            file.fileName,
            style: textTheme.bodySmall?.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getFileTypeFromUrl(file.fileUrl),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              if (file.modifiedTimeStampFile != null)
                Text(
                  'Modified: ${file.modifiedTimeStampFile}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                        fontSize: 11,
                      ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getFileTypeIcon(String fileUrl) {
    final url = fileUrl.toLowerCase();
    if (url.contains('.pdf')) {
      return Icons.picture_as_pdf;
    } else if (url.contains('.jpg') ||
        url.contains('.jpeg') ||
        url.contains('.png') ||
        url.contains('.gif')) {
      return Icons.image;
    } else if (url.contains('youtube.com') || url.contains('youtu.be')) {
      return Icons.play_circle;
    } else if (url.contains('.txt') ||
        url.contains('.doc') ||
        url.contains('.docx')) {
      return Icons.description;
    } else if (url.contains('.xls') || url.contains('.xlsx')) {
      return Icons.table_chart;
    } else if (url.contains('.ppt') || url.contains('.pptx')) {
      return Icons.slideshow;
    } else {
      return Icons.insert_drive_file;
    }
  }

  String _getFileTypeFromUrl(String fileUrl) {
    final url = fileUrl.toLowerCase();
    if (url.contains('.pdf')) {
      return 'PDF Document';
    } else if (url.contains('.jpg') || url.contains('.jpeg')) {
      return 'JPEG Image';
    } else if (url.contains('.png')) {
      return 'PNG Image';
    } else if (url.contains('.gif')) {
      return 'GIF Image';
    } else if (url.contains('youtube.com') || url.contains('youtu.be')) {
      return 'YouTube Video';
    } else if (url.contains('.txt')) {
      return 'Text File';
    } else if (url.contains('.doc') || url.contains('.docx')) {
      return 'Word Document';
    } else if (url.contains('.xls') || url.contains('.xlsx')) {
      return 'Excel Spreadsheet';
    } else if (url.contains('.ppt') || url.contains('.pptx')) {
      return 'PowerPoint Presentation';
    } else {
      return 'File';
    }
  }
}
