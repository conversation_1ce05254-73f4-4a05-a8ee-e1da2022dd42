import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

import '../widgets/empty_state.dart';

@RoutePage()
class NotesPage extends StatefulWidget {
  final TaskDetail task;

  const NotesPage({
    super.key,
    required this.task,
  });

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> {
  @override
  Widget build(BuildContext context) {
    final hasNote =
        widget.task.taskNote != null && widget.task.taskNote!.isNotEmpty;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Task Note',
      ),
      body: !hasNote
          ? _buildEmptyState(context)
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // const Gap(8),

                  // Note content with improved styling
                  Expanded(child: _buildNoteContent(context, hasNote)),

                  // const Gap(24),
                ],
              ),
            ),
    );
  }

  Widget _buildNoteContent(BuildContext context, bool hasNote) {
    return Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: AppColors.black10,
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Note content with HTML support
            Expanded(
              child: Html(
                data: widget.task.taskNote!,
                style: {
                  "body": Style(
                    margin: Margins.zero,
                    padding: HtmlPaddings.zero,
                    fontSize: FontSize(15),
                    color: AppColors.black,
                    lineHeight: const LineHeight(1.6),
                    fontFamily: 'Montserrat',
                  ),
                  "p": Style(
                    margin: Margins.only(bottom: 8),
                    fontSize: FontSize(15),
                    color: AppColors.black,
                    lineHeight: const LineHeight(1.6),
                    fontFamily: 'Montserrat',
                  ),
                  "div": Style(
                    fontSize: FontSize(15),
                    color: AppColors.black,
                    lineHeight: const LineHeight(1.6),
                    fontFamily: 'Montserrat',
                  ),
                },
              ),
            ),
          ],
        ));
  }

  Widget _buildEmptyState(BuildContext context) {
    return const Center(
      child: EmptyState(message: 'No note available'),
    );
  }
}
