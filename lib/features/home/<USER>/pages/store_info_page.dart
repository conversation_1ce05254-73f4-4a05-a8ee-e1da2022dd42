import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_contact_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/store_comment_model.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/date_converter.dart';

import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_contacts/store_contacts_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_comments/store_comments_cubit.dart';

@RoutePage()
class StoreInfoPage extends StatefulWidget {
  final String storeId;
  final String taskId;
  const StoreInfoPage({super.key, required this.storeId, required this.taskId});

  @override
  State<StoreInfoPage> createState() => _StoreInfoPageState();
}

class _StoreInfoPageState extends State<StoreInfoPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Track which button is currently selected
  String _selectedButton = 'contacts'; // Options: 'contacts', 'comments'

  // Store comment data

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Update the selected button based on tab index
        _selectedButton = _tabController.index == 0 ? 'contacts' : 'comments';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => sl<StoreContactsCubit>()
            ..fetchStoreContacts(storeId: widget.storeId)
            ..fetchStoreContactTypes(storeId: widget.storeId),
        ),
        BlocProvider(
          create: (_) => sl<StoreCommentsCubit>(),
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<StoreContactsCubit, StoreContactsState>(
            listener: (context, state) {
              if (state is StoreContactsError) {
                SnackBarService.error(
                  context: context,
                  message: state.message,
                );
              } else if (state is StoreContactsSaved) {
                SnackBarService.success(
                  context: context,
                  message: 'Contact saved successfully',
                );
              }
            },
          ),
          BlocListener<StoreCommentsCubit, StoreCommentsState>(
            listener: (context, state) {
              if (state is StoreCommentsError) {
                SnackBarService.error(
                  context: context,
                  message: state.message,
                );
              } else if (state is StoreCommentsSaved) {
                SnackBarService.success(
                  context: context,
                  message: 'Comment saved successfully',
                );
                // Refresh comments after saving
                context.read<StoreCommentsCubit>().fetchStoreComments(
                      taskId: widget.taskId, // Using storeId as taskId for now
                    );
              }
            },
          ),
        ],
        child: BlocBuilder<StoreContactsCubit, StoreContactsState>(
          builder: (context, contactState) {
            // Show loading state for initial page load
            if ((contactState is StoreContactsInitialLoading ||
                    contactState is StoreContactsLoading ||
                    contactState is StoreContactTypesLoading ||
                    contactState is StoreContactsPartiallyLoaded) &&
                _selectedButton == 'contacts') {
              return _buildLoadingScaffold(context);
            }
            return _buildScaffold(context);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingScaffold(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: 'Store Information',
        backgroundColor: Colors.white,
        titleColor: AppColors.black,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // Toggle buttons similar to ViewToggleButtons (disabled during loading)
                Expanded(
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: const Color(0xFFE0E0E0),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        // Contacts button (selected by default)
                        Expanded(
                          child: Container(
                            decoration: const BoxDecoration(
                              color: AppColors.primaryBlue,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8),
                                bottomLeft: Radius.circular(8),
                              ),
                            ),
                            child: Center(
                              child: Text(
                                'Contacts',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratNavigationPrimaryMedium
                                    .copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                    ),
                              ),
                            ),
                          ),
                        ),
                        // Comments button
                        Expanded(
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFF5F5F5),
                              borderRadius: BorderRadius.only(
                                topRight: Radius.circular(8),
                                bottomRight: Radius.circular(8),
                              ),
                            ),
                            child: Center(
                              child: Text(
                                'Comments',
                                style: Theme.of(context)
                                    .textTheme
                                    .montserratNavigationPrimaryMedium
                                    .copyWith(
                                      color: Colors.black,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                    ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: Container(
        color: AppColors.lightGrey2,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: AppColors.primaryBlue,
              ),
              SizedBox(height: 16),
              Text(
                'Loading store information...',
                style: TextStyle(
                  color: AppColors.blackTint1,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScaffold(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: 'Store Info',
        backgroundColor: Colors.white,
        titleColor: AppColors.black,
        actions: [
          _buildAddButton(context),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // Toggle buttons similar to ViewToggleButtons
                Expanded(
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: const Color(0xFFE0E0E0),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        // Contacts button
                        _buildToggleButton(
                          context,
                          'Contacts',
                          'contacts',
                          const BorderRadius.only(
                            topLeft: Radius.circular(8),
                            bottomLeft: Radius.circular(8),
                          ),
                        ),
                        // Comments button
                        _buildToggleButton(
                          context,
                          'Comments',
                          'comments',
                          const BorderRadius.only(
                            topRight: Radius.circular(8),
                            bottomRight: Radius.circular(8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: Container(
        color: AppColors.lightGrey2,
        child: _selectedButton == 'contacts'
            ? _buildContactsList()
            : _buildCommentsList(),
      ),
    );
  }

  Widget _buildAddButton(BuildContext context) {
    return IconButton(
      onPressed: () => _selectedButton == 'contacts'
          ? _addContact(context)
          : _addComment(context),
      icon: const Icon(
        Icons.add,
        color: AppColors.primaryBlue,
      ),
    );
  }

  Future<void> _addContact(BuildContext parentContext) async {
    final result = await parentContext.router.push(
      AddContactRoute(
          contact: null, isEditMode: false, storeId: widget.storeId),
    );

    if (result == true) {
      // Contact was saved successfully, refresh the list
      if (mounted) {
        parentContext.read<StoreContactsCubit>().fetchStoreContacts(
              storeId: widget.storeId,
            );
      }
    }
  }

  Future<void> _addComment(BuildContext parentContext) async {
    final result = await parentContext.router.push(
      AddStoreCommentRoute(
        taskId: widget.taskId, // Using storeId as taskId for now
        comment: null,
        isEditMode: false,
      ),
    );

    if (result == true) {
      // Comment was saved successfully, refresh the list
      if (mounted) {
        parentContext.read<StoreCommentsCubit>().fetchStoreComments(
              taskId: widget.taskId,
            );
      }
    }
  }

  Future<void> _editContact(
      StoreContactData contact, int index, BuildContext parentContext) async {
    final result = await parentContext.router.push(
      AddContactRoute(
          contact: contact, isEditMode: true, storeId: widget.storeId),
    );

    if (result != null) {
      if (result == 'deleted') {
        // Delete the contact using cubit
        if (mounted) {
          await parentContext.read<StoreContactsCubit>().deleteStoreContact(
                storeId: widget.storeId,
                contactId: contact.employeeId ?? '',
              );
        }
      } else if (result == true) {
        // Contact was saved successfully, refresh the list
        if (mounted) {
          parentContext.read<StoreContactsCubit>().fetchStoreContacts(
                storeId: widget.storeId,
              );
        }
      }
    }
  }

  Widget _buildToggleButton(
    BuildContext context,
    String text,
    String buttonKey,
    BorderRadius borderRadius,
  ) {
    final bool isSelected = _selectedButton == buttonKey;

    return Expanded(
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedButton = buttonKey;
            // Update tab controller to match the selection
            if (buttonKey == 'contacts') {
              _tabController.animateTo(0);
              // Load contacts when contacts tab is selected
              context.read<StoreContactsCubit>().fetchStoreContacts(
                    storeId: widget.storeId,
                  );
            } else {
              _tabController.animateTo(1);
              // Load comments when comments tab is selected
              context.read<StoreCommentsCubit>().fetchStoreComments(
                    taskId: widget.taskId,
                  );
            }
          });
        },
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isSelected ? AppColors.primaryBlue : const Color(0xFFF5F5F5),
          foregroundColor: isSelected ? Colors.white : AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
            side: const BorderSide(
              color: Colors.grey,
              width: 0,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          text,
          style: Theme.of(context)
              .textTheme
              .montserratNavigationPrimaryMedium
              .copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
        ),
      ),
    );
  }

  Widget _buildContactsList() {
    return BlocBuilder<StoreContactsCubit, StoreContactsState>(
      builder: (context, state) {
        if (state is StoreContactsInitialLoading ||
            state is StoreContactsLoading ||
            state is StoreContactTypesLoading ||
            state is StoreContactsPartiallyLoaded) {
          return Container(
            color: AppColors.lightGrey2,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.primaryBlue,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading contacts...',
                    style: TextStyle(
                      color: AppColors.blackTint1,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        if (state is StoreContactsSaving) {
          final contacts = state.response.data;

          return Container(
            color: AppColors.lightGrey2,
            child: Stack(
              children: [
                ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: contacts.length,
                  itemBuilder: (context, index) {
                    final contact = contacts[index];
                    return _buildContactItem(contact, index, context);
                  },
                ),
                // Overlay loading indicator
                Container(
                  color: Colors.black.withValues(alpha: 0.3),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: AppColors.primaryBlue,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Saving contact...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        if (state is StoreContactsWithTypesLoaded) {
          final contacts = state.contactsResponse.data;

          if (contacts.isEmpty) {
            return Container(
              color: AppColors.lightGrey2,
              child: const EmptyState(message: 'No contacts available'),
            );
          }

          return Container(
            color: AppColors.lightGrey2,
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: contacts.length,
              itemBuilder: (context, index) {
                final contact = contacts[index];
                return _buildContactItem(contact, index, context);
              },
            ),
          );
        }

        if (state is StoreContactsLoaded) {
          final contacts = state.response.data;

          if (contacts.isEmpty) {
            return Container(
              color: AppColors.lightGrey2,
              child: const EmptyState(message: 'No contacts available'),
            );
          }

          return Container(
            color: AppColors.lightGrey2,
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: contacts.length,
              itemBuilder: (context, index) {
                final contact = contacts[index];
                return _buildContactItem(contact, index, context);
              },
            ),
          );
        }

        if (state is StoreContactsError || state is StoreContactTypesError) {
          return Container(
            color: AppColors.lightGrey2,
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: AppColors.blackTint1,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state is StoreContactsError
                          ? state.message
                          : (state as StoreContactTypesError).message,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        context.read<StoreContactsCubit>().fetchStoreContacts(
                              storeId: widget.storeId,
                            );
                        context
                            .read<StoreContactsCubit>()
                            .fetchStoreContactTypes(
                              storeId: widget.storeId,
                            );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return Container(
          color: AppColors.lightGrey2,
          child: const EmptyState(message: 'No contacts available'),
        );
      },
    );
  }

  Widget _buildCommentsList() {
    return BlocBuilder<StoreCommentsCubit, StoreCommentsState>(
      builder: (context, state) {
        if (state is StoreCommentsLoading) {
          return Container(
            color: AppColors.lightGrey2,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.primaryBlue,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading comments...',
                    style: TextStyle(
                      color: AppColors.blackTint1,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        if (state is StoreCommentsLoaded) {
          final comments = state.response.comments;

          if (comments.isEmpty) {
            return Container(
              color: AppColors.lightGrey2,
              child: const EmptyState(message: 'No comments available'),
            );
          }

          return Container(
            color: AppColors.lightGrey2,
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: comments.length,
              itemBuilder: (context, index) {
                final comment = comments[index];
                return _buildCommentItem(comment, context);
              },
            ),
          );
        }

        if (state is StoreCommentsError) {
          return Container(
            color: AppColors.lightGrey2,
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: AppColors.blackTint1,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        context.read<StoreCommentsCubit>().fetchStoreComments(
                              taskId: widget.taskId,
                            );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return Container(
          color: AppColors.lightGrey2,
          child: const EmptyState(message: 'No comments available'),
        );
      },
    );
  }

  Widget _buildContactItem(
      StoreContactData contact, int index, BuildContext context) {
    return GestureDetector(
      onTap: () => _editContact(contact, index, context),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Contact avatar
            CircleAvatar(
              radius: 24,
              backgroundColor: AppColors.primaryBlue.withValues(alpha: 0.1),
              // child: Text(
              //   contact.firstName.isNotEmpty ? contact.firstName.toUpperCase() : 'N/A',
              //   style: Theme.of(context)
              //       .textTheme
              //       .montserratNavigationPrimaryMedium
              //       .copyWith(
              //         color: AppColors.primaryBlue,
              //         fontWeight: FontWeight.w600,
              //       ),
              // ),
            ),
            const SizedBox(width: 18),
            // Contact details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    contact.contactTypeName ?? '',
                    style: Theme.of(context)
                        .textTheme
                        .montserratNavigationPrimaryMedium
                        .copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: AppColors.primaryBlue),
                  ),
                  const Gap(8),
                  Text(
                    contact.firstName,
                    style: Theme.of(context)
                        .textTheme
                        .montserratFormsField
                        .copyWith(
                          color: AppColors.black,
                        ),
                  ),
                  const Gap(8),
                  Text(
                    contact.lastName,
                    style: Theme.of(context)
                        .textTheme
                        .montserratFormsField
                        .copyWith(
                          color: AppColors.black,
                        ),
                  ),
                  // if (contact.jobTitle.isNotEmpty) ...[
                  //   const SizedBox(height: 4),
                  //   Text(
                  //     contact.jobTitle,
                  //     style: Theme.of(context)
                  //         .textTheme
                  //         .montserratNavigationPrimaryMedium
                  //         .copyWith(
                  //           color: AppColors.black.withValues(alpha: 0.6),
                  //           fontSize: 14,
                  //         ),
                  //   ),
                  // ],
                  // if (contact.phone.isNotEmpty) ...[
                  //   const SizedBox(height: 4),
                  //   Text(
                  //     contact.phone,
                  //     style: Theme.of(context)
                  //         .textTheme
                  //         .montserratNavigationPrimaryMedium
                  //         .copyWith(
                  //           color: AppColors.black.withValues(alpha: 0.8),
                  //           fontSize: 14,
                  //         ),
                  //   ),
                  // ],
                ],
              ),
            ),
            // Action buttons
            const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // if (contact.phone.isNotEmpty)
                //   IconButton(
                //     onPressed: () => _callContact(contact.phone),
                //     icon: const Icon(Icons.phone, color: AppColors.primaryBlue),
                //   ),
                // if (contact.email.isNotEmpty)
                //   IconButton(
                //     onPressed: () => _emailContact(contact.email),
                //     icon: const Icon(Icons.email, color: AppColors.primaryBlue),
                //   ),
                // Disclosure indicator
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.black,
                  size: 16,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentItem(StoreCommentData comment, BuildContext context) {
    return GestureDetector(
      onTap: () => _editComment(comment, context),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Comment header
            Row(
              children: [
                // CircleAvatar(
                //   radius: 26,
                //   backgroundColor: AppColors.primaryBlue.withValues(alpha: 0.1),
                //   backgroundImage: comment.userProfileImageUrl != null && comment.userProfileImageUrl!.isNotEmpty
                //       ? NetworkImage(comment.userProfileImageUrl!)
                //       : null,
                //   child: comment.userProfileImageUrl == null || comment.userProfileImageUrl!.isEmpty
                //       ? Text(
                //           comment.userName != null && comment.userName!.isNotEmpty
                //               ? comment.userName![0].toUpperCase()
                //               : 'U',
                //           style: Theme.of(context)
                //               .textTheme
                //               .montserratNavigationPrimaryMedium
                //               .copyWith(
                //                 color: AppColors.primaryBlue,
                //                 fontWeight: FontWeight.w600,
                //                 fontSize: 12,
                //               ),
                //         )
                //       : null,
                // ),
                // const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        comment.userName ?? 'Unknown User',
                        style:
                            Theme.of(context).textTheme.montserratBold.copyWith(
                                  // fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                  color: AppColors.primaryBlue,
                                ),
                      ),
                    ],
                  ),
                ),
                Text(
                  comment.dateCreated != null && comment.dateCreated!.isNotEmpty
                      ? convertToDateFormat(comment.dateCreated!)
                      : '',
                  style: Theme.of(context)
                      .textTheme
                      .montserratParagraphSmall
                      .copyWith(color: AppColors.black
                          // fontSize: 12,e
                          ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            // Comment content
            Text(
              comment.comment ?? '',
              style: Theme.of(context)
                  .textTheme
                  .montserratParagraphSmall
                  .copyWith(color: AppColors.black
                      // fontSize: 12,e
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _editComment(
      StoreCommentData comment, BuildContext parentContext) async {
    final result = await parentContext.router.push(
      AddStoreCommentRoute(
        taskId: widget.taskId,
        comment: comment,
        isEditMode: true,
      ),
    );

    if (result == true) {
      // Comment was saved successfully, refresh the list
      if (mounted) {
        parentContext.read<StoreCommentsCubit>().fetchStoreComments(
              taskId: widget.taskId,
            );
      }
    }
  }
}

// Data models for store contacts (comments are now handled by StoreCommentData)
class StoreContact {
  final String id;
  final String name;
  final String role;
  final String phone;
  final String email;

  StoreContact({
    required this.id,
    required this.name,
    required this.role,
    required this.phone,
    required this.email,
  });
}
