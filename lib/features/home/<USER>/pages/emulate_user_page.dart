import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/globals/app_globals.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/emulate_user_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/emulate_user/emulate_user_cubit.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';

@RoutePage()
class EmulateUserPage extends StatefulWidget {
  const EmulateUserPage({super.key});

  @override
  State<EmulateUserPage> createState() => _EmulateUserPageState();
}

class _EmulateUserPageState extends State<EmulateUserPage> {
  final TextEditingController _searchController = TextEditingController();
  String _userName = 'User';

  @override
  void initState() {
    super.initState();
    // Fetch emulate users when page loads
    context.read<EmulateUserCubit>().fetchEmulateUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    context.read<EmulateUserCubit>().searchUsers(query);
  }

  void _clearSearch() {
    _searchController.clear();
    context.read<EmulateUserCubit>().clearSearch();
  }

  void _onUserSelected(EmulateUserEntity user) {
    ConfirmDialog.show(
      context: context,
      title: 'Emulate User',
      message: 'Are you sure you want to emulate ${user.displayName}?',
      confirmText: 'Emulate',
      cancelText: 'Cancel',
      onConfirm: () {
        context.read<EmulateUserCubit>().emulateUser(user);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2.withValues(alpha: 0.9),
      appBar: CustomAppBar(
        title: 'Active Users',
        showBackButton: true,
        actions: [
          SizedBox(
            height: 32,
            width: 96,
            child: AppButton(
              text: _userName,
              color: AppColors.primaryBlue,
              radius: 6,
              height: 36,
              onPressed: () async {
                // Store context reference before async gap
                final currentContext = context;

                // Check if currently emulating and return to original user
                final dataManager = sl<DataManager>();
                final isEmulating = await dataManager.isEmulating();

                if (mounted) {
                  if (isEmulating) {
                    // Clear global emulated user ID immediately
                    AppGlobals.clearEmulatedUserId();
                    currentContext
                        .read<EmulateUserCubit>()
                        .returnToOriginalUser();
                  } else {
                    SnackBarService.success(
                      context: currentContext,
                      message: 'Current User: $_userName',
                    );
                  }
                }
              },
            ),
          ),
          const Gap(12),
        ],
      ),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            // Search TextField
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: 'Search users...',
                  hintStyle: textTheme.montserratParagraphSmall.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w400,
                  ),
                  prefixIcon: Icon(
                    Icons.search_rounded,
                    color: Colors.grey[700],
                    size: 20,
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.close_rounded,
                            color: Colors.grey[500],
                            size: 18,
                          ),
                          onPressed: _clearSearch,
                          splashRadius: 16,
                        )
                      : null,
                  fillColor: Colors.white,
                  filled: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(
                      color: Colors.grey.shade400,
                      width: 1.5,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(
                      color: Colors.grey.shade400,
                      width: 1.5,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(
                      color: AppColors.primaryBlue,
                      width: 2.0,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                style: textTheme.montserratParagraphSmall.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            // Users List
            Expanded(
              child: BlocConsumer<EmulateUserCubit, EmulateUserState>(
                listener: (context, state) {
                  if (state is EmulateUserError) {
                    SnackBarService.error(
                      context: context,
                      message: state.message,
                    );
                  } else if (state is EmulateUserEmulationSuccess) {
                    // Check if this was a return to original user
                    if (state.apiResults.containsKey('return_to_original')) {
                      SnackBarService.success(
                        context: context,
                        message: 'Successfully returned to original user',
                      );
                    } else {
                      SnackBarService.success(
                        context: context,
                        message: 'Emulation completed',
                      );
                    }

                    // Optionally navigate back to dashboard
                    // context.router.pushAndClearStack(DashboardRoute());
                  } else if (state is EmulateUserEmulationError) {
                    SnackBarService.error(
                      context: context,
                      message: state.message,
                    );
                  } else if (state is EmulateUserLoaded) {
                    setState(() {
                      _userName = state.name ?? 'User';
                    });
                  }
                },
                builder: (context, state) {
                  // Show loading spinner only for initial fetch
                  if (state is EmulateUserLoading) {
                    return const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.primaryBlue,
                      ),
                    );
                  }

                  // For emulation in progress, show overlay on top of existing list
                  Widget? loadingOverlay;
                  if (state is EmulateUserEmulating) {
                    loadingOverlay = Container(
                      color: Colors.white,
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const CircularProgressIndicator(
                                color: AppColors.primaryBlue,
                              ),
                              const Gap(16),
                              Text(
                                'Emulating ${state.user.displayName}',
                                style: textTheme.montserratTitleSmall.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const Gap(8),
                              Text(
                                state.currentApi,
                                style:
                                    textTheme.montserratParagraphSmall.copyWith(
                                  color: Colors.grey[600],
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }

                  if (state is EmulateUserError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading users',
                            style: textTheme.montserratTitleSmall.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: textTheme.montserratParagraphSmall.copyWith(
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              context
                                  .read<EmulateUserCubit>()
                                  .fetchEmulateUsers();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryBlue,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  // Main content
                  Widget mainContent = const SizedBox.shrink();

                  if (state is EmulateUserLoaded) {
                    if (state.filteredUsers.isEmpty) {
                      mainContent = Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              state.searchQuery.isEmpty
                                  ? 'No users available'
                                  : 'No users found',
                              style: textTheme.montserratTitleSmall.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                            if (state.searchQuery.isNotEmpty) ...[
                              const SizedBox(height: 8),
                              Text(
                                'Try adjusting your search',
                                style:
                                    textTheme.montserratParagraphSmall.copyWith(
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ],
                        ),
                      );
                    } else {
                      mainContent = ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        itemCount: state.filteredUsers.length,
                        itemBuilder: (context, index) {
                          final user = state.filteredUsers[index];
                          return _buildUserItem(user, textTheme);
                        },
                      );
                    }
                  }
                  // For EmulateUserEmulating, also show the loaded list if we have previous data
                  else if (state is EmulateUserEmulating) {
                    // Try to get previous loaded state from cubit
                    // For now, show empty but this preserves the overlay
                    mainContent = const SizedBox.shrink();
                  }

                  return Stack(
                    children: [
                      mainContent,
                      if (loadingOverlay != null) loadingOverlay,
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserItem(EmulateUserEntity user, TextTheme textTheme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 4,
        ),

        title: Text(
          user.displayName,
          style: textTheme.montserratTitleExtraSmall
              .copyWith(fontWeight: FontWeight.w600, color: AppColors.black),
        ),
        // subtitle: Column(
        //   crossAxisAlignment: CrossAxisAlignment.start,
        //   children: [
        //     if (user.email != null && user.email!.isNotEmpty) ...[
        //       const SizedBox(height: 4),
        //       Text(
        //         user.email!,
        //         style: textTheme.montserratParagraphSmall.copyWith(
        //           color: Colors.black
        //         ),
        //       ),
        //     ],
        //     if (user.state != null || user.contractor != null) ...[
        //       const SizedBox(height: 4),
        //       Row(
        //         children: [
        //           if (user.state != null && user.state!.isNotEmpty) ...[
        //             Icon(
        //               Icons.location_on,
        //               size: 14,
        //               color: Colors.grey[500],
        //             ),
        //             const SizedBox(width: 4),
        //             Text(
        //               user.state!,
        //               style: textTheme.montserratParagraphXsmall.copyWith(
        //                 color: Colors.grey[500],
        //               ),
        //             ),
        //           ],
        //           if (user.state != null &&
        //               user.state!.isNotEmpty &&
        //               user.contractor != null &&
        //               user.contractor!.isNotEmpty) ...[
        //             const SizedBox(width: 8),
        //             Text(
        //               '•',
        //               style: TextStyle(color: Colors.grey[400]),
        //             ),
        //             const SizedBox(width: 8),
        //           ],
        //           if (user.contractor != null && user.contractor!.isNotEmpty) ...[
        //             Icon(
        //               Icons.business,
        //               size: 14,
        //               color: Colors.grey[500],
        //             ),
        //             const SizedBox(width: 4),
        //             Flexible(
        //               child: Text(
        //                 user.contractor!,
        //                 style: textTheme.montserratParagraphXsmall.copyWith(
        //                   color: Colors.grey[500],
        //                 ),
        //                 overflow: TextOverflow.ellipsis,
        //               ),
        //             ),
        //           ],
        //         ],
        //       ),
        //     ],
        //   ],
        // ),
        trailing: Icon(
          Icons.chevron_right,
          color: Colors.grey[400],
        ),
        onTap: () => _onUserSelected(user),
      ),
    );
  }
}
