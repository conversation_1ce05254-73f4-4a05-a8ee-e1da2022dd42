import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/create_task_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/create_task/create_task_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/create_task/create_task_state.dart';
import 'package:storetrack_app/features/home/<USER>/models/coverage_store_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/coverage_client_model.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/searchable_bottom_sheet.dart';
import 'package:storetrack_app/shared/theme/app_date_picker_theme.dart';

@RoutePage()
class CreateTaskPage extends StatefulWidget {
  const CreateTaskPage({super.key});

  @override
  State<CreateTaskPage> createState() => _CreateTaskPageState();
}

class _CreateTaskPageState extends State<CreateTaskPage> {
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _durationController = TextEditingController();
  final TextEditingController _storesController = TextEditingController();
  final TextEditingController _clientController = TextEditingController();

  DateTime? _selectedDate;
  int? _selectedStoreId;
  int? _selectedClientId;

  // Real data from API
  List<CoverageStore> _availableStores = [];
  List<CoverageClient> _availableClients = [];
  bool _isLoadingStores = true;
  bool _isLoadingClients = true;

  // Add visibility control for client selection
  bool _showClientSelection = true;

  // Store the Cubit instance to ensure consistency
  late CreateTaskCubit _createTaskCubit;

  @override
  void initState() {
    super.initState();
    _createTaskCubit = sl<CreateTaskCubit>();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    try {
      print('🚀 Starting _loadInitialData...');
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? "";
      final token = await dataManager.getAuthToken() ?? "";

      print('👤 User ID: $userId, Token length: ${token.length}');
      if (mounted && userId.isNotEmpty && token.isNotEmpty) {
        // Fetch stores and clients using the same Cubit instance
        print('📦 Calling fetchCoverageStores...');
        _createTaskCubit.fetchCoverageStores(
          userId: userId,
          token: token,
        );

        // Give a small delay before fetching clients
        await Future.delayed(const Duration(milliseconds: 100));

        if (mounted) {
          print('👥 Calling fetchCoverageClients...');
          _createTaskCubit.fetchCoverageClients(
            userId: userId,
            token: token,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error loading initial data: ${e.toString()}',
        );
      }
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    _durationController.dispose();
    _storesController.dispose();
    _clientController.dispose();
    _createTaskCubit.close();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: AppDatePickerTheme.getTheme(context),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  Future<void> _selectStores() async {
    // Check if stores are still loading
    if (_isLoadingStores) {
      SnackBarService.warning(
        context: context,
        message: 'Please wait, stores are still loading...',
      );
      return;
    }

    // Check if no stores available
    if (_availableStores.isEmpty) {
      SnackBarService.warning(
        context: context,
        message:
            'No stores available. Please check your internet connection and reload.',
      );
      return;
    }

    // Use bottom sheet for store selection
    final int? selected = await _showStoreBottomSheet();

    if (selected != null) {
      setState(() {
        _selectedStoreId = selected;
        _storesController.text = _availableStores
            .firstWhere((store) => store.storeId == selected)
            .displayName;
      });
    }
  }

  Future<void> _selectClient() async {
    // Check if clients are still loading
    if (_isLoadingClients) {
      SnackBarService.warning(
        context: context,
        message: 'Please wait, clients are still loading...',
      );
      return;
    }

    // Check if no clients available
    if (_availableClients.isEmpty) {
      SnackBarService.warning(
        context: context,
        message:
            'No clients available. Please check your internet connection and reload.',
      );
      return;
    }

    // Use bottom sheet for client selection
    final int? selected = await _showClientBottomSheet();

    if (selected != null) {
      setState(() {
        _selectedClientId = selected;
        _clientController.text = _availableClients
            .firstWhere((client) => client.clientId == selected)
            .displayName;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _createTaskCubit,
      child: PopScope(
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) {
            _triggerSyncAfterTaskCreation();
          }
        },
        child: Scaffold(
          backgroundColor: AppColors.lightGrey1,
          appBar: const CustomAppBar(
            title: 'Create Tasks',
          ),
          body: BlocConsumer<CreateTaskCubit, CreateTaskState>(
            listener: (context, state) {
              if (state is CreateTaskSuccess) {
                SnackBarService.success(
                  context: context,
                  message: 'Task created!',
                );

                // Reset only store selection to allow batch creation
                setState(() {
                  _selectedStoreId = null;
                  _storesController.clear();
                });

                // Sync will be triggered when user exits the page
              } else if (state is CreateTaskError) {
                // Reset loading states on error
                setState(() {
                  _isLoadingStores = false;
                  _isLoadingClients = false;
                });
                SnackBarService.error(
                  context: context,
                  message: state.message,
                );
              } else if (state is CoverageStoresLoaded) {
                logger(
                    '📦 Processing CoverageStoresLoaded with ${state.response.data?.coverageStores.length ?? 0} stores');
                setState(() {
                  _availableStores = state.response.data?.coverageStores ?? [];
                  _isLoadingStores = false;
                });
              } else if (state is CoverageClientsLoaded) {
                logger(
                    '👥 Processing CoverageClientsLoaded with ${state.response.data?.coverageClients.length ?? 0} clients');
                setState(() {
                  _availableClients =
                      state.response.data?.coverageClients ?? [];
                  _isLoadingClients = false;

                  // Client visibility logic from Android
                  if (_availableClients.length == 1) {
                    _showClientSelection = false;
                    // Auto-select the single client
                    _selectedClientId = _availableClients.first.clientId;
                    _clientController.text =
                        _availableClients.first.displayName;
                  } else if (_availableClients.isEmpty) {
                    _showClientSelection = false;
                    SnackBarService.error(
                      context: context,
                      message: 'No Coverage Clients Found.',
                    );
                  } else {
                    _showClientSelection = true;
                  }
                });
              }
            },
            builder: (context, state) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Form fields
                    Column(
                      children: [
                        // Date picker
                        _buildModernTextField(
                          label: 'Task Date',
                          controller: _dateController,
                          hintText: 'Select task date',
                          prefixIcon: Icons.calendar_today_outlined,
                          readOnly: true,
                          onTap: _selectDate,
                        ),
                        const Gap(24),
                        // Task duration
                        _buildModernTextField(
                          label: 'Duration (minutes)',
                          controller: _durationController,
                          hintText: 'Enter task duration',
                          prefixIcon: Icons.access_time_outlined,
                          keyboardType: TextInputType.number,
                        ),
                        const Gap(24),
                        // Store selection
                        _buildModernTextField(
                          label: 'Stores',
                          controller: _storesController,
                          hintText: _isLoadingStores
                              ? 'Loading stores...'
                              : _availableStores.isEmpty
                                  ? 'No stores available'
                                  : 'Select stores',
                          prefixIcon: Icons.store_outlined,
                          readOnly: true,
                          onTap: _selectStores,
                          suffixIcon: _isLoadingStores
                              ? const Padding(
                                  padding: EdgeInsets.all(4),
                                  child: SizedBox(
                                    width: 12,
                                    height: 12,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 1,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        AppColors.primaryBlue,
                                      ),
                                    ),
                                  ),
                                )
                              : null,
                        ),
                        const Gap(24),
                        // Client selection - conditionally visible
                        if (_showClientSelection) ...[
                          _buildModernTextField(
                            label: 'Client',
                            controller: _clientController,
                            hintText: _isLoadingClients
                                ? 'Loading clients...'
                                : _availableClients.isEmpty
                                    ? 'No clients available'
                                    : 'Select client',
                            prefixIcon: Icons.business_outlined,
                            readOnly: true,
                            onTap: _selectClient,
                            suffixIcon: _isLoadingClients
                                ? const Padding(
                                    padding: EdgeInsets.all(4),
                                    child: SizedBox(
                                      width: 12,
                                      height: 12,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 1,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                          AppColors.primaryBlue,
                                        ),
                                      ),
                                    ),
                                  )
                                : null,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
          floatingActionButton: BlocBuilder<CreateTaskCubit, CreateTaskState>(
            builder: (context, state) {
              return Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 24),
                child: AppButton(
                  text: state is CreateTaskLoading
                      ? 'Creating Tasks...'
                      : 'Create Tasks',
                  onPressed: state is CreateTaskLoading ? () {} : _submitTasks,
                  color: state is CreateTaskLoading
                      ? AppColors.midGrey
                      : AppColors.primaryBlue,
                  height: 56,
                ),
              );
            },
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
        ),
      ),
    );
  }

  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    String? hintText,
    IconData? prefixIcon,
    String? Function(String?)? validator,
    bool readOnly = false,
    VoidCallback? onTap,
    Widget? suffixIcon,
  }) {
    final bool hasValue = controller.text.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (prefixIcon != null) ...[
              Icon(
                prefixIcon,
                color: AppColors.primaryBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
            ],
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'Montserrat',
                color: AppColors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: readOnly ? onTap : null,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: hasValue
                    ? AppColors.primaryBlue.withValues(alpha: 0.4)
                    : AppColors.black20,
                width: hasValue ? 1.5 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: hasValue
                  ? AppColors.primaryBlue.withValues(alpha: 0.05)
                  : Colors.white,
            ),
            child: readOnly
                ? Row(
                    children: [
                      Expanded(
                        child: Text(
                          controller.text.isNotEmpty
                              ? controller.text
                              : (hintText ?? ''),
                          style: TextStyle(
                            fontSize: 16,
                            fontFamily: 'Montserrat',
                            fontWeight:
                                hasValue ? FontWeight.w500 : FontWeight.w400,
                            color: hasValue
                                ? AppColors.black
                                : AppColors.blackTint1,
                          ),
                        ),
                      ),
                      if (suffixIcon != null) suffixIcon,
                    ],
                  )
                : TextFormField(
                    controller: controller,
                    keyboardType: keyboardType,
                    maxLines: 1,
                    validator: validator,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: hintText,
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                      hintStyle: const TextStyle(
                        fontSize: 16,
                        fontFamily: 'Montserrat',
                        fontWeight: FontWeight.w400,
                        color: AppColors.blackTint1,
                      ),
                    ),
                    style: const TextStyle(
                      fontSize: 16,
                      fontFamily: 'Montserrat',
                      fontWeight: FontWeight.w500,
                      color: AppColors.black,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Future<void> _submitTasks() async {
    if (_selectedDate == null) {
      SnackBarService.warning(
        context: context,
        message: 'Please select a date',
      );
      return;
    }

    if (_durationController.text.isEmpty) {
      SnackBarService.warning(
        context: context,
        message: 'Please enter task duration',
      );
      return;
    }

    if (_selectedStoreId == null) {
      SnackBarService.warning(
        context: context,
        message: 'Please select a store',
      );
      return;
    }

    if (_selectedClientId == null) {
      // Default client selection logic from Android
      if (_availableClients.isNotEmpty) {
        _selectedClientId = _availableClients.first.clientId;
      } else {
        SnackBarService.warning(
          context: context,
          message: 'Please select a client',
        );
        return;
      }
    }

    try {
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? "";
      final token = await dataManager.getAuthToken() ?? "";

      final request = CreateTaskRequestEntity(
        taskDuration: int.parse(_durationController.text),
        userId: userId,
        token: token,
        scheduledDate: DateFormat('yyyy-MM-dd').format(_selectedDate!),
        coverageStores: _selectedStoreId != null
            ? [
                _availableStores
                    .firstWhere((store) => store.storeId == _selectedStoreId)
              ]
            : [],
        clientId: _selectedClientId!,
      );

      if (mounted) {
        _createTaskCubit.createTasks(request);
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Error creating tasks: ${e.toString()}',
        );
      }
    }
  }

  Future<int?> _showStoreBottomSheet() async {
    final selectedStore = await showSearchableBottomSheet<CoverageStore>(
      context: context,
      title: 'Select Stores',
      searchHint: 'Search stores...',
      items: _availableStores,
      itemDisplayText: (store) => store.displayName,
      searchFilter: (store, query) => store.displayName,
      isSelected: (store, _) => _selectedStoreId == store.storeId,
    );

    return selectedStore?.storeId;
  }

  Future<void> _triggerSyncAfterTaskCreation() async {
    try {
      if (mounted) {
        await SyncService().sync(context: context);
      }
    } catch (e) {
      // Log sync error but don't block user flow
      logger('Sync failed after task creation: $e');
    }
  }

  Future<int?> _showClientBottomSheet() async {
    final selectedClient = await showSearchableBottomSheet<CoverageClient>(
      context: context,
      title: 'Select Client',
      searchHint: 'Search clients...',
      items: _availableClients,
      itemDisplayText: (client) => client.displayName,
      searchFilter: (client, query) => client.displayName,
      isSelected: (client, _) => _selectedClientId == client.clientId,
    );

    return selectedClient?.clientId;
  }
}
