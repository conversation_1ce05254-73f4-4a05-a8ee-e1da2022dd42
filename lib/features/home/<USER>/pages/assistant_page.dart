import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class AssistantPage extends StatefulWidget {
  const AssistantPage({super.key});

  @override
  State<AssistantPage> createState() => _AssistantPageState();
}

class _AssistantPageState extends State<AssistantPage> {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [
    ChatMessage(
      isUser: false,
      message: "Do you need help with this task?",
      timestamp: "1 min ago",
      taskCard: TaskCard(
        title: "PEPSI CO",
        subtitle: "2025 Coles Multipack Stickering - WC 17th Feb 1",
        location: "COLES, BONDI JUNCTION",
        timeEstimate: "30m",
      ),
    ),
    ChatMessage(
      isUser: true,
      message: "I'm not sure how to sign in to <PERSON><PERSON>",
      timestamp: "2 mins ago",
    ),
    ChatMessage(
      isUser: false,
      message:
          "Here is the sign-in guide for <PERSON><PERSON> inside the documents section of the task",
      timestamp: "1 min ago",
      documentReference: DocumentReference(
        title: "Coles - Visitor sign in guide",
      ),
      additionalMessage:
          "If this isn't what you're looking for, is there another issue with signing in?",
    ),
    ChatMessage(
      isUser: true,
      message: "Yes",
      timestamp: "1 min ago",
    ),
    ChatMessage(
      isUser: false,
      message:
          "Please describe what the issue is with signing in. It will be passed on to John, your manager.",
      timestamp: "1 min ago",
    ),
    ChatMessage(
      isUser: true,
      message:
          "I'm trying to sign-in to Coles in Bondi Junction but can't find the QR code in the store. The store manager isn't here and no staff seem to be able to help. I'm not sure I will be able tro complete this task.",
      timestamp: "1 min ago",
    ),
  ];

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Assistant',
        backgroundColor: Colors.transparent,
        onBackPressed: () {
          // tabsRouter.setActiveIndex(0);
        },
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined,
                color: AppColors.black),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.copy_outlined, color: AppColors.black),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.more_horiz, color: AppColors.black),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        children: [
          // Date header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            width: double.infinity,
            child: const Text(
              'Mon 17 Feb',
              style: TextStyle(
                color: AppColors.primaryBlue,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),

          // Chat messages
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return ChatBubble(message: message);
              },
            ),
          ),

          // Message input
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: TextField(
                      controller: _messageController,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 15),
                        hintText: 'Type a message...',
                        hintStyle: TextStyle(color: Colors.grey),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 40,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: IconButton(
                    icon: SvgPicture.asset(
                      'assets/icons/chevron_right_icon.svg',
                      colorFilter:
                          const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                    ),
                    onPressed: _sendMessage,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    setState(() {
      _messages.add(ChatMessage(
        isUser: true,
        message: _messageController.text,
        timestamp: "Just now",
      ));
    });

    _messageController.clear();
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatBubble({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            message.isUser ? _buildUserMessage() : _buildAssistantMessage(),
      ),
    );
  }

  List<Widget> _buildUserMessage() {
    return [
      Flexible(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.lightGrey2,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.message,
                    style: const TextStyle(
                      color: AppColors.black,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message.timestamp,
                    style: TextStyle(
                      color: AppColors.black.withValues(alpha: 0.6),
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      const SizedBox(width: 16),
      Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.lightGrey2,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Center(
          child: SvgPicture.asset(
            'assets/icons/message_icon.svg',
            width: 20,
            height: 20,
            colorFilter:
                const ColorFilter.mode(AppColors.black, BlendMode.srcIn),
          ),
        ),
      ),
    ];
  }

  List<Widget> _buildAssistantMessage() {
    return [
      Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.primaryBlue,
          borderRadius: BorderRadius.circular(100),
        ),
        child: Center(
          child: SvgPicture.asset(
            'assets/icons/chatbot_icon.svg',
            width: 20,
            height: 20,
            colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
          ),
        ),
      ),
      const SizedBox(width: 16),
      Flexible(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFD9EBF6), // Light blue color from design
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.message,
                    style: const TextStyle(
                      color: AppColors.black,
                      fontSize: 14,
                    ),
                  ),
                  if (message.taskCard != null) ...[
                    const SizedBox(height: 16),
                    _buildTaskCard(message.taskCard!),
                  ],
                  if (message.documentReference != null) ...[
                    const SizedBox(height: 16),
                    _buildDocumentReference(message.documentReference!),
                  ],
                  if (message.additionalMessage != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      message.additionalMessage!,
                      style: const TextStyle(
                        color: AppColors.black,
                        fontSize: 14,
                      ),
                    ),
                  ],
                  const SizedBox(height: 4),
                  Align(
                    alignment: Alignment.bottomRight,
                    child: Text(
                      message.timestamp,
                      style: TextStyle(
                        color: AppColors.black.withValues(alpha: 0.6),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ];
  }

  Widget _buildTaskCard(TaskCard taskCard) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.black.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title row with time estimate
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                taskCard.title,
                style: const TextStyle(
                  color: AppColors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Row(
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppColors.blackTint1,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    taskCard.timeEstimate,
                    style: const TextStyle(
                      color: AppColors.blackTint1,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            taskCard.subtitle,
            style: const TextStyle(
              color: AppColors.black,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.calendar_today_outlined,
                size: 16,
                color: AppColors.black,
              ),
              const SizedBox(width: 8),
              Text(
                taskCard.location,
                style: const TextStyle(
                  color: AppColors.black,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentReference(DocumentReference documentReference) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.black.withValues(alpha: 0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          SvgPicture.asset(
            'assets/icons/report_icon.svg',
            width: 24,
            height: 24,
            colorFilter:
                const ColorFilter.mode(AppColors.black, BlendMode.srcIn),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              documentReference.title,
              style: const TextStyle(
                color: AppColors.black,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ChatMessage {
  final bool isUser;
  final String message;
  final String timestamp;
  final TaskCard? taskCard;
  final DocumentReference? documentReference;
  final String? additionalMessage;

  ChatMessage({
    required this.isUser,
    required this.message,
    required this.timestamp,
    this.taskCard,
    this.documentReference,
    this.additionalMessage,
  });
}

class TaskCard {
  final String title;
  final String subtitle;
  final String location;
  final String timeEstimate;

  TaskCard({
    required this.title,
    required this.subtitle,
    required this.location,
    required this.timeEstimate,
  });
}

class DocumentReference {
  final String title;

  DocumentReference({
    required this.title,
  });
}
