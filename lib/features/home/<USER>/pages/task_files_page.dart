import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/documents_content_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/task_details/brief_content_widget.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';

@RoutePage()
class TaskFilesPage extends StatelessWidget {
  final int taskId;
  final num storeId;
  final String sectionType;

  const TaskFilesPage({
    super.key,
    required this.taskId,
    required this.storeId,
    required this.sectionType,
  });

  @override
  Widget build(BuildContext context) {
    // Get task from Realm database
    final realm = RealmDatabase.instance.realm;
    final taskModel =
        realm.query<TaskDetailModel>('taskId == $taskId').firstOrNull;

    if (taskModel == null) {
      return Scaffold(
        appBar: CustomAppBar(
          title: _getTitle(),
          showBackButton: true,
        ),
        body: const Center(
          child: EmptyState(
            message: 'Task not found',
          ),
        ),
      );
    }

    // Convert model to entity
    final taskEntity = TaskDetailMapper.toEntity(taskModel);

    return Scaffold(
      appBar: CustomAppBar(
        title: _getTitle(),
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        child: _buildContent(taskEntity),
      ),
    );
  }

  String _getTitle() {
    switch (sectionType) {
      case 'documents':
        return 'Documents';
      case 'brief':
        return 'Brief';
      default:
        return 'Task Files';
    }
  }

  Widget _buildContent(entities.TaskDetail task) {
    Widget content;
    double topPadding;

    switch (sectionType) {
      case 'documents':
        content = DocumentsContentWidget(task: task);
        topPadding = 12;
        break;
      case 'brief':
        content = BriefContentWidget(task: task);
        topPadding = 20;
        break;
      default:
        content = const EmptyState(
          message: 'Invalid section type',
        );
        topPadding = 20;
    }

    return Padding(
      padding: EdgeInsets.only(top: topPadding),
      child: content,
    );
  }
}
