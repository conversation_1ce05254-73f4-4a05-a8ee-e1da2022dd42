import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/entities/previous_tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class PreviousTaskFormPage extends StatefulWidget {
  final PreviousTaskEntity previousTask;
  final entities.Form form;

  const PreviousTaskFormPage({
    super.key,
    required this.previousTask,
    required this.form,
  });

  @override
  State<PreviousTaskFormPage> createState() => _PreviousTaskFormPageState();
}

class _PreviousTaskFormPageState extends State<PreviousTaskFormPage> {
  /// Count photos for a specific questionId
  int _getPhotoCountForQuestion(num? questionId) {
    if (questionId == null || widget.previousTask.photoFolder == null) {
      return 0;
    }

    int photoCount = 0;
    for (final folder in widget.previousTask.photoFolder!) {
      if (folder.photos != null) {
        for (final photo in folder.photos!) {
          if (photo.questionId == questionId) {
            photoCount++;
          }
        }
      }
    }

    return photoCount;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Previous Task Form',
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    // Process form data to organize by headers
    final organizedData = _organizeFormData();

    if (organizedData.isEmpty) {
      return const Center(
        child: Text(
          'No form data available',
          style: AppTypography.montserratParagraphSmall,
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(12),
      itemCount: organizedData.length,
      itemBuilder: (context, index) {
        final headerData = organizedData[index];
        return _buildHeaderSection(headerData);
      },
    );
  }

  Widget _buildHeaderSection(HeaderData headerData) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: AppColors.blackTint1,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Question',
                    style: AppTypography.montserratTitleExtraSmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  'Response',
                  style: AppTypography.montserratTitleExtraSmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Header title (section header like "CONGO BRANDS Range")
          if (headerData.headerTitle.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                color: AppColors.primaryBlue,
              ),
              child: Text(
                headerData.headerTitle.toUpperCase(),
                style: AppTypography.montserratTitleExtraSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
              ),
            ),

          // Question parts
          ...headerData.questionParts.map((questionPartData) =>
              _buildQuestionPartSection(questionPartData)),
        ],
      ),
    );
  }

  Widget _buildQuestionPartSection(QuestionPartData questionPartData) {
    return Column(
      children: [
        // Question part description (product name)
        if (questionPartData.description.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.lightGrey1,
              border: Border(
                left: BorderSide(
                  color: AppColors.primaryBlue.withValues(alpha: 0.3),
                  width: 3,
                ),
              ),
            ),
            child: Text(
              questionPartData.description,
              style: AppTypography.montserratTitleExtraSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
          ),

        // Measurements (individual measurement rows)
        ...questionPartData.measurements
            .map((measurementData) => _buildMeasurementRow(measurementData)),
      ],
    );
  }

  Widget _buildMeasurementRow(MeasurementData measurementData) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Text(
              measurementData.description,
              style: AppTypography.montserratParagraphSmall.copyWith(
                color: AppColors.black,
                fontSize: 13,
                height: 1.3,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: AppColors.primaryBlue.withValues(alpha: 0.3),
                    width: 0.5,
                  ),
                ),
                child: Text(
                  measurementData.response,
                  style: AppTypography.montserratParagraphSmall.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              if (measurementData.photoCount > 0) ...[
                const SizedBox(height: 3),
                _buildPhotoIndicator(measurementData.photoCount),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Build photo count indicator with icon
  Widget _buildPhotoIndicator(int photoCount) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(3),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: AppColors.primaryBlue.withValues(alpha: 0.3),
              width: 0.5,
            ),
          ),
          child: const Icon(
            Icons.photo,
            size: 16,
            color: AppColors.primaryBlue,
          ),
        ),
        const SizedBox(width: 6),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: AppColors.primaryBlue.withValues(alpha: 0.3),
              width: 0.5,
            ),
          ),
          child: Text(
            '$photoCount photo${photoCount == 1 ? '' : 's'}',
            style: AppTypography.montserratTableSmall.copyWith(
              color: AppColors.primaryBlue,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  List<HeaderData> _organizeFormData() {
    final List<HeaderData> organizedData = [];

    if (widget.form.questions == null || widget.form.questions!.isEmpty) {
      return organizedData;
    }

    // Process each question as a section
    for (final question in widget.form.questions!) {
      final headerData = HeaderData(
        headerTitle: question.questionDescription ?? 'Unknown Section',
        questionParts: [],
      );

      // Process question parts (these are the individual items)
      if (question.questionParts != null &&
          question.questionParts!.isNotEmpty) {
        for (final questionPart in question.questionParts!) {
          final questionPartData = QuestionPartData(
            description: questionPart.questionpartDescription ?? 'Unknown Item',
            measurements: [],
          );

          // Process measurements for this question part
          if (question.measurements != null) {
            for (final measurement in question.measurements!) {
              // Find corresponding answer for this specific combination
              final answer = _findAnswerForMeasurement(
                measurement.measurementId,
                questionPart.questionpartId,
              );

              // Only include measurements with valid answers
              if (_hasValidAnswer(answer)) {
                final photoCount =
                    _getPhotoCountForQuestion(question.questionId);

                questionPartData.measurements.add(MeasurementData(
                  description: measurement.measurementDescription ??
                      'Unknown Measurement',
                  response: answer ?? 'Not Answered',
                  photoCount: photoCount,
                  questionId: question.questionId,
                ));
              }
            }
          }

          // Only add question part if it has measurements with valid answers
          if (questionPartData.measurements.isNotEmpty) {
            headerData.questionParts.add(questionPartData);
          }
        }
      } else {
        // If no question parts, create measurements directly under the question
        if (question.measurements != null) {
          final questionPartData = QuestionPartData(
            description: '', // Empty description for direct measurements
            measurements: [],
          );

          for (final measurement in question.measurements!) {
            final answer =
                _findAnswerForMeasurement(measurement.measurementId, null);

            // Only include measurements with valid answers
            if (_hasValidAnswer(answer)) {
              final photoCount = _getPhotoCountForQuestion(question.questionId);

              questionPartData.measurements.add(MeasurementData(
                description:
                    measurement.measurementDescription ?? 'Unknown Measurement',
                response: answer ?? 'Not Answered',
                photoCount: photoCount,
                questionId: question.questionId,
              ));
            }
          }

          // Only add question part if it has measurements with valid answers
          if (questionPartData.measurements.isNotEmpty) {
            headerData.questionParts.add(questionPartData);
          }
        }
      }

      if (headerData.questionParts.isNotEmpty) {
        organizedData.add(headerData);
      }
    }

    return organizedData;
  }

  String? _findAnswerForMeasurement(num? measurementId, num? questionPartId) {
    if (measurementId == null || widget.form.questionAnswers == null) {
      return null;
    }

    for (final answer in widget.form.questionAnswers!) {
      if (answer.measurementId == measurementId) {
        // If questionPartId is provided, match it as well for more precise matching
        if (questionPartId != null && answer.questionpartId != questionPartId) {
          continue;
        }
        return answer.measurementTextResult ?? 'Not Answered';
      }
    }

    return null;
  }

  /// Check if a measurement has a valid non-empty answer
  /// Returns false for null, empty string, or "-" responses
  bool _hasValidAnswer(String? answer) {
    if (answer == null || answer.isEmpty || answer.trim() == '-') {
      return false;
    }
    return true;
  }
}

// Data classes for organizing form information
class HeaderData {
  final String headerTitle;
  final List<QuestionPartData> questionParts;

  HeaderData({
    required this.headerTitle,
    required this.questionParts,
  });
}

class QuestionPartData {
  final String description;
  final List<MeasurementData> measurements;

  QuestionPartData({
    required this.description,
    required this.measurements,
  });
}

class MeasurementData {
  final String description;
  final String response;
  final int photoCount;
  final num? questionId;

  MeasurementData({
    required this.description,
    required this.response,
    required this.photoCount,
    this.questionId,
  });
}
