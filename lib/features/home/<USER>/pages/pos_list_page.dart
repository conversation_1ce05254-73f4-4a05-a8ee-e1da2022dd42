import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/date_time_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/pos_response_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/entities/pos_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/pos_response_item_mapper.dart';

@RoutePage()
class PosListPage extends StatefulWidget {
  const PosListPage({super.key});

  @override
  State<PosListPage> createState() => _PosListPageState();
}

class _PosListPageState extends State<PosListPage> {
  List<Pos> posItems = [];
  Map<int, TaskDetailModel> taskLookup = {};
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final realm = RealmDatabase.instance.realm;

      // Get all POS response items from Realm
      final allPosItems = realm.all<PosResponseItemModel>().toList();

      // Get all task details from Realm for lookup
      final allTasks = realm.all<TaskDetailModel>().toList();

      // Create task lookup map for fast access
      final taskMap = <int, TaskDetailModel>{};
      for (final task in allTasks) {
        if (task.taskId != null) {
          taskMap[task.taskId!] = task;
        }
      }

      logger('POS items: ${allPosItems.length}');

      // Convert PosResponseItemModel to Pos entities using mapper
      final posEntities = PosResponseItemMapper.toEntityList(allPosItems);

      // Sort by scheduledDate first (ascending), then by storeName (ascending)
      posEntities.sort((a, b) {
        // First priority: scheduledDate (ascending)
        final dateComparison = (a.scheduledDate ?? DateTime(9999))
            .compareTo(b.scheduledDate ?? DateTime(9999));
        if (dateComparison != 0) return dateComparison;
        // Second priority: storeName (ascending)
        return (a.storeName ?? '').compareTo(b.storeName ?? '');
      });

      setState(() {
        posItems = posEntities;
        taskLookup = taskMap;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load POS data: ${e.toString()}';
        isLoading = false;
      });

      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to load POS data: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey1,
      appBar: CustomAppBar(
        title: 'POS Tasks',
        onBackPressed: () => context.router.maybePop(),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading POS tasks',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        errorMessage!,
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _initializeData,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _initializeData,
                  child: _buildTasksList(),
                ),
    );
  }

  Widget _buildTasksList() {
    if (posItems.isEmpty) {
      return const Center(
        child: EmptyState(message: 'No POS items available'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: posItems.length,
      itemBuilder: (context, index) {
        final posItem = posItems[index];
        return _buildTaskCard(posItem);
      },
    );
  }

  Widget _buildTaskCard(Pos posItem) {
    final textTheme = Theme.of(context).textTheme;

    // Get corresponding task details for sendTo and isPosMandatory
    final taskDetail =
        posItem.taskId != null ? taskLookup[posItem.taskId!] : null;

    // Calculate status based on received field
    final bool isConfirmed = posItem.received?.toLowerCase() == "true";
    final Color statusColor =
        isConfirmed ? AppColors.loginGreen : AppColors.loginRed;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border(
          left: BorderSide(
            color: statusColor,
            width: 4,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _onTaskTap(posItem),
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row with Store Name
              Row(
                children: [
                  Expanded(
                    child: Text(
                      posItem.storeName ?? 'Unknown Store',
                      style: textTheme.montserratSemiBold.copyWith(
                        fontSize: 16,
                        color: AppColors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Right side icons
                  Row(
                    children: [
                      // Address Icon (Store/Home) - from task details
                      if (taskDetail != null)
                        Container(
                          margin: const EdgeInsets.only(right: 8),
                          child: Icon(
                            taskDetail.sendTo == true
                                ? Icons.store
                                : Icons.home,
                            color: taskDetail.sendTo == true
                                ? AppColors.primaryBlue
                                : AppColors.loginGreen,
                            size: 18,
                          ),
                        ),

                      // Mandatory Task Indicator - from task details
                      if (taskDetail?.isPosMandatory == true)
                        const Icon(
                          Icons.priority_high_rounded,
                          color: AppColors.loginRed,
                          size: 18,
                        ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Client Name
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    posItem.clientName ?? 'Unknown Client',
                    style: const TextStyle(
                      fontFamily: AppFonts.montserrat,
                      fontSize: 14,
                      color: AppColors.blackTint1,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(DateTimeUtils.formatDateTimeForDisplay(
                      posItem.scheduledDate)),
                ],
              ),

              const SizedBox(height: 8),

              // Bottom Row with Task Cycle and Date Range
              Row(
                children: [
                  // Task Cycle
                  Expanded(
                    child: Text(
                      posItem.cycle ?? 'No Cycle',
                      style: const TextStyle(
                        fontFamily: AppFonts.montserrat,
                        fontSize: 13,
                        color: AppColors.black,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Task Date Range
                  Text(
                    _formatDateRange(posItem),
                    style: const TextStyle(
                      fontFamily: AppFonts.montserrat,
                      fontSize: 13,
                      color: AppColors.blackTint1,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDateRange(Pos posItem) {
    if (posItem.rangeStart != null && posItem.rangeEnd != null) {
      final startDate = DateFormat('MMM dd').format(posItem.rangeStart!);
      final endDate = DateFormat('MMM dd').format(posItem.rangeEnd!);
      return '$startDate - $endDate';
    } else if (posItem.rangeStart != null) {
      return DateFormat('MMM dd').format(posItem.rangeStart!);
    } else if (posItem.rangeEnd != null) {
      return DateFormat('MMM dd').format(posItem.rangeEnd!);
    }
    return 'No Date Range';
  }

  void _onTaskTap(Pos posItem) {
    // Get the corresponding task detail for navigation
    final taskDetail =
        posItem.taskId != null ? taskLookup[posItem.taskId!] : null;

    if (taskDetail != null) {
      // Convert TaskDetailModel to TaskDetail entity for navigation
      final taskEntity = TaskDetailMapper.toEntity(taskDetail);

      // Navigate to POS details page and refresh data when returning
      context.router.push(PosRoute(task: taskEntity)).then((_) {
        _refreshData();
      });
    } else {
      // Show error if no task detail found
      SnackBarService.error(
        context: context,
        message: 'Task details not found for this POS item',
      );
    }
  }

  Future<void> _refreshData() async {
    await _initializeData();
  }
}
