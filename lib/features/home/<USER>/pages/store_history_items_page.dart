import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/core/services/app_info_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/previous_tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_history/store_history_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/store_history/store_history_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/form_item_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class StoreHistoryItemsPage extends StatelessWidget {
  final int storeId;
  final int taskId;

  const StoreHistoryItemsPage({
    super.key,
    required this.storeId,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<StoreHistoryCubit>(),
      child: _StoreHistoryItemsPageContent(
        storeId: storeId,
        taskId: taskId,
      ),
    );
  }
}

class _StoreHistoryItemsPageContent extends StatefulWidget {
  final int storeId;
  final int taskId;

  const _StoreHistoryItemsPageContent({
    required this.storeId,
    required this.taskId,
  });

  @override
  State<_StoreHistoryItemsPageContent> createState() =>
      _StoreHistoryItemsPageContentState();
}

class _StoreHistoryItemsPageContentState
    extends State<_StoreHistoryItemsPageContent> {
  String actualDeviceUid = '';
  late String actualUserId;
  late String actualAppVersion;
  late String actualUserToken;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      // Get user ID, token, and device ID from DataManager
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();
      actualAppVersion = await sl<AppInfoService>().getVersion();

      // Fetch previous tasks with specific task ID
      if (mounted) {
        context.read<StoreHistoryCubit>().fetchPreviousTasks(
              PreviousTasksRequestEntity(
                userId: int.tryParse(actualUserId) ?? 0,
                token: actualUserToken,
                deviceUid: actualDeviceUid,
                appversion: actualAppVersion,
                taskId: widget.taskId,
                specificTaskId: widget.taskId,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize store history items: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<StoreHistoryCubit, StoreHistoryState>(
      listener: (context, state) {
        if (state is StoreHistoryError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey2,
          appBar: const CustomAppBar(
            title: 'Store History Items',
          ),
          body: _buildBody(state),
        );
      },
    );
  }

  Widget _buildBody(StoreHistoryState state) {
    if (state is StoreHistoryLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    } else if (state is StoreHistoryLoaded) {
      return state.previousTasks.isEmpty
          ? _buildEmptyState()
          : _buildTaskList(state.previousTasks);
    } else if (state is StoreHistoryError) {
      return _buildEmptyState();
    } else {
      return _buildEmptyState();
    }
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(0),
      child:
          const EmptyState(message: 'No history items available for this task'),
    );
  }

  Widget _buildTaskList(List<PreviousTaskEntity> previousTasks) {
    // Safety check for empty previousTasks
    if (previousTasks.isEmpty) {
      return _buildEmptyState();
    }

    final forms = previousTasks[0].forms ?? [];

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: forms.length,
      itemBuilder: (context, index) {
        final form = forms[index];
        return FormItemCard(
          form: form,
          onTap: () => _onFormTap(previousTasks[0], form),
        );
      },
    );
  }

  void _onFormTap(PreviousTaskEntity previousTask, dynamic form) {
    // Navigate to previous task form page
    if (mounted) {
      context.router.push(PreviousTaskFormRoute(
        previousTask: previousTask,
        form: form,
      ));
    }
  }
}
