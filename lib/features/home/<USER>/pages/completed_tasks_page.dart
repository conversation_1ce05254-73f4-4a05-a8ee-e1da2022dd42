import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/services/app_info_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/services/sync_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart'
    as submit_report;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/unschedule/unschedule_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/calendar_bottom_sheet.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/reorderable_store_list.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';

import '../../../../config/themes/app_colors.dart';
import '../../../../core/storage/data_manager.dart';
import '../blocs/unschedule/unschedule_state.dart';

@RoutePage()
class CompletedTasksPage extends StatefulWidget {
  const CompletedTasksPage({super.key});

  @override
  State<CompletedTasksPage> createState() => _CompletedTasksPageState();
}

class _CompletedTasksPageState extends State<CompletedTasksPage> {
  bool _isCheckboxMode = false;
  String actualDeviceUid = '';
  late String actualUserId;
  late String actualAppVersion;
  final List<String> actualTasksToUnschedule = [];
  late String actualUserToken;

  // Task lists
  List<TaskDetail> allApiTasks = []; // All tasks from API
  List<TaskDetail> completedTasks = []; // Filtered completed tasks
  List<TaskDetail> selectedItems = [];
  bool _areAllItemsSelected = false;

  // Calendar state
  DateTime selectedDate = DateTime.now();

  // Use a regular global key instead of a typed one to avoid errors
  final GlobalKey _storeListKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    _refreshData();
  }

  Future<void> _refreshData() async {
    try {
      // Get user ID, token, and device ID from DataManager
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();
      actualAppVersion = await sl<AppInfoService>().getVersion();

      // Fetch completed tasks
      if (mounted) {
        context.read<UnscheduleTaskCubit>().getData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Implementation of select all that works directly with the data
  void _selectAllItems() {
    setState(() {
      _areAllItemsSelected = !_areAllItemsSelected;

      if (_areAllItemsSelected) {
        // We're selecting all items - create a fresh list with all completed tasks
        selectedItems = List.from(completedTasks);
      } else {
        // We're deselecting all - clear the list
        selectedItems.clear();
      }
    });
  }

  // Filter tasks for completed tasks
  void _filterCompletedTasks() {
    // Filter completed tasks for display (tasks with status "Successful" or "Unsuccessful")
    completedTasks = allApiTasks
        .where((task) =>
            (task.taskStatus == "Successful" ||
                task.taskStatus == "Unsuccessful") &&
            task.taskId != 0 &&
            task.isOpen == false)
        .toList()
      ..sort((a, b) {
        // Handle null scheduledTimeStamp values - put them at the end
        if (a.scheduledTimeStamp == null && b.scheduledTimeStamp == null) {
          return 0;
        }
        if (a.scheduledTimeStamp == null) return 1;
        if (b.scheduledTimeStamp == null) return -1;
        // Sort by scheduledTimeStamp descending (newest first)
        return b.scheduledTimeStamp!.compareTo(a.scheduledTimeStamp!);
      });
  }

  // Build the scrollable content that includes task list
  Widget _buildScrollableContent(BuildContext context) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content section based on selected button
          _buildCompletedTaskList()
        ],
      ),
    );
  }

  // Build completed tasks list content
  Widget _buildCompletedTaskList() {
    if (completedTasks.isEmpty) {
      return const EmptyState(message: 'No completed tasks available');
    }

    return ReorderableStoreList(
      key: _storeListKey,
      tasks: completedTasks,
      isCalendarMode: _isCheckboxMode,
      showScheduledDate: false,
      showTickIndicator: true,
      showAllDisclosureIndicator: true,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: false,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
      onTaskTap: (task) {
        // Navigate to task details page
        context.router.push(TaskDetailsRoute(
          storeId: task.storeId ?? 0,
          taskId: task.taskId!.toInt(),
        ));
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UnscheduleTaskCubit, UnscheduleTaskState>(
        listener: (context, state) {
      if (state is UnscheduleTaskSuccess) {
        setState(() {
          var tasksResponse = state.tasksResponse;
          // Store all tasks from API
          allApiTasks = tasksResponse.addTasks ?? [];

          // Filter completed tasks for display
          _filterCompletedTasks();
        });
      } else if (state is UnscheduleTaskError) {
        // Show error message
        SnackBarService.error(
          context: context,
          message: state.message,
        );
      }
    }, builder: (context, state) {
      return Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: const CustomAppBar(
          title: 'Completed Tasks',
        ),
        body: state is UnscheduleTaskLoading
            ? const Center(child: CircularProgressIndicator())
            : state is UnscheduleTaskError
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading tasks',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: _initializeData,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  )
                : BlocListener<SyncCubit, SyncState>(
                    listener: (context, state) {
                      if (state is SyncSuccess) {
                        _refreshData();
                      }
                    },
                    child: RefreshIndicator(
                      onRefresh: _initializeData,
                      child: _buildScrollableContent(context),
                    ),
                  ),
        floatingActionButton: _isCheckboxMode &&
                completedTasks.isNotEmpty &&
                selectedItems.isNotEmpty
            ? Container(
                height: 56,
                decoration: BoxDecoration(
                  color: AppColors.midGrey,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.all(8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isCheckboxMode = false;
                          selectedItems.clear();
                          _areAllItemsSelected = false;
                        });
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.close_rounded,
                          color: AppColors.black,
                        ),
                      ),
                    ),
                    const Gap(8),
                    SizedBox(
                      width: 96,
                      child: AppButton(
                        text: _areAllItemsSelected
                            ? "Deselect all"
                            : "Select all",
                        color: Colors.white,
                        textColor: AppColors.black,
                        onPressed: _selectAllItems,
                        height: 40,
                      ),
                    ),
                    const Gap(8),
                    SizedBox(
                      width: 96,
                      child: AppButton(
                        text: "Reschedule",
                        color: AppColors.primaryBlue,
                        onPressed: () {
                          // Show calendar bottom sheet for rescheduling
                          _showCalendarBottomSheet(context);
                        },
                        height: 40,
                      ),
                    ),
                  ],
                ),
              )
            : null,
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      );
    });
  }

  void _handleSelectionChanged(
      List<TaskDetail> allItems, List<TaskDetail> selectedItems) {
    setState(() {
      this.selectedItems = selectedItems;
      _areAllItemsSelected = selectedItems.length == completedTasks.length &&
          completedTasks.isNotEmpty;
    });
  }

  void _showCalendarBottomSheet(BuildContext context) {
    // Store selected items count to avoid async gap issues
    final itemsCount = selectedItems.length;

    // Get all valid task start dates
    // We need to find the LATEST start date as the minimum allowed date
    // final rangeStartDates = selectedItems
    //     .map((datum) => datum.rangeStart)
    //     .whereType<DateTime>()
    //     .toList();

    // Get all valid task end dates or expiry dates
    // We need to find the EARLIEST end date as the maximum allowed date
    // final rangeEndDates = selectedItems
    //     .map((datum) => datum.expires ?? datum.rangeEnd)
    //     .whereType<DateTime>()
    //     .toList();

    // Note: Date restrictions are commented out for now
    // Set default dates if no valid dates are found
    // DateTime? minAllowedDate;
    // DateTime? maxAllowedDate;

    // Only apply date restrictions if we have valid dates
    // if (rangeStartDates.isNotEmpty) {
    //   // Find the latest start date (most restrictive minimum)
    //   minAllowedDate = rangeStartDates.reduce((a, b) => a.isAfter(b) ? a : b);
    // }

    // if (rangeEndDates.isNotEmpty) {
    //   // Find the earliest end date (most restrictive maximum)
    //   maxAllowedDate = rangeEndDates.reduce((a, b) => a.isBefore(b) ? a : b);
    // }

    // Get calendar response and cubit before the async gap
    final calendarResponse = (context.read<UnscheduleTaskCubit>().state
            is UnscheduleTaskSuccess)
        ? (context.read<UnscheduleTaskCubit>().state as UnscheduleTaskSuccess)
            .calendarResponse
        : null;

    // Get the cubit before the async gap
    final UnscheduleTaskCubit cubit = context.read<UnscheduleTaskCubit>();

    // Get all scheduled tasks to show grey circles for dates with tasks
    List<DateTime> taskDates = [];
    if (cubit.state is UnscheduleTaskSuccess) {
      var tasksResponse = (cubit.state as UnscheduleTaskSuccess).tasksResponse;
      var allTasks = tasksResponse.addTasks ?? [];

      // Add scheduled tasks to taskDates
      for (var task in allTasks) {
        if (task.scheduledTimeStamp != null &&
            task.taskStatus == "Confirmed" &&
            task.isOpen != true) {
          taskDates.add(task.scheduledTimeStamp!);
        }
      }
    }

    // Define the callback function to handle date selection
    void handleDateSelection(DateTime? selectedDate) async {
      // Check if the widget is still mounted and a date was selected
      if (selectedDate != null && mounted && selectedItems.isNotEmpty) {
        // submission timestamp is current time in timezone "Australia/NSW", locale "en", "AU"
        final submissionTimeStamp = DateTime.now();

        // Show rescheduling message
        SnackBarService.info(
          context: context,
          message:
              'Rescheduling $itemsCount tasks for ${DateFormat('MMM dd, yyyy').format(selectedDate)}',
        );

        // Create submit report requests for each selected task
        List<submit_report.SubmitReportRequestEntity> requests = [];

        for (var task in selectedItems) {
          var request = submit_report.SubmitReportRequestEntity(
            submissionState: 1,
            minutes: task.minutes?.toInt(),
            taskStatus: "Confirmed",
            scheduledTimeStamp: selectedDate,
            submissionTimeStamp: submissionTimeStamp,
            taskId: task.taskId.toString(),
            userId: actualUserId,
            token: actualUserToken,
            appversion: actualAppVersion,
            deviceUid: actualDeviceUid,
            pages: 0,
            startTaskLatitude: 0,
            startTaskLongitude: 0,
            taskLatitude: 0,
            taskLongitude: 0,
            claimableKms: 0,
            budgetCalculated: 0,
            timerMinutes: 0,
            comment: "",
            forms: [],
            followupTasks: [
              submit_report.FollowupTask(
                followupNumber: 0,
                scheduleNote: "",
                followupItemId: 0,
                taskId: task.taskId.toString(),
                followupTypeId: 0,
                visitDate: DateTime(0001, 01, 01, 00, 00, 00),
                budget: 0,
              )
            ],
            resumePauseItems: [],
            taskCommencementTimeStamp: DateTime(0001, 01, 01, 00, 00, 00),
            taskStoppedTimeStamp: DateTime(0001, 01, 01, 00, 00, 00),
          );
          requests.add(request);
        }

        // Call API to submit reports for rescheduling tasks
        await cubit.submitMultipleReports(requests);
        if (mounted) {
          await SyncService().sync(context: context);
        }

        // Clear selection after rescheduling
        setState(() {
          _isCheckboxMode = false;
          selectedItems.clear();
          _areAllItemsSelected = false;
        });
      }
    }

    // Show calendar bottom sheet
    CalendarBottomSheet.show(
      context: context,
      // minDate: minAllowedDate,
      // maxDate: maxAllowedDate,
      calendarResponse: calendarResponse,
      taskDates: taskDates,
    ).then(handleDateSelection);
  }
}
