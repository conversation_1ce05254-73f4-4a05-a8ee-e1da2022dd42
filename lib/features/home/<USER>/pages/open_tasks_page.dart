import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart' hide Form;
import '../../../../config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/services/app_info_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/open_tasks/open_tasks_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/open_tasks/open_tasks_state.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:gap/gap.dart';
import 'package:location/location.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'dart:math';

import '../../../../core/storage/data_manager.dart';
// import '../widgets/open_tasks_app_bar.dart';
import '../widgets/empty_state.dart';
import '../widgets/open_task_card_list.dart';

@RoutePage()
class OpenTasksPage extends StatefulWidget {
  const OpenTasksPage({super.key});

  @override
  State<OpenTasksPage> createState() => _OpenTasksPageState();
}

class _OpenTasksPageState extends State<OpenTasksPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool isWeekView = true;
  late DateTime selectedDate;
  DateTime? selectedCalendarDate;
  List<DateTime> weekDays = [];
  final DateTime _currentMonth = DateTime.now();
  List<DateTime> monthDays = [];

  // Track which button is currently selected
  String _selectedButton =
      'all'; // Options: 'all', 'this_week', 'next_week', 'overdue'

  // Task data
  String actualDeviceUid = '';
  late String actualUserId;
  late String actualAppVersion;
  late String actualUserToken;

  List<TaskDetail> openTasks = [];
  bool _isCheckboxMode = false;
  List<TaskDetail> selectedItems = [];
  bool _areAllItemsSelected = false;

  // Store all tasks from the API response
  List<TaskDetail> allApiTasks = [];

  // Location-related state
  LocationData? _currentLocation;
  bool _locationLoading = true;

  // Date calculations for filtering (matching Android logic)
  late DateTime _thisWeekMonday;
  late DateTime _thisWeekSunday;
  late DateTime _nextWeekMonday;
  late DateTime _nextWeekSunday;
  late DateTime _currentDate;

  // Task counts for each category
  int _allTasksCount = 0;
  int _thisWeekCount = 0;
  int _nextWeekCount = 0;
  int _overdueCount = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);

    // Set initial selected button based on tab
    _selectedButton = _tabController.index == 0
        ? 'all'
        : _tabController.index == 1
            ? 'this_week'
            : _tabController.index == 2
                ? 'next_week'
                : 'overdue';

    // Initialize selectedDate
    selectedDate = DateTime.now();

    // Calculate date ranges for filtering
    _calculateDateRanges();

    // Initialize the week and month days
    _generateWeekDays();
    _generateMonthDays();

    // Initialize data
    _initializeData();

    // Get current location
    _getCurrentLocation();
  }

  void _generateWeekDays() {
    // Get the start of the week (Monday)
    final DateTime now = selectedDate;
    final int day = now.weekday;
    final DateTime firstDayOfWeek = now.subtract(Duration(days: day - 1));

    weekDays = List.generate(7, (index) {
      return firstDayOfWeek.add(Duration(days: index));
    });

    // Check if today is in this week, if so select it
    final today = DateTime.now();
    final bool isTodayInWeek = weekDays.any((day) =>
        day.day == today.day &&
        day.month == today.month &&
        day.year == today.year);

    if (isTodayInWeek) {
      // Select today
      selectedDate = today;
    }
  }

  /// Calculate date ranges matching Android logic
  void _calculateDateRanges() {
    // Get current date and time
    final DateTime now = DateTime.now();
    _currentDate = DateTime(now.year, now.month, now.day);

    // Calculate This Week (Monday to Sunday)
    final int dayOfWeek = now.weekday; // 1 = Monday, 7 = Sunday
    int mondayOffset;

    if (dayOfWeek == 7) {
      // Sunday
      mondayOffset = -6;
    } else {
      mondayOffset = (1 - dayOfWeek); // Monday = 1
    }

    // This week Monday
    _thisWeekMonday = DateTime(now.year, now.month, now.day + mondayOffset);
    _thisWeekMonday = DateTime(
        _thisWeekMonday.year, _thisWeekMonday.month, _thisWeekMonday.day);

    // This week Sunday
    _thisWeekSunday = _thisWeekMonday.add(const Duration(days: 6));
    _thisWeekSunday = DateTime(
        _thisWeekSunday.year, _thisWeekSunday.month, _thisWeekSunday.day);

    // Next week Monday
    _nextWeekMonday = _thisWeekSunday.add(const Duration(days: 1));
    _nextWeekMonday = DateTime(
        _nextWeekMonday.year, _nextWeekMonday.month, _nextWeekMonday.day);

    // Next week Sunday
    _nextWeekSunday = _nextWeekMonday.add(const Duration(days: 6));
    _nextWeekSunday = DateTime(
        _nextWeekSunday.year, _nextWeekSunday.month, _nextWeekSunday.day);
  }

  /// Check if a date is before another date (matching Android isDateBefore logic)
  bool _isDateBefore(DateTime? date1, DateTime? date2) {
    if (date1 == null || date2 == null) return false;
    return date1.isBefore(date2);
  }

  PreferredSizeWidget _buildViewToggleButtons() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(48),
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Tab bar with All, This Week, Next Week, and Overdue buttons
            Expanded(
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    // All button
                    _buildToggleButton(
                      'All Tasks',
                      'all',
                      const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                    // This Week button
                    _buildToggleButton(
                      'This Week',
                      'this_week',
                      BorderRadius.zero,
                    ),
                    // Next Week button
                    _buildToggleButton(
                      'Next Week',
                      'next_week',
                      BorderRadius.zero,
                    ),
                    // Overdue button
                    _buildToggleButton(
                      'Overdue',
                      'overdue',
                      const BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleButton(
    String text,
    String buttonKey,
    BorderRadius borderRadius,
  ) {
    final bool isSelected = _selectedButton == buttonKey;

    return Expanded(
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedButton = buttonKey;
            if (buttonKey == 'all') {
              _tabController.animateTo(0);
            } else if (buttonKey == 'this_week') {
              _tabController.animateTo(1);
            } else if (buttonKey == 'next_week') {
              _tabController.animateTo(2);
            } else if (buttonKey == 'overdue') {
              _tabController.animateTo(3);
            }
          });
        },
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isSelected ? AppColors.primaryBlue : const Color(0xFFF5F5F5),
          foregroundColor: isSelected ? Colors.white : AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
            side: const BorderSide(
              color: Colors.grey,
              width: 0,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          text,
          style: Theme.of(context)
              .textTheme
              .montserratNavigationPrimaryMedium
              .copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
        ),
      ),
    );
  }

  void _generateMonthDays() {
    // Get the first day of the month
    final DateTime firstDayOfMonth =
        DateTime(_currentMonth.year, _currentMonth.month, 1);

    // Get the weekday of the first day (0 = Monday, 6 = Sunday)
    final int firstWeekday = firstDayOfMonth.weekday - 1;

    // Calculate days from previous month to show
    final DateTime firstDayToShow =
        firstDayOfMonth.subtract(Duration(days: firstWeekday));

    // Calculate total days to show (6 weeks = 42 days)
    monthDays = List.generate(42, (index) {
      return firstDayToShow.add(Duration(days: index));
    });
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Update the selected button based on tab index
        if (_tabController.index == 0) {
          _selectedButton = 'all';
          isWeekView = true;
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
        } else if (_tabController.index == 1) {
          _selectedButton = 'this_week';
          isWeekView = true;
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 2) {
          _selectedButton = 'next_week';
          isWeekView = true;
          final DateTime nextWeekDate =
              DateTime.now().add(const Duration(days: 7));
          selectedDate = nextWeekDate;
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 3) {
          _selectedButton = 'overdue';
          isWeekView = false;
        }

        // Update tasks for the selected view (no need to fetch again, just filter existing data)
        _updateOpenTasksForView();
      });
    }
  }

  void _updateOpenTasksForView() {
    // Recalculate date ranges to ensure they're current
    _calculateDateRanges();

    List<TaskDetail> tasksToDisplay = [];
    List<TaskDetail> allTaskCounter = [];
    List<TaskDetail> thisWeekCounter = [];
    List<TaskDetail> nextWeekCounter = [];
    List<TaskDetail> overdueCounter = [];

    // Count tasks for each category (matching Android logic)
    for (TaskDetail task in allApiTasks) {
      // Basic validation
      if (task.isOpen != true ||
          task.taskStatus != "Tentative" ||
          task.taskId == 0) {
        continue;
      }

      DateTime? taskStartDate = task.rangeStart;
      DateTime? taskExpiresDate = task.expires;

      // All tasks
      allTaskCounter.add(task);

      // This week tasks - check if task falls within this week range
      if (taskStartDate != null && taskExpiresDate != null) {
        if (taskStartDate
                .isAfter(_thisWeekMonday.subtract(const Duration(days: 1))) &&
            taskExpiresDate
                .isBefore(_thisWeekSunday.add(const Duration(days: 1)))) {
          thisWeekCounter.add(task);
        }
      }

      // Next week tasks - check if task falls within next week range
      if (taskStartDate != null && taskExpiresDate != null) {
        if (taskStartDate
                .isAfter(_nextWeekMonday.subtract(const Duration(days: 1))) &&
            taskExpiresDate
                .isBefore(_nextWeekSunday.add(const Duration(days: 1)))) {
          nextWeekCounter.add(task);
        }
      }

      // Overdue tasks - check if expires date is before current date
      if (taskExpiresDate != null &&
          _isDateBefore(taskExpiresDate, _currentDate)) {
        overdueCounter.add(task);
      }
    }

    // Update counts
    _allTasksCount = allTaskCounter.length;
    _thisWeekCount = thisWeekCounter.length;
    _nextWeekCount = nextWeekCounter.length;
    _overdueCount = overdueCounter.length;

    // Filter tasks based on selected button
    if (_selectedButton == 'all') {
      tasksToDisplay = allTaskCounter;
    } else if (_selectedButton == 'this_week') {
      tasksToDisplay = thisWeekCounter;
    } else if (_selectedButton == 'next_week') {
      tasksToDisplay = nextWeekCounter;
    } else if (_selectedButton == 'overdue') {
      tasksToDisplay = overdueCounter;
    }

    // Apply 10km radius filtering
    if (_currentLocation != null) {
      tasksToDisplay = tasksToDisplay.where((task) {
        // Check if task has coordinates
        double? taskLat =
            task.taskLatitude?.toDouble() ?? task.latitude?.toDouble();
        double? taskLon =
            task.taskLongitude?.toDouble() ?? task.longitude?.toDouble();

        if (taskLat == null || taskLon == null) {
          return false; // No coordinates = exclude task
        }

        // Calculate distance
        double distance = _calculateDistanceInKm(
          _currentLocation!.latitude!,
          _currentLocation!.longitude!,
          taskLat,
          taskLon,
        );

        return distance <= 10.0; // Only show tasks within 10km
      }).toList();
    } else {
      tasksToDisplay = []; // No location = no tasks
    }

    setState(() {
      openTasks = tasksToDisplay
        ..sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));
    });
  }

  Future<void> _getCurrentLocation() async {
    try {
      final locationService = sl<LocationService>();

      if (await locationService.isLocationAvailable()) {
        _currentLocation = await locationService.getCurrentPosition();
      }

      if (mounted) {
        setState(() {
          _locationLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _locationLoading = false;
        });
      }
    }
  }

  // Calculate distance using Haversine formula
  double _calculateDistanceInKm(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    // Convert degrees to radians
    double lat1Rad = lat1 * (pi / 180);
    double lon1Rad = lon1 * (pi / 180);
    double lat2Rad = lat2 * (pi / 180);
    double lon2Rad = lon2 * (pi / 180);

    // Calculate differences
    double dLat = lat2Rad - lat1Rad;
    double dLon = lon2Rad - lon1Rad;

    // Apply Haversine formula
    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(dLon / 2) * sin(dLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  Future<void> _initializeData() async {
    _refreshData();
  }

  Future<void> _refreshData() async {
    try {
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();
      actualAppVersion = await sl<AppInfoService>().getVersion();

      if (mounted) {
        // Get open available tasks
        context.read<OpenTasksCubit>().getOpenAvailable(
              actualUserToken,
              actualUserId,
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  void _filterOpenTasks(List<TaskDetail> allTasks) {
    allApiTasks = allTasks;
    _updateOpenTasksForView();
  }

  void _handleAcceptTask() async {
    if (selectedItems.isEmpty) {
      SnackBarService.warning(
        context: context,
        message: 'Please select at least one task to accept.',
      );
      return;
    }

    try {
      for (var task in selectedItems) {
        await context.read<OpenTasksCubit>().acceptTask(
              task.taskId.toString(),
              actualUserId,
              actualUserToken,
              actualDeviceUid,
              actualAppVersion,
            );
      }

      // Clear selection after accepting tasks
      if (mounted) {
        setState(() {
          _isCheckboxMode = false;
          selectedItems.clear();
          _areAllItemsSelected = false;
        });

        // Refresh data to show updated list
        await _refreshData();
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to accept tasks: ${e.toString()}',
        );
      }
    }
  }

  void _handleSelectionChanged(
      List<TaskDetail> itemsInList, List<TaskDetail> selectedItemsInList) {
    setState(() {
      final itemsInListIds = itemsInList.map((t) => t.taskId).toSet();
      selectedItems.removeWhere((task) => itemsInListIds.contains(task.taskId));
      selectedItems.addAll(selectedItemsInList);
      _areAllItemsSelected =
          selectedItems.length == openTasks.length && openTasks.isNotEmpty;
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OpenTasksCubit, OpenTasksState>(
      listener: (context, state) {
        if (state is OpenTasksSuccess) {
          setState(() {
            var response = state.response;
            var allTasks = response.addTasks ?? [];
            _filterOpenTasks(allTasks);
          });
        } else if (state is OpenAvailableSuccess) {
          setState(() {
            // Convert OpenAvailableTask to TaskDetail for compatibility
            var allTasks = state.response.tasksAvailable
                .map((openTask) => TaskDetail(
                      taskId: int.tryParse(openTask.taskId) ?? 0,
                      storeName: openTask.storeName,
                      location: openTask
                          .location, // Use location instead of storeAddress
                      latitude: openTask.latitude,
                      longitude: openTask.longitude,
                      taskStatus: "Tentative",
                      isOpen: true,
                      posRequired: false,
                      scheduledTimeStamp:
                          DateTime.tryParse(openTask.scheduledTimeStamp),
                      comment: openTask
                          .taskNote, // Use taskNote instead of description
                      budget: openTask.budget,
                      client: openTask.client,
                      clientId: openTask.clientId,
                      cycle: openTask.cycle,
                      cycleId: openTask.cycleId,
                      expires: DateTime.tryParse(openTask.expires),
                      rangeStart: DateTime.tryParse(openTask.rangeStart),
                      rangeEnd: DateTime.tryParse(openTask.rangeEnd),
                      reOpened: openTask.reOpened,
                      reOpenedReason: openTask.reOpenedReason,
                      disallowReschedule: openTask.disallowReschedule,
                      teamlead: openTask.teamlead,
                      // Add other required fields with default values
                    ))
                .toList();
            _filterOpenTasks(allTasks);
          });
        } else if (state is OpenAcceptedSuccess) {
          setState(() {
            // Convert OpenAcceptedTask to TaskDetail for compatibility
            var allTasks = state.response.tasksAvailable
                .map((openTask) => TaskDetail(
                      taskId: int.tryParse(openTask.taskId) ?? 0,
                      storeName: openTask.storeName,
                      location: openTask
                          .location, // Use location instead of storeAddress
                      latitude: openTask.latitude,
                      longitude: openTask.longitude,
                      taskStatus: "Confirmed",
                      isOpen: false,
                      posRequired: false,
                      scheduledTimeStamp:
                          DateTime.tryParse(openTask.scheduledTimeStamp),
                      comment: openTask
                          .taskNote, // Use taskNote instead of description
                      budget: openTask.budget,
                      client: openTask.client,
                      clientId: openTask.clientId,
                      cycle: openTask.cycle,
                      cycleId: openTask.cycleId,
                      expires: DateTime.tryParse(openTask.expires),
                      rangeStart: DateTime.tryParse(openTask.rangeStart),
                      rangeEnd: DateTime.tryParse(openTask.rangeEnd),
                      reOpened: openTask.reOpened,
                      reOpenedReason: openTask.reOpenedReason,
                      disallowReschedule: openTask.disallowReschedule,
                      teamlead: openTask.teamlead,
                      // Add other required fields with default values
                    ))
                .toList();
            _filterOpenTasks(allTasks);
          });
        } else if (state is OpenTasksError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        } else if (state is AcceptTaskSuccess) {
          SnackBarService.success(
            context: context,
            message: state.message,
          );
          // Refresh data after successful task acceptance
          _refreshData();
        } else if (state is AcceptTaskError) {
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey2,
          appBar: CustomAppBar(
            title: 'Open Tasks',
            bottom: _buildViewToggleButtons(),
            actions: [
              SizedBox(
                width: 80,
                height: 32,
                child: AppButton(
                  text: 'Accept',
                  color: AppColors.primaryBlue,
                  onPressed: _handleAcceptTask,
                  radius: 6,
                  elevation: 0,
                  height: 32,
                ),
              ),
              // const Gap(8),
              // GestureDetector(
              //   onTap: () {
              //     setState(() {
              //       _isCheckboxMode = !_isCheckboxMode;
              //       if (!_isCheckboxMode) {
              //         selectedItems.clear();
              //         _areAllItemsSelected = false;
              //       }
              //     });
              //   },
              //   child: Image.asset(
              //     AppAssets.appbarCalendarEdit,
              //     color:
              //         _isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
              //     scale: 4,
              //   ),
              // ),
              const Gap(16),
            ],
          ),
          body: BlocListener<SyncCubit, SyncState>(
            listener: (context, state) {
              if (state is SyncSuccess) {
                _refreshData();
              }
            },
            child: RefreshIndicator(
              onRefresh: _initializeData,
              child: _buildScrollableContent(context),
            ),
          ),
        );
      },
    );
  }

  // Build the scrollable content that includes task list
  Widget _buildScrollableContent(BuildContext context) {
    final currentState = context.read<OpenTasksCubit>().state;

    if (currentState is OpenTasksLoading || _locationLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_currentLocation == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 64,
              color: Colors.grey,
            ),
            Gap(16),
            Text(
              'Location Required',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            Gap(8),
            Text(
              'Please enable location services to view nearby tasks',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content section based on selected button
          if (_selectedButton == 'all')
            // For "All" tab, show all open tasks
            _buildAllOpenTasksList()
          else if (_selectedButton == 'this_week' ||
              _selectedButton == 'next_week')
            // For week tabs, show open tasks for the selected week
            _buildWeekOpenTasksList()
          else if (_selectedButton == 'overdue')
            // For overdue tab, show overdue open tasks
            _buildOverdueOpenTasksList()
          else
            // Fallback for any other state
            Container(
              color: AppColors.lightGrey2,
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Select a view option',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAllOpenTasksList() {
    if (openTasks.isEmpty) {
      return const EmptyState(message: 'No open tasks available');
    }

    return OpenTaskCardList(
      tasks: openTasks,
      isCheckboxMode: _isCheckboxMode,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
      onTaskTap: (task) {
        // Handle task tap - navigate to task details or accept task
        context.router.push(TaskDetailsRoute(
          taskId: task.taskId!.toInt(),
          storeId: task.storeId ?? 0,
        ));
      },
    );
  }

  Widget _buildWeekOpenTasksList() {
    if (openTasks.isEmpty) {
      return const EmptyState(message: 'No open tasks for this week');
    }

    return OpenTaskCardList(
      tasks: openTasks,
      isCheckboxMode: _isCheckboxMode,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
      onTaskTap: (task) {
        // Handle task tap - navigate to task details or accept task
        context.router.push(TaskDetailsRoute(
          taskId: task.taskId!.toInt(),
          storeId: task.storeId ?? 0,
        ));
      },
    );
  }

  Widget _buildOverdueOpenTasksList() {
    if (openTasks.isEmpty) {
      return const EmptyState(message: 'No overdue open tasks');
    }

    return OpenTaskCardList(
      tasks: openTasks,
      isCheckboxMode: _isCheckboxMode,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
      onTaskTap: (task) {
        // Handle task tap - navigate to task details or accept task
        context.router.push(TaskDetailsRoute(
          taskId: task.taskId!.toInt(),
          storeId: task.storeId ?? 0,
        ));
      },
    );
  }
}
