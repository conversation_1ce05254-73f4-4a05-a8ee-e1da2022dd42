import 'package:flutter/material.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

class AppButton extends StatelessWidget {
  final String text;
  final Color color;
  final VoidCallback onPressed;
  final double? height;
  final Color textColor;
  final double radius;
  final double elevation;

  const AppButton({
    super.key,
    required this.text,
    required this.color,
    required this.onPressed,
    this.height = 50,
    this.textColor = Colors.white,
    this.radius = 10,
    this.elevation = 2,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: height,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          elevation: elevation,
          backgroundColor: color,
          padding: const EdgeInsets.symmetric(vertical: 0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius),
          ),
        ),
        onPressed: onPressed,
        child: Text(
          text,
          style: Theme.of(context).textTheme.montserrat.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                color: textColor,
              ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}
