import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/services/device_info_service.dart';
import 'package:storetrack_app/core/services/firebase_service.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/create_jwt.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/core/services/app_info_service.dart';
import 'package:storetrack_app/features/auth/data/models/auth_request.dart';
import 'package:storetrack_app/features/auth/data/models/auth_response.dart';
import 'package:storetrack_app/features/auth/data/models/login_request.dart';
import 'package:storetrack_app/features/auth/domain/usecases/login_nz_use_case.dart';
import 'package:storetrack_app/features/auth/domain/usecases/login_use_case.dart';

import '../../../../../core/utils/logger.dart';
import '../../../data/models/login_response.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit(
    this._loginNZUseCase,
    this._loginUseCase,
  ) : super(AuthInitial());

  final LoginNZUseCase _loginNZUseCase;
  final LoginUseCase _loginUseCase;

  Future<void> loginNZ({
    required AuthRequest authRequest,
  }) async {
    emit(AuthLoading());
    var authResponse = await _loginNZUseCase.call(authRequest);
    logger("sample-authResponse ${authResponse.data?.toJson().toString()}");
    logger(authResponse.data?.toJson().toString() ?? "haha5");
    if (authResponse.isSuccess) {
      logger("haha2");
      emit(AuthSuccess(authResponse.data!));
    } else {
      logger("haha3 ${authResponse.error}");
      emit(AuthError(authResponse.error!));
    }
  }

  Future<void> loginAzure() async {
    emit(AuthLoading());
    emit(AuthSuccess(AuthResponse()));
  }

  Future<void> login({
    required String email,
  }) async {
    emit(AuthLoading());
    var jwt = createJwt(email);

    final deviceInfoService = sl<DeviceInfoService>();
    final deviceUid = await deviceInfoService.getDeviceId();
    final deviceName = await deviceInfoService.getDeviceName();
    final deviceModel = await deviceInfoService.getDeviceModel();
    final deviceVersion = await deviceInfoService.getDeviceVersion();
    final devicePlatform = deviceInfoService.getDevicePlatform();

    // Get FCM token from Firebase with retry mechanism
    String? fcmToken;

    logger('🔍 [login] Attempting to get FCM token...');
    fcmToken = await _getFCMTokenWithRetry();
    if (fcmToken == null) {
      logger(
          '❌ [login] FCM token not available after retries - proceeding with login anyway');
    } else {
      logger('✅ [login] FCM token obtained successfully');
    }

    final appInfoService = sl<AppInfoService>();
    final appVersion = await appInfoService.getVersion();

    var loginRequest = LoginRequest(
      azureJwt: jwt,
      deviceToken: fcmToken,
      devicePlatform: devicePlatform,
      appversion: appVersion,
      deviceuid: deviceUid,
      devicename: deviceName,
      devicemodel: deviceModel,
      deviceversion: deviceVersion,
    );
    var loginResponse = await _loginUseCase.call(loginRequest);
    logger("sample-authResponse ${loginResponse.data?.toJson().toString()}");

    if (loginResponse.isSuccess) {
      sl<DataManager>().saveLoginResponse(loginResponse.data!);
      sl<DataManager>().saveAuthToken(jwt);
      sl<DataManager>().saveEmail(email);
      emit(LoginSuccess(loginResponse.data!));
    } else {
      emit(AuthError(loginResponse.error!));
    }
  }

  /// Attempts to get FCM token with retry mechanism and timing checks
  Future<String?> _getFCMTokenWithRetry() async {
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 2);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      logger('🔄 [_getFCMTokenWithRetry] Attempt $attempt/$maxRetries');

      try {
        final stopwatch = Stopwatch()..start();
        final token = await FirebaseService.getToken();
        stopwatch.stop();

        logger(
            '⏱️ [_getFCMTokenWithRetry] Token request took ${stopwatch.elapsedMilliseconds}ms');

        if (token != null) {
          logger(
              '✅ [_getFCMTokenWithRetry] Token obtained on attempt $attempt');
          return token;
        } else {
          logger(
              '❌ [_getFCMTokenWithRetry] Token was null on attempt $attempt');
        }
      } catch (e) {
        logger('❌ [_getFCMTokenWithRetry] Error on attempt $attempt: $e');
      }

      if (attempt < maxRetries) {
        logger(
            '⏳ [_getFCMTokenWithRetry] Waiting ${retryDelay.inSeconds}s before retry...');
        await Future.delayed(retryDelay);
      }
    }

    logger(
        '❌ [_getFCMTokenWithRetry] Failed to get token after $maxRetries attempts');
    return null;
  }
}
