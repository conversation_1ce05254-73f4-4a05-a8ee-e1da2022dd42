# MSAL Configuration Guide

## Overview

This document provides step-by-step instructions for configuring Microsoft Authentication Library (MSAL) in the StoreTrack Flutter application. MSAL enables secure authentication with Azure Active Directory for both Android and iOS platforms.

## Prerequisites

- Azure Active Directory app registration configured
- Client ID from Azure portal
- Redirect URI configured in Azure portal

## Android Configuration

### 1. Update MSAL Configuration File

**File**: `assets/jsons/msal_config.json`

- Change the `redirect_uri` to match the one configured in `AndroidManifest.xml`

### 2. Update Android Manifest

**File**: `android/app/src/main/AndroidManifest.xml`

- Change the `host` attribute to match the one in `msal_config.json`

### 3. Update MSAL Auth Service

**File**: `lib/core/services/msal_auth_service.dart`

- Update `androidRedirectUri` to match the one in `AndroidManifest.xml`
- Update `clientIdAndroid` to match the client ID from Azure portal

## iOS Configuration

### 1. Update Info.plist

**File**: `ios/Runner/Info.plist`

- Change the `CFBundleURLSchemes` to match the redirect URI scheme configured in Azure portal

## Important Notes

- Ensure all redirect URIs are consistent across configuration files
- Client IDs must match exactly with your Azure portal app registration
- Test authentication flow after making configuration changes
