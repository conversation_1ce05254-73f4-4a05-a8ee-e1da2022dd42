# Test Plan for Multiple PhotoUploadWidget Enhancement

## Overview
This document outlines the testing approach for the enhanced measurement widgets that now support multiple PhotoUploadWidget instances based on phototags.

## Changes Made

### 1. Updated Measurement Widget Interfaces
All measurement widgets now accept:
- `List<PhotoTagsT>? photoTags` - List of photo tags for the measurement
- `Map<int, List<String>>? photosByTag` - Photos grouped by photo tag ID

### 2. Enhanced Widgets
- ✅ TextFieldWidget
- ✅ DropdownWidget  
- ✅ CheckboxWidget
- ✅ CounterWidget
- ✅ DatePickerWidget
- ✅ RadioButtonWidget
- ✅ MultiSelectWidget

### 3. Updated QPMD Page
- Enhanced photo loading to group photos by photo tag ID
- Updated measurement widget instantiation to pass multiple photo tags
- Added helper methods for photo management

## Testing Scenarios

### Scenario 1: Single Photo Tag (Backward Compatibility)
**Expected Behavior:** When a measurement has only one photo tag, the widget should display a single PhotoUploadWidget (same as before).

**Test Steps:**
1. Navigate to a measurement with a single photo tag
2. Verify only one PhotoUploadWidget is displayed
3. Verify photo upload functionality works correctly
4. Verify compression settings are applied based on PhotoResPerc
5. Verify HI-RES indicator appears when image_rec is true

### Scenario 2: Multiple Photo Tags
**Expected Behavior:** When a measurement has multiple photo tags, the widget should display multiple PhotoUploadWidget instances, one for each tag.

**Test Steps:**
1. Navigate to a measurement with multiple photo tags
2. Verify multiple PhotoUploadWidget instances are displayed
3. Verify each widget shows the correct photos for its photo tag
4. Verify gaps are properly added between widgets
5. Verify each widget has correct compression settings based on its photo tag

### Scenario 3: No Photo Tags
**Expected Behavior:** When a measurement has no photo tags but showCameraIcon is true, no PhotoUploadWidget should be displayed.

**Test Steps:**
1. Navigate to a measurement with no photo tags
2. Verify no PhotoUploadWidget is displayed
3. Verify the measurement widget functions normally otherwise

### Scenario 4: Photo Filtering
**Expected Behavior:** Photos should be correctly filtered and grouped by photo tag ID.

**Test Steps:**
1. Add photos to different photo tags for the same measurement
2. Verify each PhotoUploadWidget shows only photos for its specific tag
3. Verify photos are filtered by level (combinetypeid = 3)
4. Verify photos are filtered by question_id, measurement_id, and questionpart_id

## Key Implementation Details

### Photo Loading Logic
```dart
// Photos are now grouped by measurement ID and photo tag ID
final photoTagId = photo.photoTagId?.toInt() ?? 0;
if (!_measurementPhotosByTag[measurementId]!.containsKey(photoTagId)) {
  _measurementPhotosByTag[measurementId]![photoTagId] = [];
}
_measurementPhotosByTag[measurementId]![photoTagId]!.add(imageUrl);
```

### Widget Creation Pattern
```dart
// Use multiple photo tags if available, otherwise fall back to single photo tag
if (photoTags != null && photoTags!.isNotEmpty)
  ..._buildPhotoUploadWidgets()
else if (photoTag != null)
  PhotoUploadWidget(/* single widget */)
```

### Helper Methods
- `_getPhotosForTag(int photoTagId)` - Get photos for specific tag
- `_buildPhotoUploadWidgets()` - Build multiple PhotoUploadWidget instances
- `_getPhotoTagsForMeasurement(Measurement)` - Get all photo tags for measurement
- `_getPhotosForMeasurementByTag(num)` - Get photos grouped by tag

## Expected Benefits

1. **Multiple Photo Categories:** Users can now upload photos to different categories/tags for the same measurement
2. **Better Organization:** Photos are organized by their specific purpose/tag
3. **Backward Compatibility:** Existing single photo tag functionality continues to work
4. **Consistent UI:** Each PhotoUploadWidget maintains the same behavior and styling
5. **Proper Compression:** Each photo tag can have different compression settings

## Verification Checklist

- [ ] All measurement widgets compile without errors
- [ ] Single photo tag measurements work as before
- [ ] Multiple photo tag measurements display multiple widgets
- [ ] Photos are correctly filtered by tag ID
- [ ] Photo compression settings are applied per tag
- [ ] HI-RES indicators work correctly per tag
- [ ] Photo upload and navigation to MPTPage works
- [ ] Photo validation works for each tag separately
- [ ] UI spacing and layout look correct with multiple widgets
